#!/usr/bin/env python3
"""
程式碼區間詳細檢測器
專門負責連續整數區間的完整掃描和詳細日誌記錄
"""

import os
import csv
from datetime import datetime
from typing import List, Dict, Any, Tuple
from ..ft_eqc_grouping_processor import FileFilterConfig

class CodeRegionDetailedDetector:
    """程式碼區間詳細檢測器 - 提供完整的連續整數區間掃描"""
    
    def __init__(self, folder_path: str):
        print("🔍 [code_region_detailed_detector.py] __init__() - 開始執行")
        self.folder_path = folder_path
        self.min_start_column = 10  # 從第10欄開始
        self.filter_config = FileFilterConfig()  # 整合環境變數排除機制
        self.log_entries = []
        
    def detect_and_log_all_regions(self) -> None:
        """檢測並記錄所有連續整數區間"""
        print("🔍 [code_region_detailed_detector.py] detect_and_log_all_regions() - 開始執行")
        try:
            # 1. 找到 Golden EQC 檔案
            golden_file = self._find_golden_eqc_file()
            if not golden_file:
                self._log("❌ 未找到有效的 Golden EQC 檔案")
                return
                
            # 2. 掃描所有連續整數區間
            all_regions = self._scan_all_consecutive_regions(golden_file)
            
            # 3. 記錄詳細結果
            self._log_detailed_results(all_regions, golden_file)
            
            # 4. 寫入日誌檔案
            self._write_debug_log()
            
        except Exception as e:
            self._log(f"❌ 詳細區間檢測失敗: {e}")
            
    def _find_golden_eqc_file(self) -> str:
        """找到第一個有效的 Golden EQC 檔案（遞歸搜尋子目錄）- 與 EQCSimpleDetector 邏輯一致"""
        print("🔍 [code_region_detailed_detector.py] _find_golden_eqc_file() - 開始執行")
        try:
            # 掃描所有 CSV 檔案（使用環境變數排除機制）
            csv_files = []
            for root, dirs, files in os.walk(self.folder_path):
                # 檢查是否應排除整個資料夾
                if self.filter_config.should_exclude_folder(root):
                    continue
                    
                for file in files:
                    if file.lower().endswith('.csv'):
                        file_path = os.path.join(root, file)
                        # 使用統一的檔案排除機制（替換硬編碼）
                        if self.filter_config.should_exclude_file(file_path):
                            continue
                        csv_files.append(file_path)
            
            print(f"   🔍 掃描到 {len(csv_files)} 個候選 CSV 檔案")
            
            # 檢查每個檔案是否為 Golden EQC
            for csv_file in csv_files:
                if self._is_eqc_bin1_file(csv_file) and self._has_enough_bin1_rows(csv_file):
                    print(f"   ✅ 找到 Golden EQC: {os.path.basename(csv_file)}")
                    return csv_file
        except Exception as e:
            print(f"   ❌ 搜尋 Golden EQC 檔案失敗: {e}")
        return None
        
    def _is_eqc_bin1_file(self, file_path: str) -> bool:
        """檢查是否為 EQC BIN1 檔案 - 與 EQCSimpleDetector 邏輯一致"""
        try:
            filename_lower = file_path.lower()
            # 檢查檔案名稱是否包含 EQC 相關關鍵字
            if 'eqcfaildata' in filename_lower or 'onlineeqc' in filename_lower:
                return True
                
            # 檢查檔案內容
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            return '(qc)' in content.lower() or 'onlineeqc' in content.lower()
            
        except Exception:
            return False
        
    def _has_enough_bin1_rows(self, file_path: str) -> bool:
        """檢查檔案是否有足夠的 BIN=1 行 - 與 EQCSimpleDetector 邏輯一致"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
                
            bin1_count = 0
            data_start_row = 12  # 從第13行開始（索引12）
            
            for i in range(data_start_row, len(lines)):
                try:
                    elements = lines[i].split(',')
                    # 檢查第2欄 (elements[1]) 是否為 BIN=1
                    if len(elements) > 1 and elements[1].strip() == '1':
                        bin1_count += 1
                        if bin1_count >= 2:
                            return True
                except (IndexError, ValueError):
                    continue
            
            return False
            
        except Exception as e:
            # 調試：記錄檢查失敗的原因
            print(f"   ⚠️ 檢查 BIN=1 行失敗: {e}")
            return False
        
    def _scan_all_consecutive_regions(self, csv_file: str) -> List[Dict]:
        """掃描所有連續整數區間"""
        print("🔍 [code_region_detailed_detector.py] _scan_all_consecutive_regions() - 開始執行")
        all_regions = []
        
        try:
            # 讀取第一個 BIN=1 行的數據
            with open(csv_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
                
            # 找到第一個 BIN=1 行
            values = None
            for i in range(12, len(lines)):
                elements = lines[i].split(',')
                if len(elements) > 1 and elements[1].strip() == '1':
                    values = elements
                    break
                    
            if not values:
                return []
                
            # 掃描連續整數區間
            current_count = 0
            current_start = -1
            
            for i in range(self.min_start_column, len(values)):
                try:
                    if self._is_numeric(values[i]):
                        value_stripped = values[i].strip().lower()
                        
                        # 檢查是否為整數或 "none"
                        is_integer = False
                        if value_stripped == 'none':
                            is_integer = True  # "none" 視為整數
                        else:
                            try:
                                temp_val = float(value_stripped)
                                is_integer = (temp_val == int(temp_val))  # 是整數
                            except:
                                is_integer = False
                        
                        if is_integer:
                            if current_count == 0:
                                current_start = i
                            current_count += 1
                        else:
                            if current_count > 0:
                                all_regions.append({
                                    'start': current_start,
                                    'end': i - 1,
                                    'count': current_count,
                                    'start_column': current_start + 1,
                                    'end_column': i,
                                })
                            current_count = 0
                    else:
                        if current_count > 0:
                            all_regions.append({
                                'start': current_start,
                                'end': i - 1,
                                'count': current_count,
                                'start_column': current_start + 1,
                                'end_column': i,
                            })
                        current_count = 0
                except:
                    if current_count > 0:
                        all_regions.append({
                            'start': current_start,
                            'end': i - 1,
                            'count': current_count,
                            'start_column': current_start + 1,
                            'end_column': i,
                        })
                    current_count = 0
                    
            # 處理最後一個區間
            if current_count > 0:
                all_regions.append({
                    'start': current_start,
                    'end': len(values) - 1,
                    'count': current_count,
                    'start_column': current_start + 1,
                    'end_column': len(values),
                })
                
        except Exception as e:
            self._log(f"掃描過程中發生錯誤: {e}")
            
        return all_regions
        
    def _is_numeric(self, value: str) -> bool:
        """檢查是否為數值（包含 'none' 作為有效數字）"""
        value_stripped = value.strip().lower()
        
        # 將 "none" 視為有效數字（代表缺失值的數字佔位符）
        if value_stripped == 'none':
            return True
            
        try:
            float(value_stripped)
            return True
        except:
            return False
            
    def _log_detailed_results(self, all_regions: List[Dict], golden_file: str):
        """記錄詳細掃描結果"""
        self._log("🔍 連續整數區間完整掃描結果")
        self._log("=" * 60)
        self._log(f"📊 檢測檔案: {os.path.basename(golden_file)}")
        self._log(f"📊 掃描範圍: 第{self.min_start_column+1}欄開始")
        self._log("")
        
        if not all_regions:
            self._log("❌ 未找到任何連續整數區間")
            return
            
        self._log("🎯 發現的連續整數區間:")
        for i, region in enumerate(all_regions, 1):
            # 根據區間大小顯示不同狀態
            if region['count'] >= 100:
                status = "✅"
                note = " (大型區間)"
            elif region['count'] >= 20:
                status = "🔵"
                note = " (中型區間)"
            elif region['count'] >= 5:
                status = "🟡"
                note = " (小型區間)"
            else:
                status = "⚪"
                note = " (微型區間)"
            
            self._log(f"   區間{i}: 第{region['start_column']}-{region['end_column']}欄 ({region['count']}個連續整數) {status}{note}")
            
        # 找到最長區間
        max_region = max(all_regions, key=lambda x: x['count'])
        self._log("")
        self._log(f"✅ 採用最長區間: 第{max_region['start_column']}-{max_region['end_column']}欄 ({max_region['count']}個連續整數)")
        
        # 驗證條件（移除230閾值）
        start_check = max_region['start'] > 10
        length_check = max_region['count'] > 5
        
        self._log(f"   驗證條件: start > 10 {'✓' if start_check else '✗'}, length > 5 {'✓' if length_check else '✗'}")
        self._log(f"   總計發現: {len(all_regions)} 個連續整數區間")
        
    def _log(self, message: str):
        """添加日誌條目"""
        timestamp = datetime.now().strftime('%H:%M:%S.%f')[:-3]
        self.log_entries.append(f"[{timestamp}] {message}")
        print(f"   {message}")  # 同時輸出到控制台
        
    def _write_debug_log(self):
        """寫入詳細日誌檔案"""
        try:
            log_filename = f"EQCTOTALDATA_CodeRegion_DEBUG_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
            log_path = os.path.join(self.folder_path, log_filename)
            
            with open(log_path, 'w', encoding='utf-8') as f:
                f.write("程式碼區間詳細檢測 DEBUG LOG\n")
                f.write(f"開始時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write("=" * 60 + "\n\n")
                
                for entry in self.log_entries:
                    f.write(entry + "\n")
                    
                f.write("\n" + "=" * 60 + "\n")
                f.write("詳細檢測完成\n")
                
        except Exception as e:
            print(f"   ⚠️ 無法寫入詳細日誌檔案: {e}")
    
    def get_max_consecutive_region(self) -> tuple:
        """獲取最長連續整數區間的起始和結束位置（索引）"""
        print("🔍 [code_region_detailed_detector.py] get_max_consecutive_region() - 開始執行")
        try:
            # 找到 Golden EQC 檔案
            golden_file = self._find_golden_eqc_file()
            if not golden_file:
                return (-1, -1)
                
            # 掃描所有連續整數區間
            all_regions = self._scan_all_consecutive_regions(golden_file)
            if not all_regions:
                return (-1, -1)
                
            # 找到最長區間
            max_region = max(all_regions, key=lambda x: x['count'])
            return (max_region['start'], max_region['end'])
            
        except Exception as e:
            print(f"   ⚠️ 獲取最長區間失敗: {e}")
            return (-1, -1)