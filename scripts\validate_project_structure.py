#!/usr/bin/env python3
"""
專案結構驗證腳本
遵循 CLAUDE.md 的程式測試要求 - 實際執行驗證功能
"""

import sys
from pathlib import Path

# 添加專案根目錄到 Python 路徑
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.infrastructure.config.settings import Settings
from src.domain.exceptions.base import (
    OutlookSummaryException,
    EmailProcessingException,
    ValidationException,
)


def test_config_system() -> bool:
    """測試配置系統是否正常運作"""
    print("🔧 測試配置系統...")

    try:
        # 測試預設配置
        settings = Settings()
        print(f"  ✅ 應用程式名稱: {settings.app_name}")
        print(f"  ✅ 日誌級別: {settings.log_level}")
        print(f"  ✅ 除錯模式: {settings.debug}")

        # 測試資料庫配置
        db_config = settings.database
        print(f"  ✅ 資料庫驅動: {db_config.driver}")
        print(f"  ✅ 連線字串: {db_config.connection_string}")

        # 測試郵件配置
        email_config = settings.email
        print(f"  ✅ 郵件監控: {email_config.monitor_enabled}")
        print(f"  ✅ 檢查間隔: {email_config.check_interval}秒")

        # 測試從字典建立配置
        config_data = {
            "app_name": "測試中文應用程式",
            "debug": True,
            "database": {"driver": "postgresql", "host": "測試主機", "database_name": "測試資料庫"},
        }

        settings_from_dict = Settings.from_dict(config_data)
        print(f"  ✅ 從字典建立配置: {settings_from_dict.app_name}")
        print(f"  ✅ 資料庫主機: {settings_from_dict.database.host}")

        # 測試配置轉字典
        config_dict = settings.to_dict()
        print(f"  ✅ 配置轉字典成功，包含 {len(config_dict)} 個項目")

        return True

    except Exception as e:
        print(f"  ❌ 配置系統測試失敗: {e}")
        return False


def test_exception_system() -> bool:
    """測試例外處理系統是否正常運作"""
    print("\n🚨 測試例外處理系統...")

    try:
        # 測試基礎例外
        base_exception = OutlookSummaryException(
            message="測試基礎例外", error_code="TEST_001", details={"test_field": "測試值"}
        )
        print(f"  ✅ 基礎例外: {base_exception}")

        # 測試例外字典轉換
        exc_dict = base_exception.to_dict()
        print(f"  ✅ 例外轉字典: {exc_dict}")

        # 測試郵件處理例外
        email_exception = EmailProcessingException(
            message="測試郵件處理失敗",
            email_id="test_email_123",
            vendor="GTK",
            processing_step="parsing",
        )
        print(f"  ✅ 郵件處理例外: {email_exception}")

        # 測試驗證例外
        validation_exception = ValidationException(
            message="測試驗證失敗", field_name="email", field_value="invalid@email"
        )
        print(f"  ✅ 驗證例外: {validation_exception}")

        # 測試例外鏈接
        try:
            original_error = ValueError("原始錯誤")
            raise EmailProcessingException(
                message="處理郵件時發生錯誤", email_id="test_email", vendor="GTK"
            ) from original_error
        except EmailProcessingException as e:
            print(f"  ✅ 例外鏈接: {e.__cause__}")

        return True

    except Exception as e:
        print(f"  ❌ 例外處理系統測試失敗: {e}")
        return False


def test_project_structure() -> bool:
    """測試專案結構是否完整"""
    print("\n📁 測試專案結構...")

    try:
        required_dirs = [
            "src/domain/entities",
            "src/domain/value_objects",
            "src/domain/services",
            "src/domain/exceptions",
            "src/application/use_cases",
            "src/application/interfaces",
            "src/infrastructure/adapters",
            "src/infrastructure/parsers",
            "src/infrastructure/config",
            "src/presentation/api",
            "src/presentation/cli",
            "src/presentation/web",
            "tests/unit",
            "tests/integration",
            "tests/e2e",
            "database/migrations",
            "analytics/notebooks",
            "monitoring/prometheus",
        ]

        missing_dirs = []
        for dir_path in required_dirs:
            full_path = project_root / dir_path
            if not full_path.exists():
                missing_dirs.append(dir_path)
            else:
                print(f"  ✅ {dir_path}")

        if missing_dirs:
            print(f"  ❌ 缺少目錄: {missing_dirs}")
            return False

        print(f"  ✅ 所有 {len(required_dirs)} 個必要目錄都存在")
        return True

    except Exception as e:
        print(f"  ❌ 專案結構測試失敗: {e}")
        return False


def test_chinese_support() -> bool:
    """測試中文支援是否正常"""
    print("\n🇹🇼 測試中文支援...")

    try:
        # 測試中文配置
        chinese_config = Settings(app_name="繁體中文郵件處理系統", log_level="DEBUG")
        print(f"  ✅ 中文應用程式名稱: {chinese_config.app_name}")

        # 測試中文例外訊息
        chinese_exception = EmailProcessingException(
            message="郵件處理過程中發生錯誤，請檢查郵件格式",
            email_id="電子郵件_123",
            vendor="GTK科技",
            processing_step="解析階段",
        )
        print(f"  ✅ 中文例外訊息: {chinese_exception}")

        # 測試中文字典轉換
        chinese_dict = chinese_exception.to_dict()
        print(f"  ✅ 中文字典轉換: {chinese_dict['message']}")

        return True

    except Exception as e:
        print(f"  ❌ 中文支援測試失敗: {e}")
        return False


def main() -> None:
    """主要測試函式"""
    print("🚀 開始專案結構程式測試驗證...")
    print(f"📂 專案根目錄: {project_root}")

    # 執行所有測試
    test_results = {
        "專案結構": test_project_structure(),
        "配置系統": test_config_system(),
        "例外處理": test_exception_system(),
        "中文支援": test_chinese_support(),
    }

    # 統計結果
    print("\n" + "=" * 60)
    print("📊 測試結果摘要:")

    passed_count = 0
    for test_name, result in test_results.items():
        status = "✅ 通過" if result else "❌ 失敗"
        print(f"  {test_name}: {status}")
        if result:
            passed_count += 1

    total_tests = len(test_results)
    success_rate = (passed_count / total_tests) * 100

    print(f"\n🎯 測試統計: {passed_count}/{total_tests} 通過 ({success_rate:.1f}%)")

    if success_rate == 100:
        print("🎉 所有測試通過！專案結構初始化成功！")
        print("📝 TASK_001: 專案結構初始化 - 完成")
        return_code = 0
    else:
        print("⚠️  部分測試失敗，請檢查錯誤訊息")
        return_code = 1

    print("=" * 60)
    sys.exit(return_code)


if __name__ == "__main__":
    main()
