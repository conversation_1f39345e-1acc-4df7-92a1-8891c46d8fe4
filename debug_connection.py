#!/usr/bin/env python3
"""
簡單的連接調試腳本
逐步測試不同的連接方式
"""

import poplib
import smtplib
import socket
import ssl
from dotenv import load_dotenv
import os

def test_basic_connection():
    """測試基本網路連接"""
    print("=== 基本連接測試 ===")
    
    server = "hcmail.gmt.com.tw"
    
    # 測試 POP3 端口
    print(f"🔗 測試 POP3 端口 1100...")
    try:
        sock = socket.create_connection((server, 1100), timeout=10)
        sock.close()
        print("✅ POP3 端口 1100 可連接")
    except Exception as e:
        print(f"❌ POP3 端口 1100 連接失敗: {e}")
    
    # 測試 SMTP 端口
    print(f"🔗 測試 SMTP 端口 2500...")
    try:
        sock = socket.create_connection((server, 2500), timeout=10)
        sock.close()
        print("✅ SMTP 端口 2500 可連接")
    except Exception as e:
        print(f"❌ SMTP 端口 2500 連接失敗: {e}")
    
    print()

def test_pop3_protocols():
    """測試不同的 POP3 協議"""
    print("=== POP3 協議測試 ===")
    
    server = "hcmail.gmt.com.tw"
    port = 1100
    
    # 測試普通 POP3
    print("🔗 測試普通 POP3...")
    try:
        mail = poplib.POP3(server, port, timeout=10)
        response = mail.getwelcome()
        print(f"✅ 普通 POP3 連接成功: {response}")
        mail.quit()
    except Exception as e:
        print(f"❌ 普通 POP3 連接失敗: {e}")
    
    # 測試 SSL POP3
    print("🔗 測試 SSL POP3...")
    try:
        mail = poplib.POP3_SSL(server, port, timeout=10)
        response = mail.getwelcome()
        print(f"✅ SSL POP3 連接成功: {response}")
        mail.quit()
    except Exception as e:
        print(f"❌ SSL POP3 連接失敗: {e}")
    
    print()

def test_smtp_protocols():
    """測試不同的 SMTP 協議"""
    print("=== SMTP 協議測試 ===")
    
    server = "hcmail.gmt.com.tw"
    port = 2500
    
    # 測試普通 SMTP
    print("🔗 測試普通 SMTP...")
    try:
        smtp = smtplib.SMTP(server, port, timeout=10)
        response = smtp.noop()
        print(f"✅ 普通 SMTP 連接成功: {response}")
        smtp.quit()
    except Exception as e:
        print(f"❌ 普通 SMTP 連接失敗: {e}")
    
    # 測試 SSL SMTP
    print("🔗 測試 SSL SMTP...")
    try:
        smtp = smtplib.SMTP_SSL(server, port, timeout=10)
        response = smtp.noop()
        print(f"✅ SSL SMTP 連接成功: {response}")
        smtp.quit()
    except Exception as e:
        print(f"❌ SSL SMTP 連接失敗: {e}")
    
    print()

def test_auth_formats():
    """測試不同的認證格式"""
    print("=== 認證格式測試 ===")
    
    load_dotenv()
    base_username = os.getenv('EMAIL_ADDRESS', 'telowyield1')
    password = os.getenv('EMAIL_PASSWORD')
    
    if not password:
        print("❌ 未設定密碼，跳過認證測試")
        return
    
    server = "hcmail.gmt.com.tw"
    
    # 測試不同的使用者名稱格式
    username_formats = [
        base_username,  # telowyield1
        f"{base_username}@gmt.com.tw",  # <EMAIL>
        f"{base_username}@hcmail.gmt.com.tw",  # <EMAIL>
    ]
    
    for username in username_formats:
        print(f"🔐 測試認證格式: {username}")
        
        # 測試 POP3 認證
        try:
            mail = poplib.POP3(server, 1100, timeout=10)
            mail.user(username)
            mail.pass_(password)
            print(f"   ✅ POP3 認證成功")
            mail.quit()
            break  # 成功就跳出
        except poplib.error_proto as e:
            print(f"   ❌ POP3 認證失敗: {e}")
        except Exception as e:
            print(f"   ❌ POP3 連接錯誤: {e}")
    
    print()

def main():
    """主函數"""
    print("=== 郵件伺服器連接診斷 ===")
    print("目標伺服器: hcmail.gmt.com.tw")
    print()
    
    test_basic_connection()
    test_pop3_protocols()
    test_smtp_protocols()
    test_auth_formats()
    
    print("=== 診斷完成 ===")

if __name__ == "__main__":
    main()