#!/usr/bin/env python3
"""
啟動 FT Summary 服務
包含 API 和 Web 介面
"""
import os
import sys
import subprocess
import time

def check_requirements():
    """檢查系統需求"""
    print("🔍 檢查系統需求...")
    
    # 檢查 Python 版本
    python_version = sys.version_info
    print(f"Python 版本: {python_version.major}.{python_version.minor}.{python_version.micro}")
    
    if python_version < (3, 8):
        print("❌ 需要 Python 3.8 或更高版本")
        return False
    
    # 檢查虛擬環境
    venv_path = "venv"
    if os.path.exists(venv_path):
        print(f"✅ 找到虛擬環境: {venv_path}")
    else:
        print("⚠️ 未找到虛擬環境，將使用系統 Python")
    
    # 檢查關鍵檔案
    key_files = [
        "batch_csv_to_excel_processor.py",
        "src/presentation/api/ft_eqc_api.py",
        "src/presentation/api/models.py",
        "src/presentation/web/templates/ft_summary_ui.html",
        "src/presentation/web/static/js/ft-summary-processor.js"
    ]
    
    for file_path in key_files:
        if os.path.exists(file_path):
            print(f"✅ 關鍵檔案存在: {file_path}")
        else:
            print(f"❌ 關鍵檔案缺失: {file_path}")
            return False
    
    return True

def start_service():
    """啟動 FT Summary 服務"""
    print("\n🚀 啟動 FT Summary 服務...")
    
    try:
        # 檢查是否在虛擬環境中
        if 'VIRTUAL_ENV' in os.environ:
            python_cmd = 'python'
            print(f"✅ 使用虛擬環境: {os.environ['VIRTUAL_ENV']}")
        else:
            python_cmd = 'python3'
            print("⚠️ 未檢測到虛擬環境，使用系統 Python")
        
        # 啟動 FastAPI 服務
        cmd = [
            python_cmd, "-m", "uvicorn",
            "src.presentation.api.ft_eqc_api:app",
            "--host", "127.0.0.1",
            "--port", "8010",
            "--reload",
            "--log-level", "info"
        ]
        
        print(f"執行命令: {' '.join(cmd)}")
        print("\n" + "=" * 60)
        print("🌐 FT Summary 服務已啟動")
        print("📊 API 文檔: http://127.0.0.1:8010/docs")
        print("🖥️ FT Summary UI: http://127.0.0.1:8010/ft-summary-ui")
        print("🔧 EQC 系統 UI: http://127.0.0.1:8010/ui")
        print("❤️ 健康檢查: http://127.0.0.1:8010/health")
        print("=" * 60)
        print("按 Ctrl+C 停止服務")
        print()
        
        # 啟動服務
        subprocess.run(cmd)
        
    except KeyboardInterrupt:
        print("\n⏹️ 服務已停止")
    except Exception as e:
        print(f"❌ 啟動失敗: {e}")
        return False
    
    return True

def show_usage():
    """顯示使用說明"""
    print("📋 FT Summary 服務使用說明")
    print("=" * 60)
    print("1. API 端點:")
    print("   POST /api/process_ft_summary")
    print("   參數: folder_path, force_overwrite")
    print()
    print("2. Web 介面:")
    print("   GET /ft-summary-ui")
    print("   提供圖形化介面進行批量處理")
    print()
    print("3. 功能:")
    print("   - 掃描指定資料夾中的 CSV 檔案")
    print("   - 自動識別 FT 類型檔案")
    print("   - 生成個別 Summary 檔案")
    print("   - 整併為最終 FT_SUMMARY.csv")
    print()
    print("4. 測試資料夾:")
    print("   doc/20250523/Production Data")
    print("=" * 60)

if __name__ == "__main__":
    print("🎯 FT Summary 服務啟動器")
    print("遵循 CLAUDE.md 規範")
    print()
    
    # 檢查參數
    if len(sys.argv) > 1 and sys.argv[1] == "--help":
        show_usage()
        sys.exit(0)
    
    # 檢查系統需求
    if not check_requirements():
        print("\n❌ 系統需求檢查失敗")
        sys.exit(1)
    
    # 顯示使用說明
    show_usage()
    
    # 等待用戶確認
    try:
        input("\n按 Enter 繼續啟動服務，或 Ctrl+C 取消...")
    except KeyboardInterrupt:
        print("\n取消啟動")
        sys.exit(0)
    
    # 啟動服務
    start_service()