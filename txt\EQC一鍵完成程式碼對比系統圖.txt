EQC 一鍵完成程式碼對比系統架構圖
=====================================

📋 系統概述
-----------
功能名稱: processCompleteEQCWorkflow()
API端點: /api/process_online_eqc
處理流程: 自動生成EQCTOTALDATA → 程式碼區間檢測 → 雙重搜尋 → 完整報告

🏗️ 系統架構層次
===============

┌─────────────────────────────────────────────────────────────────┐
│                        前端 UI 層                                │
├─────────────────────────────────────────────────────────────────┤
│ 檔案: ft_eqc_grouping_ui.html                                   │
│ 主要函式:                                                        │
│ • processCompleteEQCWorkflow()  - 一鍵完成處理觸發函式           │
│ • showProcessing()              - 處理狀態顯示                   │
│ • DOMManager.get()              - DOM 元素管理                   │
└─────────────────────────────────────────────────────────────────┘
                                    ↓
┌─────────────────────────────────────────────────────────────────┐
│                        API 接口層                               │
├─────────────────────────────────────────────────────────────────┤
│ 檔案: src/presentation/api/ft_eqc_api.py                        │
│ 主要端點與函式:                                                  │
│ • /api/process_online_eqc       - 完整 Online EQC 處理端點      │
│ • /api/process_eqc_advanced     - 兩階段 EQC 處理端點           │
│ • process_folder_path()         - 路徑處理與轉換                │
│ • OnlineEQCProcessResponse      - 回應資料模型                  │
└─────────────────────────────────────────────────────────────────┘
                                    ↓
┌─────────────────────────────────────────────────────────────────┐
│                      核心控制器層                               │
├─────────────────────────────────────────────────────────────────┤
│ 檔案: eqc_standard_processor.py                                 │
│ 主要控制函式:                                                    │
│ • process_code_comparison_pipeline()  - 一鍵程式碼對比流程      │
│ • process_standard_eqc_pipeline()     - 完整 EQC 處理流程       │
│ • process_from_stage2_only()          - 第二階段專用處理        │
│ • _generate_standard_report()         - 標準處理報告生成        │
│ • _generate_final_report()            - 最終處理報告生成        │
└─────────────────────────────────────────────────────────────────┘
                                    ↓
┌─────────────────────────────────────────────────────────────────┐
│                    第一階段: EQCTOTALDATA 生成                  │
├─────────────────────────────────────────────────────────────────┤
│ 檔案: src/infrastructure/adapters/excel/eqc/eqc_bin1_final_processor.py │
│ 核心類別: EQCBin1FinalProcessor                                  │
│ 主要函式:                                                        │
│ • process_complete_eqc_integration()  - 完整 EQC 整合處理       │
│ • generate_eqc_total_data()           - EQCTOTALDATA 生成       │
│ • find_golden_ic_content()            - 尋找 Golden IC 內容     │
│ • convert_spd_files_to_csv()          - SPD 檔案轉 CSV         │
│ • fill_eqc_bin1_statistics()          - 填入 BIN1 統計資料     │
│ • generate_ft_eqc_fail_data_with_hyperlinks() - 失敗資料超連結 │
│ • _process_eqc_rt_files_sorted()      - EQC RT 檔案排序處理    │
│                                                                 │
│ 輔助模組:                                                        │
│ • HyperlinkProcessor                  - 超連結處理器            │
│ • EQCDebugLogger                      - 詳細日誌記錄器          │
│ • CSVFileDiscovery                    - CSV 檔案發現器          │
└─────────────────────────────────────────────────────────────────┘
                                    ↓
┌─────────────────────────────────────────────────────────────────┐
│                  第二階段: 程式碼區間檢測                        │
├─────────────────────────────────────────────────────────────────┤
│ 檔案: src/infrastructure/adapters/excel/eqc/eqc_simple_detector.py │
│ 核心類別: EQCSimpleDetector                                      │
│ 主要函式:                                                        │
│ • find_code_region()                  - 程式碼區間檢測主函式    │
│ • _find_bin1_rows()                   - 尋找 BIN=1 資料行      │
│ • _detect_code_differences()          - 檢測程式碼差異          │
│ • _find_consecutive_integer_range()   - 尋找連續整數區間        │
│ • _find_first_difference()            - 第一次差異檢測          │
│ • _find_second_difference_range()     - 第二次區間檢測          │
│ • _is_numeric()                       - 數值驗證函式            │
│                                                                 │
│ 檢測邏輯:                                                        │
│ • 目標區間: 第298-335欄 (主要區間)                              │
│ • 備用區間: 第1565-1600欄 (備用區間)                            │
│ • 連續整數閾值: 230                                             │
└─────────────────────────────────────────────────────────────────┘
                                    ↓
┌─────────────────────────────────────────────────────────────────┐
│                    第三階段: 雙重搜尋機制                        │
├─────────────────────────────────────────────────────────────────┤
│ 檔案: src/infrastructure/adapters/excel/eqc/eqc_dual_search_corrected.py │
│ 核心類別: EQCDualSearchCorrected                                 │
│ 主要函式:                                                        │
│ • perform_corrected_dual_search()     - 修正版雙重搜尋主函式    │
│ • _search_main_region_complete_match() - 主要區間完全匹配搜尋   │
│ • _search_backup_region_with_mapping() - 備用區間映射搜尋       │
│ • _validate_code_region_parameters()  - 程式碼區間參數驗證      │
│                                                                 │
│ 搜尋策略:                                                        │
│ • 第一重搜尋: 主要區間 100% 完全匹配驗證                        │
│ • 第二重搜尋: 備用區間動態映射到主要區間前N個欄位               │
│ • 映射邏輯: 備用區間N個欄位對應主要區間前N個欄位                │
│                                                                 │
│ 範例實作檔案:                                                    │
│ • eqc_dual_search_example.py          - 雙重搜尋機制範例        │
│   - EQCDualSearchMechanism             - 雙重搜尋機制類別       │
│   - perform_dual_search_matching()     - 雙重搜尋匹配函式       │
│   - _search_in_main_region()           - 主要區間搜尋           │
│   - _search_in_backup_region()         - 備用區間搜尋           │
└─────────────────────────────────────────────────────────────────┘
                                    ↓
┌─────────────────────────────────────────────────────────────────┐
│                    第四階段: 完整報告生成                        │
├─────────────────────────────────────────────────────────────────┤
│ 主要報告生成函式:                                                │
│ • _generate_standard_report()         - 標準處理報告            │
│ • _generate_final_report()            - 最終處理報告            │
│                                                                 │
│ 報告內容包含:                                                    │
│ • 處理時間與目錄資訊                                             │
│ • 程式碼區間檢測結果                                             │
│ • 雙重搜尋機制結果                                               │
│ • 各階段處理狀態                                                 │
│ • 檔案生成統計資訊                                               │
│                                                                 │
│ 環境變數控制:                                                    │
│ • EQC_DETAILED_LOGS=true/false        - 控制詳細報告生成        │
└─────────────────────────────────────────────────────────────────┘

🔧 輔助工具模組
===============

┌─────────────────────────────────────────────────────────────────┐
│                        路徑處理模組                              │
├─────────────────────────────────────────────────────────────────┤
│ • process_chinese_paths_in_directory() - 中文路徑標準化處理     │
│ • process_folder_path()               - 資料夾路徑轉換          │
└─────────────────────────────────────────────────────────────────┘

┌─────────────────────────────────────────────────────────────────┐
│                        日誌記錄模組                              │
├─────────────────────────────────────────────────────────────────┤
│ • EQCDebugLogger                      - EQC 詳細日誌記錄器      │
│   - log_section()                     - 記錄處理階段            │
│   - log_file_scan()                   - 記錄檔案掃描結果        │
│   - log_processing_step()             - 記錄處理步驟            │
└─────────────────────────────────────────────────────────────────┘

┌─────────────────────────────────────────────────────────────────┐
│                        檔案處理模組                              │
├─────────────────────────────────────────────────────────────────┤
│ • CSVFileDiscovery                    - CSV 檔案自動發現        │
│   - find_all_csv_files()              - 尋找所有 CSV 檔案      │
│   - scan_directory()                  - 目錄掃描函式            │
│                                                                 │
│ • HyperlinkProcessor                  - 超連結處理器            │
│   - create_hyperlink()                - 建立檔案超連結          │
│   - convert_to_network_path()         - 轉換為網路路徑          │
└─────────────────────────────────────────────────────────────────┘

🎯 核心工作流程
===============

1. 前端觸發 processCompleteEQCWorkflow()
   ↓
2. API 接收請求 /api/process_online_eqc
   ↓
3. 路徑處理與驗證 process_folder_path()
   ↓
4. 執行一鍵程式碼對比流程 process_code_comparison_pipeline()
   ↓
5. 步驟0: 中文路徑處理 process_chinese_paths_in_directory()
   ↓
6. 步驟1: 自動生成 EQCTOTALDATA process_complete_eqc_integration()
   ↓
7. 步驟2: 程式碼區間檢測 find_code_region()
   ↓
8. 步驟3: 雙重搜尋機制 perform_corrected_dual_search()
   ↓
9. 步驟4: 完整報告生成 _generate_standard_report()

📊 處理結果統計
===============
• EQCTOTALDATA.csv 生成
• EQCTOTALDATA_RAW.csv 生成  
• 程式碼區間檢測報告
• 雙重搜尋匹配結果
• 完整處理報告 (EQC_標準處理報告_YYYYMMDD_HHMMSS.txt)

🔍 關鍵技術特點
===============
• 兩階段處理架構: 第一階段生成資料，第二階段分析處理
• 雙重搜尋容錯機制: 主要區間失敗時自動切換備用區間
• 動態區間檢測: 自動識別程式碼區間位置
• 智能檔案發現: 自動掃描並處理所有相關 CSV 檔案
• 詳細日誌追蹤: 完整記錄每個處理步驟的詳細資訊
• 環境變數控制: 可彈性控制報告生成和日誌詳細程度

=====================================
文檔建立時間: 2025-06-20
系統版本: EQC 一鍵完成處理系統 v2.0
=====================================
