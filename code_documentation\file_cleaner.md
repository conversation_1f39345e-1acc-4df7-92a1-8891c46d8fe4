# file_cleaner.py

檔案清理服務模組，提供24小時自動清理功能，遵循CLAUDE.md的反假測試原則。

## FileCleaner

檔案清理服務類別，提供檔案清理的核心功能。

### 屬性
- `logger` (logging.Logger): 日誌記錄器

### __init__

初始化檔案清理器。

**參數:**
- `logger` (Optional[logging.Logger]): 可選的日誌記錄器，如果未提供則使用模組預設記錄器

**返回值:**
- None

### clean_old_files

清理超過指定時間的檔案。

**參數:**
- `directory` (str): 要清理的目錄路徑
- `hours` (int): 檔案保留時間（小時），預設為24
- `max_items` (int): 最大處理項目數（防止無限循環），預設為1000

**返回值:**
- List[str]: 被清理的檔案名稱列表

**異常:**
- FileNotFoundError: 目錄不存在時拋出
- OSError: 檔案操作失敗時拋出

**功能:**
- 檢查目錄是否存在且為有效目錄
- 計算檔案截止時間
- 使用 os.scandir 提高性能
- 只處理檔案，跳過目錄和符號連結
- 記錄詳細的清理日誌
- 防止無限循環的安全機制

### clean_old_files_recursive

遞歸清理目錄中的舊檔案（包含子目錄）。

**參數:**
- `directory` (str): 要清理的目錄路徑
- `hours` (int): 檔案保留時間（小時），預設為24
- `max_items` (int): 最大處理項目數（防止無限循環），預設為10000

**返回值:**
- List[str]: 被清理的檔案路徑列表

**異常:**
- FileNotFoundError: 目錄不存在時拋出
- OSError: 檔案操作失敗時拋出

**功能:**
- 遞歸遍歷所有子目錄
- 清理所有符合條件的檔案
- 嘗試刪除空的子目錄
- 記錄相對路徑以便追蹤
- 安全的錯誤處理機制

### get_file_age_info

獲取目錄中檔案的年齡資訊。

**參數:**
- `directory` (str): 目錄路徑

**返回值:**
- List[dict]: 包含檔案資訊的字典列表，每個字典包含：
  - `name` (str): 檔案名稱
  - `path` (str): 檔案完整路徑
  - `size` (int): 檔案大小（位元組）
  - `modified_time` (float): 修改時間戳
  - `age_hours` (float): 檔案年齡（小時）
  - `creation_time` (float): 建立時間戳

**功能:**
- 掃描目錄中的所有檔案
- 計算檔案年齡
- 收集詳細的檔案統計資訊
- 安全的錯誤處理

### preview_cleanup

預覽將被清理的檔案（不實際刪除）。

**參數:**
- `directory` (str): 目錄路徑
- `hours` (int): 檔案保留時間（小時），預設為24

**返回值:**
- List[str]: 將被清理的檔案名稱列表

**功能:**
- 模擬清理過程但不實際刪除檔案
- 用於預覽清理結果
- 幫助用戶確認清理範圍
- 安全的檢查機制

## 設計特點

### 安全機制
- **防止無限循環**: 使用 max_items 參數限制處理項目數量
- **路徑驗證**: 檢查目錄存在性和有效性
- **符號連結處理**: 不跟隨符號連結，避免安全風險
- **錯誤隔離**: 單個檔案錯誤不影響整體清理過程

### 效能優化
- **使用 os.scandir**: 比 os.listdir 更高效
- **批次處理**: 一次性處理多個檔案
- **記憶體控制**: 避免載入過多檔案資訊到記憶體

### 日誌記錄
- **詳細日誌**: 記錄每個操作的詳細資訊
- **分級記錄**: 使用不同級別記錄不同類型的事件
- **錯誤追蹤**: 完整記錄錯誤資訊以便除錯

### 彈性配置
- **可調整保留時間**: 支援自訂檔案保留時間
- **可選遞歸**: 支援單層或遞歸清理
- **預覽功能**: 支援預覽清理結果
