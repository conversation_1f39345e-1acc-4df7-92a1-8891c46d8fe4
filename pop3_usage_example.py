#!/usr/bin/env python3
"""
POP3 郵件系統使用範例
展示如何使用 POP3 郵件讀取器和 SMTP 發送器
"""

import asyncio
import sys
from pathlib import Path
from datetime import datetime

# 添加項目根目錄到 Python 路徑
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.infrastructure.adapters.email_reader_factory import EmailReaderFactory, EmailReaderType
from src.infrastructure.adapters.smtp.smtp_sender import SMTPSender, SMTPConfig, EmailMessage
from email_config import EmailConfigManager


async def main():
    """主函數"""
    print("=== POP3 郵件系統使用範例 ===")
    print()
    
    # 初始化配置管理器
    config_manager = EmailConfigManager()
    
    try:
        # 1. 建立郵件讀取器
        print("1. 建立 POP3 郵件讀取器...")
        email_reader = EmailReaderFactory.create_from_env_config(EmailReaderType.POP3)
        
        # 2. 連接到 POP3 伺服器
        print("2. 連接到 POP3 伺服器...")
        if await email_reader.connect():
            print("✅ POP3 連接成功")
            
            # 3. 檢查郵件數量
            unread_count = email_reader.get_unread_count()
            print(f"📧 未讀郵件數量: {unread_count}")
            
            # 4. 讀取郵件
            if unread_count > 0:
                print("4. 讀取郵件...")
                emails = await email_reader.read_emails(count=5)
                
                for i, email in enumerate(emails, 1):
                    print(f"\n📧 郵件 {i}:")
                    print(f"   主旨: {email.subject}")
                    print(f"   寄件者: {email.sender}")
                    print(f"   時間: {email.received_time}")
                    print(f"   附件: {len(email.attachments)} 個")
                    
                    # 解析郵件
                    parsing_result = await email_reader.parse_email(email)
                    if parsing_result.is_success:
                        print(f"   解析狀態: ✅ 成功")
                        print(f"   廠商代碼: {parsing_result.vendor_code}")
                    else:
                        print(f"   解析狀態: ❌ 失敗")
                        print(f"   錯誤訊息: {parsing_result.error_message}")
            else:
                print("📭 沒有未讀郵件")
            
            # 5. 斷開連接
            await email_reader.disconnect()
            print("✅ POP3 連接已斷開")
            
        else:
            print("❌ POP3 連接失敗")
            return
        
        # 6. 發送測試郵件
        print("\n6. 發送測試郵件...")
        
        # 取得 SMTP 配置
        smtp_config_data = config_manager.get_smtp_config()
        account_config = config_manager.get_email_account()
        
        # 建立 SMTP 配置
        smtp_config = SMTPConfig(
            server=smtp_config_data.server,
            port=smtp_config_data.port,
            username=account_config.email_address,
            password=account_config.password,
            use_auth=smtp_config_data.use_auth,
            use_tls=smtp_config_data.use_tls,
            timeout=smtp_config_data.timeout
        )
        
        # 建立 SMTP 發送器
        smtp_sender = SMTPSender(smtp_config)
        
        # 發送測試郵件
        test_message = EmailMessage(
            to=[account_config.email_address],
            subject=f"POP3 系統測試 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
            body=f"""這是一封來自 POP3 郵件系統的測試郵件。

發送時間: {datetime.now()}
系統狀態: 正常運作

這封郵件展示了以下功能：
1. SMTP 郵件發送
2. POP3 郵件讀取
3. 郵件解析
4. 附件處理

系統已準備好處理您的郵件！
""",
            html_body=f"""
<html>
<body>
    <h2>POP3 系統測試郵件</h2>
    <p>這是一封來自 POP3 郵件系統的測試郵件。</p>
    
    <h3>系統資訊</h3>
    <ul>
        <li>發送時間: {datetime.now()}</li>
        <li>系統狀態: <strong style="color: green;">正常運作</strong></li>
    </ul>
    
    <h3>功能展示</h3>
    <ol>
        <li>SMTP 郵件發送 ✅</li>
        <li>POP3 郵件讀取 ✅</li>
        <li>郵件解析 ✅</li>
        <li>附件處理 ✅</li>
    </ol>
    
    <p><strong>系統已準備好處理您的郵件！</strong></p>
</body>
</html>
"""
        )
        
        if smtp_sender.send_email(test_message):
            print("✅ 測試郵件發送成功")
            
            # 顯示發送統計
            stats = smtp_sender.get_statistics()
            print(f"📊 發送統計:")
            print(f"   總發送數: {stats['total_sent']}")
            print(f"   失敗數: {stats['total_failed']}")
            
        else:
            print("❌ 測試郵件發送失敗")
        
        print("\n=== 範例完成 ===")
        
    except Exception as e:
        print(f"❌ 範例執行失敗: {e}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())