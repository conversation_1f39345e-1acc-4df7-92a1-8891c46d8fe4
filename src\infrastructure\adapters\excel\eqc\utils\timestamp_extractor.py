#!/usr/bin/env python3
"""
統一時間戳提取工具模組
消除重複的時間解析邏輯，提供標準化的時間戳處理功能
遵循 CLAUDE.md 功能替換原則
"""

import os
from datetime import datetime
from typing import Optional


class TimestampExtractor:
    """
    統一時間戳提取器
    解決跨多個檔案的重複時間解析邏輯問題
    """
    
    @staticmethod
    def extract_internal_timestamp(file_path: str) -> Optional[int]:
        """
        提取檔案內部時間戳 - 統一版本
        優先使用檔案內部第6行的Date資訊，備用檔案名稱時間戳
        
        修復內容：
        - 修正2位年份解析邏輯 (50年分界點)
        - 統一錯誤處理機制
        - 標準化備用邏輯
        
        Args:
            file_path (str): 檔案路徑
            
        Returns:
            Optional[int]: Unix時間戳，失敗返回None
        """
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            # 檢查第6行 (Date行) - 優先使用檔案內部時間
            if len(lines) > 5:
                date_line = lines[5]  # 第6行 (索引5)
                if "Date:" in date_line:
                    # 提取時間字串並轉換為時間戳
                    parts = date_line.split(',')
                    if len(parts) > 1:
                        date_str = parts[1].strip()
                        timestamp = TimestampExtractor._parse_csv_date_string(date_str)
                        if timestamp:
                            return timestamp
            
            # 備用1：使用檔案名稱時間戳 (14位數字)
            filename = os.path.splitext(os.path.basename(file_path))[0]
            if len(filename) >= 14:  # 檢查是否有時間戳
                timestamp_str = filename[-14:]  # 最後14位數字
                if timestamp_str.isdigit():
                    return int(timestamp_str)
            
            # 備用2：檢查8位數字時間戳
            if len(filename) >= 8:
                timestamp_str = filename[-8:]  # 最後8位數字
                if timestamp_str.isdigit():
                    return int(timestamp_str)
            
            return None
            
        except Exception as e:
            # 除錯模式下可打開此註解
            # print(f"   除錯: 時間戳提取失敗 {os.path.basename(file_path)}: {e}")
            return None
    
    @staticmethod
    def _parse_csv_date_string(date_str: str) -> Optional[int]:
        """
        解析CSV內部的日期字串
        修復2位年份解析問題，使用50年分界點邏輯
        
        Args:
            date_str (str): 日期字串，如 "05/22/25 18:44:13"
            
        Returns:
            Optional[int]: Unix時間戳，解析失敗返回None
        """
        try:
            # 修復2位年份解析問題 - 使用50年分界點
            # 格式為 "05/22/25 18:44:13" 或 "07/22/24 00:00:52"
            dt = datetime.strptime(date_str, "%m/%d/%y %H:%M:%S")
            
            # 修正年份邏輯：2位年份智能判斷
            # 0-49 -> 2000-2049, 50-99 -> 1950-1999
            if dt.year < 1970:  # Unix timestamp開始年份
                # 如果解析出的年份小於1970，調整到正確的21世紀年份
                corrected_year = dt.year + 100
                dt = dt.replace(year=corrected_year)
            
            return int(dt.timestamp())
            
        except ValueError:
            # 嘗試其他常見日期格式
            try:
                dt = datetime.strptime(date_str, "%Y-%m-%d %H:%M:%S")
                return int(dt.timestamp())
            except ValueError:
                return None
        except Exception:
            return None
    
    @staticmethod 
    def parse_filename_timestamp(filename: str) -> Optional[int]:
        """
        從檔案名稱解析時間戳
        支援多種時間戳格式
        
        Args:
            filename (str): 檔案名稱
            
        Returns:
            Optional[int]: Unix時間戳，解析失敗返回None
        """
        # 移除副檔名
        basename = os.path.splitext(filename)[0]
        
        # 嘗試14位數字時間戳
        if len(basename) >= 14:
            timestamp_str = basename[-14:]
            if timestamp_str.isdigit():
                return int(timestamp_str)
        
        # 嘗試8位數字時間戳  
        if len(basename) >= 8:
            timestamp_str = basename[-8:]
            if timestamp_str.isdigit():
                return int(timestamp_str)
        
        return None
    
    @staticmethod
    def format_timestamp_readable(timestamp: Optional[int]) -> str:
        """
        將時間戳格式化為可讀字串
        
        Args:
            timestamp (Optional[int]): Unix時間戳
            
        Returns:
            str: 格式化的時間字串
        """
        if timestamp and timestamp > 0:
            try:
                return datetime.fromtimestamp(timestamp).strftime("%Y-%m-%d %H:%M:%S")
            except (ValueError, OSError):
                return "無效時間戳"
        return "無時間戳"