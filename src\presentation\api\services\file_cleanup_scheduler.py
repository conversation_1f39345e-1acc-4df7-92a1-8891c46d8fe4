"""
檔案清理調度器模組
提供定時清理和手動清理功能
"""

import os
import time
from typing import Dict, Any, List
from pathlib import Path
from loguru import logger


class FileCleanupScheduler:
    """檔案清理調度器"""
    
    def __init__(self):
        self.is_running = False
        self.default_retention_hours = 24
        
    def manual_cleanup(self, target_directories: List[str] = None, retention_hours: int = 24) -> Dict[str, Any]:
        """
        手動清理檔案
        
        Args:
            target_directories: 要清理的目錄列表
            retention_hours: 保留時間（小時）
            
        Returns:
            清理結果
        """
        logger.info(f"🗑️ 開始手動清理檔案，保留時間: {retention_hours}小時")
        
        if not target_directories:
            target_directories = [
                "/mnt/d/project/python/outlook_summary/doc",
                "/tmp/uploads",
                "/tmp/extracted"
            ]
        
        results = {
            "total_cleaned": 0,
            "directories": {},
            "errors": []
        }
        
        current_time = time.time()
        retention_seconds = retention_hours * 3600
        
        for directory in target_directories:
            if not os.path.exists(directory):
                continue
                
            try:
                dir_results = self._cleanup_directory(directory, current_time, retention_seconds)
                results["directories"][directory] = dir_results
                results["total_cleaned"] += dir_results["cleaned_count"]
                
            except Exception as e:
                error_msg = f"清理目錄失敗 {directory}: {str(e)}"
                logger.error(error_msg)
                results["errors"].append(error_msg)
        
        logger.info(f"✅ 手動清理完成，共清理 {results['total_cleaned']} 個檔案")
        return results
    
    def _cleanup_directory(self, directory: str, current_time: float, retention_seconds: int) -> Dict[str, Any]:
        """
        清理單個目錄
        
        Args:
            directory: 目錄路徑
            current_time: 當前時間戳
            retention_seconds: 保留時間（秒）
            
        Returns:
            清理結果
        """
        result = {
            "cleaned_count": 0,
            "cleaned_files": [],
            "total_size_freed": 0
        }
        
        try:
            for root, dirs, files in os.walk(directory):
                for file in files:
                    file_path = os.path.join(root, file)
                    
                    try:
                        # 獲取檔案修改時間
                        file_mtime = os.path.getmtime(file_path)
                        
                        # 檢查是否超過保留時間
                        if current_time - file_mtime > retention_seconds:
                            file_size = os.path.getsize(file_path)
                            os.remove(file_path)
                            
                            result["cleaned_count"] += 1
                            result["cleaned_files"].append(file_path)
                            result["total_size_freed"] += file_size
                            
                            logger.debug(f"🗑️ 清理檔案: {file_path}")
                            
                    except Exception as e:
                        logger.warning(f"無法清理檔案 {file_path}: {str(e)}")
                        
        except Exception as e:
            logger.error(f"遍歷目錄失敗 {directory}: {str(e)}")
            raise
        
        return result
    
    def get_status(self) -> Dict[str, Any]:
        """
        獲取調度器狀態
        
        Returns:
            狀態資訊
        """
        return {
            "is_running": self.is_running,
            "default_retention_hours": self.default_retention_hours,
            "status": "active" if self.is_running else "stopped"
        }
    
    def start_scheduler(self) -> Dict[str, Any]:
        """啟動調度器"""
        self.is_running = True
        logger.info("🔄 檔案清理調度器已啟動")
        return {"message": "調度器已啟動"}
    
    def stop_scheduler(self) -> Dict[str, Any]:
        """停止調度器"""
        self.is_running = False
        logger.info("⏹️ 檔案清理調度器已停止")
        return {"message": "調度器已停止"}