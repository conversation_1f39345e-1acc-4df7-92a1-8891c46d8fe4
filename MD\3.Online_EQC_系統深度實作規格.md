# 3. Online EQC 系統深度實作規格

## 📋 文檔資訊
- **建立日期**: 2025-06-07
- **版本**: v3.0 (整合版)
- **層級**: 第三層級專業系統文件
- **基於**: `REF/module2.txt` 逐行深度分析 + 功能整合
- **目的**: 提供精確的實作細節，確保 Python 版本完全對應 VBA 邏輯
- **關聯**: 隸屬於 `0.專案流程管理總覽.md` 階層式管理架構

## 🔍 核心函數深度解析

### 0. 檔案發現與分類系統基礎函數 (新增分析)

這是整個 Online EQC 系統的基礎架構，對應 VBA 中的檔案處理流水線。

#### 0.1 FindALLCSVFiles 函數分析

**VBA 原始邏輯**:
```vba
Function FindALLCSVFiles(folderPath As String) As Variant
    ' 遞迴掃描資料夾，找出所有 CSV 檔案
    ' 返回: CSV 檔案路徑陣列
End Function
```

**Python 實作**:
```python
class CSVFileDiscovery:
    def find_all_csv_files(self, folder_path: str) -> List[str]:
        """
        遞迴掃描資料夾，找出所有 CSV 檔案
        
        核心邏輯：
        1. 使用 os.walk() 進行遞迴掃描
        2. 過濾 .csv 副檔名
        3. 排除已處理檔案 (eqctotaldata, eqcfaildata)
        4. 自動調用去重功能
        
        Returns:
            List[str]: 去重後的 CSV 檔案路徑列表
        """
        csv_files = []
        
        for root, dirs, files in os.walk(folder_path):
            for file in files:
                if file.lower().endswith('.csv'):
                    file_path = os.path.join(root, file)
                    
                    # 排除已處理的檔案
                    if not any(excluded in file.lower() 
                              for excluded in self.excluded_files):
                        csv_files.append(file_path)
        
        return self._remove_duplicates(csv_files)
```

**關鍵特性**：
- **遞迴掃描**：支援多層資料夾結構
- **檔案過濾**：自動排除系統生成檔案
- **整合去重**：一步完成掃描和去重

#### 0.2 RemoveDuplicateCSVFiles 函數分析

**VBA 原始邏輯**:
```vba
Function RemoveDuplicateCSVFiles(csvFiles As Variant) As Variant
    ' 基於檔案名稱去除重複檔案
    ' 保留第一個遇到的檔案
End Function
```

**Python 實作**:
```python
def _remove_duplicates(self, csv_files: List[str]) -> List[str]:
    """
    去除重複檔案 (基於檔案名稱)
    
    核心邏輯：
    1. 使用 set 追蹤已見過的檔案名稱
    2. 保留第一次遇到的檔案
    3. 忽略路徑差異，只比較檔案名稱
    
    Args:
        csv_files: 原始 CSV 檔案列表
        
    Returns:
        List[str]: 去重後的檔案列表
    """
    seen_names = set()
    unique_files = []
    
    for file_path in csv_files:
        filename = os.path.basename(file_path)
        if filename not in seen_names:
            seen_names.add(filename)
            unique_files.append(file_path)
    
    return unique_files
```

**去重策略**：
- **基於檔案名稱**：忽略不同路徑下的同名檔案
- **保持順序**：維持原始掃描順序
- **記憶體高效**：使用 set 進行 O(1) 查找

#### 0.3 FindALLEQCFILE 函數分析

**VBA 原始邏輯**:
```vba
Function FindALLEQCFILE(csvFiles As Variant) As Variant
    ' 從 CSV 檔案列表中篩選出 EQC 檔案
    ' 檢測規則：檔案內容包含 (qc) 標記
End Function
```

**Python 實作**:
```python
def classify_eqc_files(self, csv_files: List[str]) -> List[str]:
    """
    分類 EQC 檔案 (包含 Online EQC)
    
    檢測規則優先順序：
    1. 檔案路徑包含 'onlineeqc' 或 '.qa'
    2. 檔案前兩行包含 '(qc)' 標記  
    3. 第三行包含 'qa' 標記
    
    Returns:
        List[str]: EQC 檔案路徑列表
    """
    eqc_files = []
    
    for file_path in csv_files:
        if self._is_eqc_file(file_path):
            eqc_files.append(file_path)
    
    return eqc_files

def _is_eqc_file(self, file_path: str) -> bool:
    """檢測是否為 EQC 檔案"""
    try:
        filename_lower = file_path.lower()
        
        # 規則1: 檔案路徑檢測
        if 'onlineeqc' in filename_lower or '.qa' in filename_lower:
            return True
        
        with open(file_path, 'r', encoding='utf-8') as f:
            # 規則2: 前兩行內容檢測
            for i in range(2):
                line = f.readline().lower()
                if '(qc)' in line:
                    return True
            
            # 規則3: 第三行檢測
            third_line = f.readline().lower()
            if 'qa' in third_line:
                return True
        
        return False
    except Exception:
        return False
```

**檢測機制**：
- **多層檢測**：檔案名稱 → 檔案內容 → 備用規則
- **容錯處理**：異常檔案不會中斷處理
- **標記識別**：精確識別 Online EQC 檔案

#### 0.4 FindALLFTFILE 函數分析

**VBA 原始邏輯**:
```vba
Function FindALLFTFILE(csvFiles As Variant) As Variant
    ' 從 CSV 檔案列表中篩選出 FT 檔案  
    ' 檢測規則：檔案內容包含 (ft) 或 (auto_qc) 標記
End Function
```

**Python 實作**:
```python
def classify_ft_files(self, csv_files: List[str]) -> List[str]:
    """
    分類 FT 檔案
    
    檢測規則：
    - 檔案前兩行包含 '(ft)' 或 '(auto_qc)' 標記
    
    Returns:
        List[str]: FT 檔案路徑列表
    """
    ft_files = []
    
    for file_path in csv_files:
        if self._is_ft_file(file_path):
            ft_files.append(file_path)
    
    return ft_files

def _is_ft_file(self, file_path: str) -> bool:
    """檢測是否為 FT 檔案"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            # 檢查前兩行
            for i in range(2):
                line = f.readline().lower()
                if any(marker in line for marker in self.ft_markers):
                    return True
        return False
    except Exception:
        return False
```

**識別特性**：
- **多標記支援**：同時支援 (ft) 和 (auto_qc)
- **快速檢測**：只讀取前兩行提升效能
- **精確匹配**：避免誤判非 FT 檔案

#### 0.5 完整檔案處理流水線

**整合使用範例**:
```python
# VBA 流水線對應
def process_file_pipeline(folder_path: str) -> Tuple[List[str], List[str]]:
    """
    完整檔案處理流水線 - 對應 VBA 四步驟
    
    VBA 對應：
    csvFilestemp = FindALLCSVFiles(folderPath)
    csvFiles = RemoveDuplicateCSVFiles(csvFilestemp) 
    csvFileseqc = FindALLEQCFILE(csvFiles)
    csvFilesft = FindALLFTFILE(csvFiles)
    """
    discovery = CSVFileDiscovery()
    
    # 步驟1+2: 掃描並去重 (整合在一個函數中)
    csv_files = discovery.find_all_csv_files(folder_path)
    
    # 步驟3: 分類 EQC 檔案
    eqc_files = discovery.classify_eqc_files(csv_files)
    
    # 步驟4: 分類 FT 檔案  
    ft_files = discovery.classify_ft_files(csv_files)
    
    return eqc_files, ft_files
```

**效能優化**：
- **一次掃描**：檔案發現和去重整合處理
- **並行分類**：EQC 和 FT 分類可並行執行
- **記憶體管理**：避免重複讀取大檔案

### 1. CopyRowsToNewFile 函數深度分析 (671-892行)

這是整個系統最複雜的函數，包含多層條件判斷和資料處理邏輯。

#### 1.1 函數簽名和變數初始化
```vba
Function CopyRowsToNewFile(filenameA As String, filenameB As String) As String
' filenameA = FT檔案, filenameB = EQC失敗檔案
```

**關鍵變數說明**:
```vba
' 檔案格式檢測變數
Dim cta_file8290 As Boolean  ' CTA 8290格式 (Serial_No)
Dim cta_file8280 As Boolean  ' CTA 8280格式 (Index_No)
Dim cta_file As Integer      ' CTA格式代碼 (0=一般, 8290, 8280)

' 測試時間檢測變數
Dim havetesttime_ft As Boolean   ' FT檔案是否有測試時間
Dim havetesttime_qc As Boolean   ' EQC檔案是否有測試時間

' 欄位過濾變數 (僅CTA8280格式)
Dim have_X_COORD_ft As Boolean   ' 是否有X座標欄位
Dim have_Y_COORD_ft As Boolean   ' 是否有Y座標欄位
Dim have_Alarm_ft As Boolean     ' 是否有警報欄位
Dim bypass1, bypass2, bypass3 As Integer  ' 要跳過的欄位索引

' CTA資料匹配變數
Dim cta_Part_No As String    ' 零件編號 (第3欄)
Dim cta_Dut_No As String     ' 設備編號 (第4欄)
Dim cta_Site_No As String    ' Site編號 (第5欄)
```

#### 1.2 檔案格式檢測邏輯 (769-802行)

**Python 實作**:
```python
class CTAFormatDetector:
    """CTA 格式檢測器"""
    
    def detect_format(self, ft_file_lines: List[str]) -> Dict[str, Any]:
        """檢測 CTA 檔案格式和特徵"""
        format_info = {
            'cta_format': 0,  # 0=一般, 8290, 8280
            'has_test_time': False,
            'coordinate_columns': {'x': -1, 'y': -1, 'alarm': -1},
            'bypass_columns': []
        }
        
        # 第7行檢測 (index 6)
        if len(ft_file_lines) > 6:
            line_7_elements = ft_file_lines[6].split(',')
            if len(line_7_elements) > 2:
                if 'Serial_No' in line_7_elements[2]:
                    format_info['cta_format'] = 8290
                elif 'Index_No' in line_7_elements[2]:
                    format_info['cta_format'] = 8280
                    
                    # 僅CTA8280格式檢測座標欄位
                    if len(line_7_elements) > 8 and 'X_COORD' in line_7_elements[8]:
                        format_info['coordinate_columns']['x'] = 8
                        format_info['bypass_columns'].append(8)
                    
                    if len(line_7_elements) > 9 and 'Y_COORD' in line_7_elements[9]:
                        format_info['coordinate_columns']['y'] = 9
                        format_info['bypass_columns'].append(9)
                    
                    if len(line_7_elements) > 14 and 'Alarm' in line_7_elements[14]:
                        format_info['coordinate_columns']['alarm'] = 14
                        format_info['bypass_columns'].append(14)
        
        # 第8行檢測CTA標記 (index 7)
        if len(ft_file_lines) > 7:
            line_8_elements = ft_file_lines[7].split(',')
            if line_8_elements and 'cta' in line_8_elements[0].lower():
                # 確認格式代碼
                pass
        
        # 第11行檢測測試時間 (index 10)
        if len(ft_file_lines) > 10:
            line_11_elements = ft_file_lines[10].split(',')
            if len(line_11_elements) > 2 and 'Time' in line_11_elements[2]:
                format_info['has_test_time'] = True
        
        return format_info
```

#### 1.3 資料匹配的三種模式 (804行)

**VBA 原始邏輯**:
```vba
If (lineElementsA(0) = valueInColumnS And cta_file = 0) Or 
   (lineElementsA(3) = cta_Part_No And lineElementsA(4) = cta_Dut_No And lineElementsA(5) = cta_Site_No And cta_file = 8290) Or 
   (lineElementsA(3) = cta_Part_No And lineElementsA(4) = cta_Dut_No And lineElementsA(5) = cta_Site_No And cta_file = 8280) Then
```

**Python 實作**:
```python
def match_data_row(self, ft_row: List[str], eqc_fail_row: List[str], 
                  format_info: Dict) -> bool:
    """資料行匹配邏輯"""
    value_in_column_s = eqc_fail_row[0]  # EQC失敗行的第0欄
    cta_part_no = eqc_fail_row[3]        # 第3欄: Part_No
    cta_dut_no = eqc_fail_row[4]         # 第4欄: Dut_No  
    cta_site_no = eqc_fail_row[5]        # 第5欄: Site_No
    
    cta_format = format_info['cta_format']
    
    # 模式1: 一般格式 - 序號匹配
    if cta_format == 0:
        return ft_row[0] == value_in_column_s
    
    # 模式2&3: CTA格式 - Part_No + Dut_No + Site_No 匹配
    elif cta_format in [8290, 8280]:
        return (len(ft_row) > 5 and 
                ft_row[3] == cta_part_no and 
                ft_row[4] == cta_dut_no and 
                ft_row[5] == cta_site_no)
    
    return False
```

#### 1.4 資料清理的三種策略 (805-851行)

**策略1: 座標欄位過濾** (805-834行)
```python
def apply_coordinate_filtering(self, ft_row: List[str], 
                              bypass_columns: List[int]) -> List[str]:
    """過濾座標相關欄位 (僅CTA8280格式)"""
    if not bypass_columns:
        return ft_row
    
    filtered_row = []
    for k, value in enumerate(ft_row):
        if k not in bypass_columns:  # 跳過 X_COORD, Y_COORD, Alarm
            filtered_row.append(value)
    
    return filtered_row
```

**策略2: 測試時間對齊** (835-851行)
```python
def align_test_time_columns(self, ft_row: List[str], 
                           ft_has_time: bool, eqc_has_time: bool) -> List[str]:
    """測試時間欄位對齊處理"""
    if ft_has_time == eqc_has_time:
        return ft_row  # 兩者一致，不需調整
    
    aligned_row = []
    for k, value in enumerate(ft_row):
        if ft_has_time and not eqc_has_time:
            # FT有時間但EQC沒有 -> 跳過第2欄 (測試時間)
            if k != 2:
                aligned_row.append(value)
        elif not ft_has_time and eqc_has_time:
            # FT沒有時間但EQC有 -> 在第2欄插入 "987"
            if k == 2:
                aligned_row.append("987")
            else:
                aligned_row.append(value)
        else:
            aligned_row.append(value)
    
    return aligned_row
```

**策略3: 直接複製** (836行)
```python
def direct_copy(self, ft_row: List[str]) -> List[str]:
    """直接複製FT資料行"""
    return ft_row.copy()
```

#### 1.5 超連結路徑處理 (872-883行)

**VBA 邏輯**:
```vba
hyplink_temp = ReplacePath(filenameA)  ' 轉換為網路路徑
rowAWithFT = AddFTToRowA(targetRowInA, 2, hyplink_temp & ",")
```

**Python 實作**:
```python
def create_hyperlink_data(self, data_row: List[str], file_path: str, 
                         column_index: int = 2) -> List[str]:
    """建立包含超連結的資料行"""
    network_path = self.convert_to_network_path(file_path)
    
    result_row = data_row.copy()
    if column_index < len(result_row):
        result_row[column_index] = network_path
    else:
        # 擴展列表以包含超連結欄位
        result_row.extend([''] * (column_index - len(result_row) + 1))
        result_row[column_index] = network_path
    
    return result_row

def convert_to_network_path(self, local_path: str) -> str:
    """將本地路徑轉換為網路共享路徑"""
    # 對應 ReplacePath 函數邏輯
    temp_path = self.config.get('temp_path', '')
    net_path = self.config.get('net_path', '')
    
    if temp_path in local_path:
        # 替換路徑前綴，保留檔案名稱
        relative_part = local_path.replace(temp_path, '').lstrip('\\/')
        return os.path.join(net_path, relative_part).replace('\\', '/')
    
    return local_path
```

#### 1.6 檔案輸出邏輯 (884-889行)

**檔案命名規則**:
```vba
newFileName = Left(filenameB, InStrRev(filenameB, ".") - 1) & "_EQCFAILDATA.csv"
```

**Python 實作**:
```python
def generate_output_filename(self, eqc_file_path: str) -> str:
    """生成輸出檔案名稱"""
    base_name = os.path.splitext(eqc_file_path)[0]
    return f"{base_name}_EQCFAILDATA.csv"

def save_merged_data(self, merged_data: List[List[str]], 
                    output_file: str, header_lines: List[str]) -> str:
    """儲存合併後的資料"""
    with open(output_file, 'w', encoding='utf-8', newline='') as f:
        # 寫入前12行標頭
        for header_line in header_lines[:12]:
            f.write(header_line + '\n')
        
        # 寫入合併的資料
        csv_writer = csv.writer(f)
        for row in merged_data:
            csv_writer.writerow(row)
    
    return output_file
```

### 2. AddFTToRowA 系列函數詳解 (637-670行)

#### 2.1 AddFTToRowA 基礎版本
```python
def add_ft_to_row(self, target_row: str, column_index: int = 3, 
                 replacement_text: str = "FT") -> str:
    """在指定欄位插入標記文字"""
    columns = target_row.split(',')
    
    for k in range(len(columns)):
        if k == column_index:
            columns[k] = replacement_text
    
    return ','.join(columns)
```

#### 2.2 AddFTToRowA2 雙欄位版本
```python
def add_ft_to_row_dual(self, target_row: str, 
                      column_index1: int = 3, replacement_text1: str = "FT",
                      column_index2: int = 4, replacement_text2: str = "1") -> str:
    """同時在兩個欄位插入標記文字"""
    columns = target_row.split(',')
    
    for k in range(len(columns)):
        if k == column_index1:
            columns[k] = replacement_text1
        elif k == column_index2:
            columns[k] = replacement_text2
    
    return ','.join(columns)
```

### 3. FindDifferentColumn 差異分析算法 (908-1036行)

這個函數實現了一個複雜的數值差異檢測算法：

#### 3.1 核心邏輯分析
```vba
' 找出兩行 BIN=1 的資料
For i = 0 To numRows
    lineElementsA = Split(rows(i + 12), ",")
    If lineElementsA(1) = 1 Then '找到A行
        ARow = i + 12
        For j = i + 1 To numRows
            lineElementsB = Split(rows(j + 12), ",")
            If lineElementsB(1) = 1 Then  '找到B行
                BRow = j + 12
                Exit For
            End If
        Next j
        Exit For
    End If
Next i
```

#### 3.2 連續整數範圍檢測
```vba
' 找出連續整數欄位範圍
For i = 10 To maxIndex
    If IsNumeric(AValues(i)) Then
        temp_a_val = CDbl(AValues(i))
        If IsNumeric(temp_a_val) And Int(temp_a_val) = temp_a_val Then
            cnt = cnt + 1
            If cnt > maxCnt Then
                maxCnt = cnt
                start2 = i - maxCnt + 1
                end1 = i
                If maxCnt >= 230 Then Exit For  ' 效能優化
            End If
        Else
            cnt = 0
        End If
    End If
Next i
```

**Python 實作**:
```python
def find_different_column(self, csv_file: str) -> Tuple[int, int]:
    """找出兩行BIN1資料的差異欄位"""
    df = pd.read_csv(csv_file)
    
    # 檢查檔案是否被佔用
    if self.is_file_locked(csv_file):
        return 0, 0
    
    # 找出BIN=1的行
    data_rows = df.iloc[12:]  # 從第12行開始
    bin1_rows = data_rows[data_rows.iloc[:, 1] == 1]
    
    if len(bin1_rows) < 2:
        return 0, 0
    
    row_a = bin1_rows.iloc[0].values
    row_b = bin1_rows.iloc[1].values
    
    # 找出連續整數範圍
    start_col, end_col = self.find_continuous_integer_range(row_a, start_from=10)
    
    if start_col == 0:
        return 0, 0
    
    # 比較差異 (從 start_col + 3 開始)
    for i in range(start_col + 3, min(end_col, len(row_a), len(row_b))):
        try:
            if float(row_a[i]) != float(row_b[i]):
                return i, end_col
        except (ValueError, TypeError):
            continue
    
    return 0, 0

def find_continuous_integer_range(self, values: np.array, 
                                start_from: int = 10) -> Tuple[int, int]:
    """找出連續整數欄位範圍"""
    max_count = 0
    current_count = 0
    start_col = 0
    end_col = 0
    
    for i in range(start_from, len(values)):
        try:
            val = float(values[i])
            if val == int(val):  # 是整數
                current_count += 1
                if current_count > max_count:
                    max_count = current_count
                    start_col = i - max_count + 1
                    end_col = i
                    if max_count >= 230:  # 效能優化
                        break
            else:
                current_count = 0
        except (ValueError, TypeError):
            current_count = 0
    
    return start_col, end_col
```

### 4. OpenSummaryFindBig 特殊演算法 (1314-1407行)

這是一個非常特殊的 Excel 分析演算法：

#### 4.1 演算法邏輯
```vba
' 步驟1: 找出第1行中數字最大的值
For i = 1 To lastCol1
    If IsNumeric(xlSheet.Cells(1, i)) Then
        If xlSheet.Cells(1, i) > maxNum Then
            maxNum = xlSheet.Cells(1, i)
            maxCol = i - 1
            lastColst = maxCol
        End If
    End If
Next i

' 步驟2: 從該欄位開始，在第5行累加數字直到等於maxNum
For i = maxCol To lastCol5
    If IsNumeric(xlSheet.Cells(5, i).value) Then
        sumNum = sumNum + val(xlSheet.Cells(5, i).value)
        If sumNum = maxNum Then
            lastColsp = i
            Exit For
        End If
    End If
Next i
```

**Python 實作**:
```python
def find_data_range_in_summary(self, excel_file: str) -> Dict[str, int]:
    """在Summary Excel中找出資料範圍"""
    
    workbook = openpyxl.load_workbook(excel_file)
    worksheet = workbook.active
    
    # 步驟1: 找出第1行中的最大數字
    max_num = 0
    max_col = 1
    last_col_st = 1
    
    for col in range(1, worksheet.max_column + 1):
        cell_value = worksheet.cell(row=1, column=col).value
        if isinstance(cell_value, (int, float)) and cell_value > max_num:
            max_num = cell_value
            max_col = col - 1  # VBA使用0-based索引
            last_col_st = max_col
    
    # 步驟2: 在第5行累加數字
    sum_num = 0
    last_col_sp = max_col
    
    for col in range(max_col, worksheet.max_column + 1):
        cell_value = worksheet.cell(row=5, column=col).value
        if isinstance(cell_value, (int, float)):
            sum_num += cell_value
            if sum_num == max_num:
                last_col_sp = col
                break
        else:
            sum_num = 0  # 重置累加
    
    # 找出資料的最後一行
    last_row = 0
    for row in range(8, worksheet.max_row + 1):
        cell_value = worksheet.cell(row=row, column=last_col_st + 3).value
        if cell_value is not None:
            last_row = row
    
    return {
        'x_start': 1,
        'y_start': last_col_st,
        'x_stop': last_col_sp,
        'y_stop': last_row
    }
```

### 5. 檔案路徑處理精確邏輯

#### 5.1 ReplacePath 函數實作 (1569-1584行)
```python
def replace_path(self, input_path: str, temp_path: str, net_path: str) -> str:
    """精確對應VBA的ReplacePath函數"""
    find_str = temp_path + "\\"
    replace_str = net_path + "\\"
    
    start_pos = input_path.find(find_str)
    if start_pos == -1:
        return ""
    
    # 找出最後一個反斜線的位置
    end_pos = input_path.rfind("\\", start_pos + len(find_str))
    
    if end_pos > start_pos:
        # 提取檔案名稱部分
        filename_part = input_path[end_pos + 1:]
        return replace_str + filename_part
    else:
        return ""
```

## 🔧 完整的 Python 實作架構

### 主要類別結構
```python
from dataclasses import dataclass
from typing import List, Dict, Tuple, Optional, Any
import pandas as pd
import numpy as np
import csv
import os
from pathlib import Path

@dataclass
class EQCProcessingConfig:
    """EQC 處理配置"""
    temp_path: str
    net_path: str
    data_start_row: int = 12  # 資料從第12行開始
    header_rows: int = 12     # 前12行是標頭
    
class AdvancedEQCProcessor:
    """進階 EQC 處理器 - 精確對應 VBA 邏輯"""
    
    def __init__(self, config: EQCProcessingConfig):
        self.config = config
        self.format_detector = CTAFormatDetector()
        self.file_matcher = AdvancedFileMatcher()
        
    def copy_rows_to_new_file(self, ft_file: str, eqc_file: str) -> str:
        """精確實作 CopyRowsToNewFile 函數"""
        
        # 讀取檔案
        with open(eqc_file, 'r', encoding='utf-8') as f:
            eqc_lines = f.readlines()
        
        # 檢測EQC檔案的測試時間
        eqc_has_time = self.detect_test_time_in_eqc(eqc_lines)
        
        # 處理每個EQC失敗行
        merged_data = []
        header_written = False
        
        for i in range(self.config.data_start_row, len(eqc_lines)):
            eqc_line = eqc_lines[i].strip()
            if not eqc_line:
                break
                
            eqc_elements = eqc_line.split(',')
            if len(eqc_elements) < 2:
                continue
                
            # 檢查是否為失敗行 (BIN != 1)
            if self.safe_int(eqc_elements[1]) != 1:
                
                # 寫入標頭 (只寫一次)
                if not header_written:
                    for header_line in eqc_lines[:self.config.header_rows]:
                        merged_data.append(header_line.strip().split(','))
                    header_written = True
                
                # 處理 FT 檔案匹配
                if ft_file:
                    ft_format = self.format_detector.detect_format_from_file(ft_file)
                    matched_ft_row = self.find_matching_ft_row(
                        ft_file, eqc_elements, ft_format
                    )
                    
                    if matched_ft_row:
                        # 清理和對齊 FT 資料
                        cleaned_ft_row = self.clean_ft_data(
                            matched_ft_row, ft_format, eqc_has_time
                        )
                        # 加入超連結
                        ft_with_link = self.create_hyperlink_data(
                            cleaned_ft_row, ft_file, 2
                        )
                        merged_data.append(ft_with_link)
                
                # 加入 EQC 失敗行 (含超連結)
                eqc_with_link = self.create_hyperlink_data(
                    eqc_elements, eqc_file, 2
                )
                merged_data.append(eqc_with_link)
        
        # 儲存結果
        output_file = self.generate_output_filename(eqc_file)
        self.save_merged_data_to_csv(merged_data, output_file)
        
        return output_file
```

### 完整的資料處理管道
```python
class EQCDataPipeline:
    """完整的 EQC 資料處理管道"""
    
    def __init__(self, config: EQCProcessingConfig):
        self.config = config
        self.processor = AdvancedEQCProcessor(config)
        
    def execute_complete_eqc_analysis(self, folder_path: str) -> Dict[str, Any]:
        """執行完整的 EQC 分析流程"""
        
        results = {
            'processed_files': [],
            'statistics': {},
            'errors': [],
            'output_files': []
        }
        
        try:
            # 步驟1: 處理 CTA 檔案
            cta_processed = self.processor.read_csvs_in_directory(folder_path)
            
            # 步驟2: 分類 CSV 檔案
            csv_classification = self.processor.classify_csv_files(folder_path)
            
            # 步驟3: 匹配 FT 和 EQC 檔案
            matched_pairs = self.processor.match_ft_eqc_files(
                csv_classification['ft_files'],
                csv_classification['eqc_files']
            )
            
            # 步驟4: 找出 EQC 失敗檔案
            fail_results = self.processor.find_eqc_fail_files(matched_pairs)
            
            # 步驟5: 生成 EQCTOTALDATA.csv
            total_data_file = self.processor.generate_eqc_total_data(
                csv_classification['eqc_files'], fail_results
            )
            
            # 步驟6: 計算統計資料
            statistics = self.processor.calculate_comprehensive_statistics(
                total_data_file, fail_results
            )
            
            # 步驟7: 找出 Golden EQC 並分析差異
            golden_eqc = self.processor.find_golden_eqc_file(
                csv_classification['eqc_files']
            )
            
            if golden_eqc:
                start_col, end_col = self.processor.find_different_column(golden_eqc)
                if start_col > 10 and (end_col - start_col) > 5:
                    self.processor.insert_eqc_rt_data(
                        total_data_file, start_col, end_col, statistics
                    )
            
            # 步驟8: 轉換為 Excel 並建立超連結
            for analysis_file in fail_results['analysis_files']:
                excel_file = self.processor.convert_to_excel_with_hyperlinks(
                    analysis_file
                )
                results['output_files'].append(excel_file)
            
            # 處理主要資料檔案
            if os.path.exists(total_data_file):
                main_excel = self.processor.convert_to_excel_with_hyperlinks(
                    total_data_file
                )
                results['output_files'].append(main_excel)
            
            results['statistics'] = statistics
            results['processed_files'] = len(matched_pairs)
            
        except Exception as e:
            results['errors'].append(str(e))
        
        return results
```

## 📋 精確實作檢查清單

### Phase 0: 檔案發現與分類基礎 (新增 - 已完成 ✅)
- [x] **FindALLCSVFiles 實作**：遞迴掃描資料夾邏輯
- [x] **RemoveDuplicateCSVFiles 實作**：基於檔案名稱的去重策略
- [x] **FindALLEQCFILE 實作**：多層檢測規則的 EQC 檔案識別
- [x] **FindALLFTFILE 實作**：多標記支援的 FT 檔案分類
- [x] **檔案處理流水線**：四步驟整合處理邏輯
- [x] **效能優化策略**：記憶體管理和並行處理

### Phase 1: 檔案格式檢測 (100% 精確度要求)
- [ ] **CTA 格式檢測**：精確實作第7、8、11行的檢測邏輯
- [ ] **測試時間檢測**：FT和EQC檔案的時間欄位識別
- [ ] **座標欄位檢測**：X_COORD、Y_COORD、Alarm欄位定位

### Phase 2: 資料匹配算法 (三種模式)
- [ ] **一般模式匹配**：序號直接對應
- [ ] **CTA8290模式**：Part_No + Dut_No + Site_No匹配
- [ ] **CTA8280模式**：Part_No + Dut_No + Site_No匹配 + 欄位過濾

### Phase 3: 資料清理策略 (三種策略)
- [ ] **座標欄位過濾**：bypass1、bypass2、bypass3邏輯
- [ ] **測試時間對齊**：四種組合的處理邏輯
- [ ] **直接複製模式**：無需調整的情況

### Phase 4: 路徑和檔案處理
- [ ] **ReplacePath 精確實作**：路徑轉換邏輯
- [ ] **檔案命名規則**：_EQCFAILDATA.csv後綴
- [ ] **檔案鎖定檢查**：IsFileOpen函數對應

### Phase 5: Excel 處理算法
- [ ] **OpenSummaryFindBig**：特殊的數據範圍檢測
- [ ] **超連結建立**：ConvertToHyperlinks對應
- [ ] **顏色標記**：ColorMatch功能

### Phase 6: 統計計算精確性
- [ ] **差異欄位檢測**：FindDifferentColumn算法
- [ ] **連續整數範圍**：230個欄位的效能優化
- [ ] **BIN分布統計**：精確的計算公式

## 🎯 關鍵技術挑戰

### 1. 多層條件判斷的精確實作
VBA版本包含大量嵌套的if-else邏輯，需要確保每個條件分支都被正確實作。

### 2. 檔案編碼和格式相容性
確保Python版本能正確處理各種編碼格式的CSV檔案。

### 3. 效能優化策略
- 大檔案的分塊處理
- 記憶體使用優化
- 演算法複雜度控制

### 4. 錯誤處理和容錯機制
- 檔案不存在的處理
- 資料格式異常的處理
- 系統資源不足的處理

---

## 🔄 檔案處理與轉換系統

### 1. CSV 到 Excel 的完整轉換流程

基於已實作的 AdvancedPerformanceManager 系統，提供完整的檔案處理解決方案。詳細的 FT-EQC 檔案配對機制請參考：**[3.1 FT-EQC 檔案配對機制實作規格](./3.1_FT_EQC_檔案配對機制實作規格.md)**

*深度分析版本: v3.2 | 最後更新: 2025-06-08 | 基於逐行程式碼分析 + 檔案發現與分類系統 + FT-EQC配對機制完整實作*

## 📚 函數對照表

### 檔案處理核心函數 Python 實作對照

| VBA 函數 | Python 實作 | 檔案位置 | 說明 |
|---------|-------------|----------|------|
| `FindALLCSVFiles` | `CSVFileDiscovery.find_all_csv_files()` | ft_eqc_grouping_processor.py:33 | 遞迴掃描資料夾找所有CSV檔案 |
| `RemoveDuplicateCSVFiles` | `CSVFileDiscovery._remove_duplicates()` | ft_eqc_grouping_processor.py:48 | 基於檔案名稱去重 |
| `FindALLEQCFILE` | `CSVFileDiscovery.classify_eqc_files()` | ft_eqc_grouping_processor.py:71 | 多層檢測規則分類EQC檔案 |
| `FindALLFTFILE` | `CSVFileDiscovery.classify_ft_files()` | ft_eqc_grouping_processor.py:61 | 多標記支援分類FT檔案 |
| `CopyRowsToNewFile` | `OnlineEQCFailProcessor._copy_rows_to_new_file()` | ft_eqc_grouping_processor.py:377 | FT與EQC失敗資料配對 |
| `FindmatchedCSV` | `TimeBasedMatcher.match_files()` | ft_eqc_grouping_processor.py:127 | 時間戳檔案配對 |
| `FindOnlieEQCFAILFiles` | `OnlineEQCFailProcessor.find_online_eqc_fail_files()` | ft_eqc_grouping_processor.py:329 | Online EQC失敗檔案識別 |
| `FindOnlieEQCBin1datalog` | `OnlineEQCFailProcessor.find_online_eqc_bin1_datalog()` | ft_eqc_grouping_processor.py:438 | EQC BIN1資料提取 |
| `FindEQCFAILDATALOG` | `OnlineEQCFailProcessor.generate_eqc_total_data()` | ft_eqc_grouping_processor.py:607 | EQC失敗資料彙整 |
| `ReplacePath` | `AdvancedEQCProcessor.convert_to_network_path()` | 3.Online_EQC_系統深度實作規格.md:477 | 網路路徑轉換 |
| `AddFTToRowA` | `OnlineEQCFailProcessor._add_ft_to_row()` | ft_eqc_grouping_processor.py:510 | 欄位標記插入 |

**檔案處理流水線完整對照**：
```vba
' VBA 四步驟流水線
csvFilestemp = FindALLCSVFiles(folderPath)
csvFiles = RemoveDuplicateCSVFiles(csvFilestemp) 
csvFileseqc = FindALLEQCFILE(csvFiles)
csvFilesft = FindALLFTFILE(csvFiles)
```

```python
# Python 對應實作（整合優化版）
discovery = CSVFileDiscovery()
csv_files = discovery.find_all_csv_files(folder_path)  # 包含去重
eqc_files = discovery.classify_eqc_files(csv_files)
ft_files = discovery.classify_ft_files(csv_files)
```