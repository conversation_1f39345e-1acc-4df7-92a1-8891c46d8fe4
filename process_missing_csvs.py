#!/usr/bin/env python3
"""
智能批量CSV處理器 - 簡化版
遵循CLAUDE.md規範：檔案≤500行，功能替換原則，使用現有程式

功能：資料夾參數化 + 呼叫現有BatchCsvToExcelProcessor
"""

import argparse
import os
import sys
from pathlib import Path

# 加入現有程式路徑
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from batch_csv_to_excel_processor import BatchCsvToExcelProcessor


def main():
    """
    主執行函數 - 支援命令列參數
    
    使用方式：
    python process_missing_csvs.py                    # 使用預設資料夾
    python process_missing_csvs.py --folder doc/other # 指定資料夾
    """
    parser = argparse.ArgumentParser(
        description='智能批量CSV處理器 - 將資料夾中所有CSV檔案轉換為Excel',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
範例：
  %(prog)s                          使用預設資料夾 doc/20250523
  %(prog)s --folder doc/20250601    處理指定資料夾
  %(prog)s --folder . --force       強制覆蓋現有Excel檔案
        """
    )
    
    parser.add_argument(
        '--folder', 
        default='doc/20250523',
        help='CSV資料夾路徑 (預設: doc/20250523)'
    )
    
    parser.add_argument(
        '--force', 
        action='store_true',
        help='強制覆蓋現有Excel檔案'
    )
    
    args = parser.parse_args()
    
    # 檢查資料夾是否存在
    if not os.path.exists(args.folder):
        print(f"❌ 錯誤：資料夾不存在 - {args.folder}")
        return False
    
    print("🚀 智能批量CSV處理器")
    print("=" * 60)
    print(f"📁 處理資料夾：{args.folder}")
    print(f"🔄 強制覆蓋：{'是' if args.force else '否'}")
    print("=" * 60)
    
    # 使用現有的BatchCsvToExcelProcessor
    processor = BatchCsvToExcelProcessor(enable_logging=True)
    result = processor.process_folder(args.folder, force_overwrite=args.force)
    
    print("\n" + "=" * 60)
    print("🎉 處理完成")
    print("=" * 60)
    
    if result.success:
        print(f"✅ 成功處理：{result.processed_files}/{result.total_files} 個檔案")
        print(f"⏭️ 跳過檔案：{result.skipped_files} 個（已存在）")
        print(f"❌ 失敗檔案：{result.failed_files} 個")
        print(f"⏱️ 總處理時間：{result.processing_time:.2f} 秒")
        return True
    else:
        print(f"❌ 處理失敗：{result.error_message}")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)