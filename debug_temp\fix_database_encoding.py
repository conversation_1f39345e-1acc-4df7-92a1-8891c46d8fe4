#!/usr/bin/env python3
"""
修復資料庫中現有的郵件編碼問題
"""

import sys
from pathlib import Path
from email.header import decode_header
from email.utils import parseaddr

# 添加專案根目錄到 Python 路徑
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.infrastructure.adapters.database.email_database import EmailDatabase
from src.infrastructure.logging.logger_manager import LoggerManager


class DatabaseEncodingFixer:
    """
    資料庫編碼修復器
    """
    
    def __init__(self):
        """初始化修復器"""
        self.logger = LoggerManager().get_logger("DatabaseEncodingFixer")
        self.database = EmailDatabase()
        self.fixed_count = 0
        self.error_count = 0
    
    def decode_mime_header(self, header_value: str) -> str:
        """
        解碼 MIME 編碼的郵件頭
        
        Args:
            header_value: 原始頭部值
            
        Returns:
            解碼後的字串
        """
        if not header_value:
            return header_value
            
        try:
            # 解碼 MIME 編碼
            decoded_parts = decode_header(header_value)
            result = ""
            
            for part, encoding in decoded_parts:
                if isinstance(part, bytes):
                    if encoding:
                        try:
                            result += part.decode(encoding)
                        except (UnicodeDecodeError, LookupError):
                            # 如果指定編碼失敗，嘗試常見編碼
                            for fallback_encoding in ['utf-8', 'big5', 'gbk', 'latin-1']:
                                try:
                                    result += part.decode(fallback_encoding)
                                    break
                                except (UnicodeDecodeError, LookupError):
                                    continue
                            else:
                                result += part.decode('utf-8', errors='replace')
                    else:
                        result += part.decode('utf-8', errors='replace')
                else:
                    result += str(part)
            
            return result.strip()
            
        except Exception as e:
            self.logger.warning(f"解碼失敗: {e}")
            return header_value
    
    def decode_sender(self, sender_value: str) -> str:
        """
        解碼發件人信息
        
        Args:
            sender_value: 原始發件人值
            
        Returns:
            解碼後的發件人信息
        """
        if not sender_value:
            return sender_value
            
        try:
            # <AUTHOR> <EMAIL>
            name, email_addr = parseaddr(sender_value)
            
            # 解碼姓名部分
            if name:
                decoded_name = self.decode_mime_header(name)
                if email_addr:
                    return f"{decoded_name} <{email_addr}>"
                else:
                    return decoded_name
            else:
                return email_addr or sender_value
                
        except Exception as e:
            self.logger.warning(f"解碼發件人失敗: {e}")
            return sender_value
    
    def is_likely_encoded(self, text: str) -> bool:
        """
        檢查文字是否可能是編碼的
        
        Args:
            text: 要檢查的文字
            
        Returns:
            是否可能是編碼的
        """
        if not text:
            return False
            
        # 檢查是否包含 MIME 編碼標記
        if text.startswith("=?") and text.endswith("?="):
            return True
            
        # 檢查是否包含常見的編碼模式
        if "=?" in text and "?=" in text:
            return True
            
        return False
    
    def fix_all_emails(self):
        """修復所有郵件的編碼問題"""
        self.logger.info("開始修復資料庫中的郵件編碼...")
        
        try:
            # 取得所有郵件
            with self.database.get_session() as session:
                from src.infrastructure.adapters.database.models import EmailDB
                
                emails = session.query(EmailDB).all()
                total_emails = len(emails)
                self.logger.info(f"找到 {total_emails} 封郵件需要檢查")
                
                for i, email in enumerate(emails, 1):
                    try:
                        # 檢查並修復主題
                        if self.is_likely_encoded(email.subject):
                            original_subject = email.subject
                            decoded_subject = self.decode_mime_header(email.subject)
                            if decoded_subject != original_subject:
                                email.subject = decoded_subject
                                self.logger.debug(f"修復主題: {original_subject} -> {decoded_subject}")
                                self.fixed_count += 1
                        
                        # 檢查並修復寄件者
                        if self.is_likely_encoded(email.sender):
                            original_sender = email.sender
                            decoded_sender = self.decode_sender(email.sender)
                            if decoded_sender != original_sender:
                                email.sender = decoded_sender
                                self.logger.debug(f"修復寄件者: {original_sender} -> {decoded_sender}")
                                self.fixed_count += 1
                        
                        # 每處理 100 封郵件顯示進度
                        if i % 100 == 0:
                            self.logger.info(f"進度: {i}/{total_emails} ({i/total_emails*100:.1f}%)")
                            session.commit()  # 批次提交
                    
                    except Exception as e:
                        self.logger.error(f"修復郵件 ID {email.id} 時發生錯誤: {e}")
                        self.error_count += 1
                        continue
                
                # 最終提交
                session.commit()
                
                self.logger.info("=" * 50)
                self.logger.info(f"修復完成！")
                self.logger.info(f"總共檢查: {total_emails} 封郵件")
                self.logger.info(f"修復數量: {self.fixed_count} 個欄位")
                self.logger.info(f"錯誤數量: {self.error_count} 個")
                
        except Exception as e:
            self.logger.error(f"修復資料庫時發生錯誤: {e}")
            raise
    
    def fix_senders(self):
        """修復寄件者表中的編碼問題"""
        self.logger.info("開始修復寄件者表...")
        
        try:
            with self.database.get_session() as session:
                from src.infrastructure.adapters.database.models import SenderDB
                
                senders = session.query(SenderDB).all()
                total_senders = len(senders)
                self.logger.info(f"找到 {total_senders} 個寄件者需要檢查")
                
                fixed_senders = 0
                for sender in senders:
                    try:
                        # 檢查並修復顯示名稱
                        if sender.display_name and self.is_likely_encoded(sender.display_name):
                            original_name = sender.display_name
                            decoded_name = self.decode_mime_header(sender.display_name)
                            if decoded_name != original_name:
                                sender.display_name = decoded_name
                                self.logger.debug(f"修復寄件者名稱: {original_name} -> {decoded_name}")
                                fixed_senders += 1
                        
                        # 檢查並修復 email 地址（雖然通常不需要）
                        if self.is_likely_encoded(sender.email_address):
                            original_email = sender.email_address
                            decoded_email = self.decode_sender(sender.email_address)
                            if decoded_email != original_email:
                                sender.email_address = decoded_email
                                self.logger.debug(f"修復 email 地址: {original_email} -> {decoded_email}")
                                fixed_senders += 1
                    
                    except Exception as e:
                        self.logger.error(f"修復寄件者 ID {sender.id} 時發生錯誤: {e}")
                        continue
                
                session.commit()
                self.logger.info(f"寄件者表修復完成，修復了 {fixed_senders} 個記錄")
                
        except Exception as e:
            self.logger.error(f"修復寄件者表時發生錯誤: {e}")
            raise


def main():
    """主函數"""
    print("=" * 60)
    print("📧 郵件資料庫編碼修復工具")
    print("=" * 60)
    print()
    print("此工具將修復資料庫中現有的郵件編碼問題，包括：")
    print("1. MIME 編碼的郵件主題")
    print("2. MIME 編碼的寄件者名稱")
    print()
    
    # 確認執行
    response = input("確定要執行修復嗎？(y/N): ")
    if response.lower() != 'y':
        print("已取消執行")
        return
    
    # 執行修復
    fixer = DatabaseEncodingFixer()
    
    try:
        # 修復郵件表
        fixer.fix_all_emails()
        
        # 修復寄件者表
        fixer.fix_senders()
        
        print("\n✅ 修復完成！")
        
    except Exception as e:
        print(f"\n❌ 修復失敗: {e}")
        return 1
    
    return 0


if __name__ == "__main__":
    sys.exit(main())