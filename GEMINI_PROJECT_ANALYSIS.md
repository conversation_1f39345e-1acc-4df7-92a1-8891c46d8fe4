# Python 郵件處理系統 - 專案分析報告

> 本報告由 Gemini AI 深度分析生成，提供完整的專案架構、技術細節和開發指南

## 1. 專案概述

本專案是一個企業級的 Python 郵件處理與資料分析系統，其核心目標是將一個既有的、基於 VBA 的 Excel 郵件處理流程，遷移至一個現代化、可維護、可擴展的 Python 後端系統。

系統的主要業務是自動化處理來自多家半導體測試廠商（如 GTK, ETD, XAHT, JCET, LINGSEN）的電子郵件，執行包括郵件內容解析、附件檔案處理、資料轉換、統計分析和自動化報告生成在內的一系列複雜任務。

### 核心目標與價值
- **自動化與效率**: 取代手動 VBA 操作，實現7x24小時全自動化處理流程。
- **可維護性與擴展性**: 採用現代軟體架構（六角架構），使系統易於理解、維護和擴展新功能或新廠商。
- **可測試性**: 強調測試驅動開發（TDD），確保系統的穩定性和可靠性，目標測試覆蓋率 > 90%。
- **數據一致性**: 透過標準化流程確保資料處理結果的一致性和準確性。
- **性能提升**: 利用 Python 的性能優勢和向量化計算，大幅提升資料處理效率。

### 技術棧概覽
- **後端語言**: Python 3.11+
- **Web 框架**: FastAPI
- **資料處理**: Pandas, NumPy
- **Excel 操作**: XlsxWriter, openpyxl
- **資料驗證**: Pydantic
- **日誌系統**: Loguru
- **測試框架**: Pytest
- **程式碼品質**: Black, Flake8, MyPy

## 2. 架構設計

本專案採用了清晰的 **六角架構（Hexagonal Architecture）**，也稱為「埠與介面卡架構」，旨在將核心業務邏輯與外部基礎設施（如資料庫、API、檔案系統）完全解耦。

### 架構分層圖
```
+---------------------------------------------------------------------------------+
|                                  外部世界 (使用者、其他系統)                      |
+---------------------------------------------------------------------------------+
       |                                      ^
       v (埠 - Ports)                         | (介面卡 - Adapters)
+---------------------------------------------------------------------------------+
|                                  展示層 (Presentation)                          |
|   - API (FastAPI)                             - Web UI (HTML/JS)                |
|   - CLI (命令列介面)                                                            |
+---------------------------------------------------------------------------------+
       |                                      ^
       v (埠 - Ports)                         | (介面卡 - Adapters)
+---------------------------------------------------------------------------------+
|                                  應用層 (Application)                           |
|   - Use Cases (使用案例，如 ProcessEmailUseCase)                                |
|   - Interfaces (定義外部服務的抽象介面)                                         |
+---------------------------------------------------------------------------------+
       |                                      ^
       v (領域服務)                           | (領域事件)
+---------------------------------------------------------------------------------+
|                                  領域層 (Domain)                                |
|   - Entities (核心實體，如 Email, Vendor)                                     |
|   - Value Objects (值物件，如 EmailAddress)                                   |
|   - Domain Services (領域服務，如 ParsingService)                             |
|   - Domain Exceptions (自訂領域例外)                                          |
+---------------------------------------------------------------------------------+
       |                                      ^
       v (埠 - Ports)                         | (介面卡 - Adapters)
+---------------------------------------------------------------------------------+
|                                基礎設施層 (Infrastructure)                      |
|   - 資料庫 (PostgreSQL/SQLite)                - 檔案系統 (本地/網路)            |
|   - 郵件客戶端 (Outlook COM/IMAP)             - 外部 API                      |
|   - 日誌系統 (Loguru)                         - 配置管理 (Pydantic)             |
+---------------------------------------------------------------------------------+
```

### 各層職責詳解

#### 領域層 (`src/domain`)
- **職責**: 包含所有核心業務邏輯、規則和狀態，是系統的心臟。
- **特點**: 完全獨立，不依賴任何外部技術。
- **主要組件**:
    - `entities`: 核心業務實體，如 `Email`, `Vendor`。
    - `value_objects`: 不可變的資料結構，如 `EmailAddress`。
    - `services`: 跨多個實體的領域邏輯。
    - `exceptions`: 自訂的業務例外。

#### 應用層 (`src/application`)
- **職責**: 協調領域層的實體和服務來完成具體的使用案例（Use Case）。
- **特點**: 定義系統能做什麼，但不關心如何做。
- **主要組件**:
    - `use_cases`: 實現具體的業務流程，如 `ProcessEmailUseCase`。
    - `interfaces`: 定義與外部世界（基礎設施層）溝通的「埠」（Ports），如 `EmailReader` 介面。

#### 基礎設施層 (`src/infrastructure`)
- **職責**: 提供外部技術的具體實現，是應用層介面的「介面卡」（Adapters）。
- **特點**: 處理所有與外部世界的互動，如資料庫存取、檔案讀寫、API 呼叫。
- **主要組件**:
    - `adapters`: 實現應用層定義的介面，如 `OutlookAdapter` 實現 `EmailReader`。
    - `parsers`: 針對不同廠商的郵件解析器。
    - `config`: 配置管理系統。
    - `logging`: 日誌系統。

#### 展示層 (`src/presentation`)
- **職責**: 作為使用者或其他系統與應用層互動的入口。
- **特點**: 處理 HTTP 請求、渲染 Web 介面、執行命令列指令。
- **主要組件**:
    - `api`: FastAPI 端點定義。
    - `web`: HTML 模板和靜態資源（CSS/JS）。
    - `cli`: 命令列介面（未來擴展）。

## 3. 核心功能模組

系統的核心功能圍繞著 EQC（Electronic Quality Control）和 FT（Final Test）資料的自動化處理。

### EQC 一鍵完成處理流程
這是系統最核心、最複雜的功能，整合了多個子處理步驟，實現從原始資料到最終分析報告的全自動化。

**UI 入口**: `ft_eqc_grouping_ui_modular.html` -> "一鍵完成程式碼對比" 按鈕
**API 端點**: `/api/process_eqc_advanced`

**完整流程**:
1.  **第一階段：EQCTOTALDATA 生成**
    -   **模組**: `eqc_bin1_final_processor.py`
    -   **功能**: 自動發現並整合目錄下的所有 CSV 檔案，包括 `.spd` 檔案的自動轉換，並執行 FT-EQC 智能配對，最終生成包含所有相關資料的 `EQCTOTALDATA.csv`。
2.  **第二階段：程式碼區間檢測與分析**
    -   **模組**: `eqc_standard_processor.py` 作為控制器，協調以下模組：
    -   `eqc_simple_detector.py`: 檢測主要和備用程式碼區間。
    -   `eqc_dual_search_corrected.py`: 執行雙重搜尋機制，驗證程式碼一致性。
    -   `eqc_inseqcrtdata2_processor.py`: 執行資料重組，將同一顆 IC 的 EQC RT 資料移動到對應的 FAIL 行下方。
    -   `eqc_step5_testflow_processor.py`: 根據 Step 4 的匹配日誌，生成線性測試流程。
    -   `eqc_step6_excel_processor.py`: 將 Step 5 的結果轉換為 Excel，並高亮顯示 RT 行。
    -   `csv_to_excel_converter.py`: 最終生成包含完整 Summary 的 Excel 報告。

### 檔案管理與上傳
- **UI 入口**: 檔案上傳區塊
- **API 端點**: `/api/upload_archive`, `/api/download_file`, `/api/today_processed_files`
- **功能**:
    -   支援 `.zip`, `.7z`, `.rar` 等多種壓縮格式上傳與自動解壓縮。
    -   提供今日處理記錄的查詢、顯示和下載功能。
    -   實現了重複上傳防護機制。

### FT Summary 批量處理
- **UI 入口**: `ft_summary_ui.html`
- **API 端點**: `/api/process_ft_summary`
- **功能**:
    -   遞迴掃描指定資料夾中的所有 CSV 檔案。
    -   **MD5 去重**: 確保內容相同的檔案只處理一次。
    -   **時間戳排序**: 確保橫向整併時資料按時間順序排列。
    -   **雙模式處理**: 提供「完整模式」和「快速模式（僅Summary）」以滿足不同需求。

## 4. 技術實作細節

### 配置管理
- **檔案**: `src/infrastructure/config/settings.py`, `config_manager.py`
- **技術**: 使用 Pydantic 進行強型別的配置定義與驗證。
- **特色**: 支援從 `.env` 檔案和環境變數載入，實現多環境（開發、測試、生產）配置隔離。

### 日誌系統
- **檔案**: `src/infrastructure/logging/logger_manager.py`
- **技術**: 基於 Loguru 實現高度客製化的日誌系統。
- **特色**:
    -   **彩色日誌**: 不同級別使用不同顏色，符合 `GEMINI.md` 的特殊要求。
    -   **呼叫者資訊**: 日誌自動包含檔案名、函式名和行號，便於除錯。
    -   **結構化日誌**: 支援 JSON 格式輸出，便於日誌分析系統整合。

### 錯誤處理
- **檔案**: `src/domain/exceptions/base.py`
- **技術**: 建立自訂的例外處理體系，繼承自 Python 的 `Exception`。
- **特色**:
    -   **分層例外**: 定義了 `DomainException`, `EmailProcessingException`, `ValidationException` 等，使錯誤類型更明確。
    -   **統一格式**: 所有自訂例外都包含 `error_code` 和 `details`，便於 API 回應和問題追蹤。

### 向量化與性能優化
- **檔案**: `csv_to_excel_converter.py`
- **技術**: 大量使用 Pandas 和 NumPy 的向量化操作。
- **應用**:
    -   **BIN1 保護機制**: 將逐行檢查改為批量陣列操作，性能提升數倍。
    -   **設備 BIN 分類**: 使用 NumPy 的廣播和遮罩（masking）功能，實現高效的上下限檢查和 BIN 分配。
- **成果**: 核心處理步驟的性能達到每秒百萬次比較級別。

## 5. 開發流程

專案嚴格遵循 `GEMINI.md` 和 `Makefile` 中定義的開發流程。

### TDD (測試驅動開發)
1.  **Red**: 先在 `tests/` 目錄下編寫失敗的測試案例。
2.  **Green**: 在 `src/` 目錄下編寫最少的程式碼讓測試通過。
3.  **Refactor**: 重構程式碼，提升品質和性能，同時確保所有測試依然通過。

### 開發命令
開發者使用 `make` 命令來執行標準化操作：
- `make install`: 安裝所有開發依賴。
- `make test`: 執行所有單元測試和整合測試。
- `make quality-check`: 執行程式碼格式化（Black）、風格檢查（Flake8）和型別檢查（MyPy）。
- `make all-tests`: 執行所有測試和品質檢查，是提交前的必要步驟。

### 核心開發原則
- **反假測試 (Anti-Fake Testing)**: 所有功能必須經過真實世界場景的驗證，例如檔案操作必須確認檔案時間戳和大小的變化。
- **功能替換 (No Backward Compatibility)**: 新功能一旦完成，必須徹底移除舊的、重複的程式碼，確保程式碼庫的簡潔。
- **後端程式測試**: 所有後端邏輯除了單元測試，還必須通過 `curl` 或直接執行 Python 腳本進行程式級的測試驗證。

## 6. 測試策略

### 測試層次
- **單元測試 (`tests/unit`)**: 測試單一函數或類別的邏輯，不依賴外部服務。
- **整合測試 (`tests/integration`)**: 測試多個模組之間的協作，如服務層與基礎設施層的互動。
- **端到端測試 (`tests/e2e`)**: 使用 Playwright 模擬使用者從 UI 到後端的完整操作流程。

### 測試工具與覆蓋率
- **測試框架**: Pytest
- **覆蓋率工具**: pytest-cov
- **目標覆蓋率**: > 90%

### 測試驗證
- **CI/CD**: 每次提交都會觸發自動化測試流程。
- **本地驗證**: 開發者在提交前必須執行 `make all-tests`。

## 7. 部署方案

### 容器化部署
專案已為容器化部署做好準備，提供了 `docker-compose.yml` 檔案（雖然未在上下文中提供，但從文件推斷存在）。
- **服務容器**: FastAPI 應用程式、PostgreSQL 資料庫、Nginx 反向代理。
- **監控容器**: Prometheus 指標收集、Grafana 視覺化儀表板。

### 啟動方式
- **開發環境**: `make dev-setup` 後，使用 `uvicorn` 或 `api_integration.py` 啟動。
- **生產環境**: 透過 `docker-compose up -d` 啟動所有服務。

## 8. 未來規劃

### 功能擴展
- **Web UI 增強**: 引入現代前端框架（如 React/Vue）以提升使用者互動體驗。
- **智慧化分析**: 整合機器學習模型，進行更深入的良率預測和異常原因分析。
- **報表客製化**: 提供使用者自訂報表格式和內容的功能。

### 架構優化
- **非同步任務佇列**: 引入 Celery 或 Redis Queue 處理長時間執行的任務，避免 API 超時。
- **快取機制**: 使用 Redis 為常用查詢和計算結果提供快取，提升響應速度。
- **微服務化**: 考慮將核心處理功能（如 EQC 分析、FT Summary）拆分為獨立的微服務。

### 維護與監控
- **CI/CD 完善**: 建立完整的持續整合與持續部署流水線。
- **日誌分析**: 整合 ELK Stack (Elasticsearch, Logstash, Kibana) 進行集中式日誌管理與分析。
- **告警系統**: 在 Grafana 中設定關鍵指標的告警規則，及時通知異常。

## 9. 郵件系統整合

### 多協議支援
專案支援多種郵件存取協議，適應不同的企業環境：

#### COM API 方式 (Windows)
- **檔案**: `src/infrastructure/adapters/outlook/outlook_adapter.py`
- **特色**: 支援 Outlook 2016+ 版本，提供即時監控和完整功能
- **限制**: 僅支援 Windows 平台，依賴 `pywin32` 套件

#### IMAP 協議 (跨平台)
- **檔案**: `test_imap_simple.py`
- **特色**: 跨平台支援，適用於 WSL 和 Linux 環境
- **功能**: 支援 SSL 連接、中文編碼、附件檢查

#### POP3/SMTP 方式 (企業內部)
- **檔案**: `test_pop3_company.py`, `email_config.py`
- **特色**: 專為企業內部郵件伺服器設計
- **配置**: 支援自訂端口、SSL/TLS 設定

### 配置管理
- **設定檔**: `.env` 檔案統一管理所有郵件設定
- **驗證機制**: 自動檢查配置完整性和格式正確性
- **環境隔離**: 支援開發、測試、生產環境的不同配置

## 10. 效能監控與優化

### 效能指標
- **處理時間**: EQC 一鍵完成約 8.5 秒
- **檔案大小**: 輸出 Excel 約 632.3 KB
- **記憶體使用**: 向量化操作大幅降低記憶體消耗
- **測試覆蓋率**: 目標 > 90%

### 優化技術
- **向量化計算**: 使用 Pandas/NumPy 批次處理
- **檔案快取**: MD5 去重避免重複處理
- **非同步處理**: 長時間任務使用背景執行

### 監控工具
- **日誌系統**: Loguru 彩色結構化日誌
- **效能追蹤**: 關鍵操作的時間和資源消耗記錄
- **錯誤追蹤**: 完整的異常堆疊和上下文資訊

---

## 結論

這個 Python 郵件處理系統代表了現代軟體工程的最佳實踐，從 VBA 的單體架構成功轉型為模組化、可測試、可擴展的六角架構。透過嚴格的 TDD 開發流程、完善的測試策略和持續的效能優化，系統不僅提供了穩定可靠的業務功能，更建立了一個可持續發展的技術基礎。

對於新開發者而言，建議先熟悉核心的領域模型和業務流程，再逐步深入各個技術模組的實作細節。同時，嚴格遵循專案的開發規範和測試要求，確保程式碼品質和系統的長期可維護性。

---

*本報告由 Gemini AI 深度分析生成，提供了全面的專案理解和技術指導。*