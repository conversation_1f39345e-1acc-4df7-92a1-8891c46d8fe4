# models.py

FT-EQC 分組 API 資料模型模組，使用 Pydantic 進行資料驗證和序列化。

## FTEQCGroupingRequest

FT-EQC 分組處理請求模型，繼承自 BaseModel。

### 屬性
- `folder_path` (str): 要處理的資料夾路徑，必填，最小長度為1

### validate_folder_path

驗證資料夾路徑不能為空的驗證器。

**參數:**
- `v` (str): 資料夾路徑

**返回值:**
- str: 去除前後空白的資料夾路徑

**異常:**
- ValueError: 當資料夾路徑為空時拋出

## StatisticsData

統計資料模型，繼承自 BaseModel，用於記錄處理統計資訊。

### 屬性
- `total_csv_files` (int): 總 CSV 檔案數，必填，必須大於等於0
- `ft_files_count` (int): FT 檔案數量，必填，必須大於等於0
- `eqc_files_count` (int): EQC 檔案數量，必填，必須大於等於0
- `successful_matches` (int): 成功配對數量，必填，必須大於等於0
- `eqc_rt_count` (int): EQC RT 檔案數量（未配對的 EQC 檔案），必填，必須大於等於0
- `matching_rate` (float): 配對成功率，必填，範圍為0.0到1.0
- `processing_timestamp` (str): 處理時間戳記，必填

## FailDetail

失敗檔案詳細資訊模型，繼承自 BaseModel。

### 屬性
- `file_path` (str): 失敗檔案路徑，必填
- `fail_count` (int): 該檔案的失敗數量，必填，必須大於等於0
- `first_fail_row` (int): 第一個失敗行的A欄值，必填，必須大於等於0

## EQCFailResult

Online EQC 失敗檔案結果模型，繼承自 BaseModel。

### 屬性
- `fail_count` (int): 失敗檔案數量，必填，必須大於等於0
- `fail_files` (List[str]): 失敗檔案列表，預設為空列表
- `analysis_files` (List[str]): 分析檔案列表，預設為空列表

**注意:** 此模型還包含其他屬性，但由於檔案內容較長，僅列出主要屬性。完整的模型定義包含更多欄位和驗證規則。
