/**
 * 郵件收件夾 JavaScript 功能
 * 提供郵件列表、搜尋、同步等互動功能
 */

class EmailInbox {
    constructor(initialData = {}) {
        this.statistics = initialData.statistics || {};
        this.senders = initialData.senders || [];
        this.currentSender = 'all';
        this.currentPage = 1;
        this.pageSize = 50;
        this.totalCount = 0;
        this.selectedEmails = new Set();
        this.emails = [];
        this.isLoading = false;
        this.autoSyncEnabled = false;
        this.autoSyncInterval = null;
        
        // DOM 元素
        this.elements = {};
        
        // 事件監聽器
        this.eventListeners = new Map();
    }
    
    /**
     * 初始化郵件收件夾
     */
    initialize() {
        this.initializeElements();
        this.setupEventListeners();
        this.loadEmails();
        this.updateStatistics();
        this.setupAutoSync();
    }
    
    /**
     * 初始化 DOM 元素
     */
    initializeElements() {
        this.elements = {
            // 統計元素
            totalEmails: document.getElementById('total-emails'),
            unreadEmails: document.getElementById('unread-emails'),
            totalSenders: document.getElementById('total-senders'),
            allCount: document.getElementById('all-count'),
            
            // 按鈕元素
            syncBtn: document.getElementById('sync-btn'),
            autoSyncBtn: document.getElementById('auto-sync-btn'),
            settingsBtn: document.getElementById('settings-btn'),
            searchBtn: document.getElementById('search-btn'),
            
            // 輸入元素
            searchInput: document.getElementById('search-input'),
            sortSelect: document.getElementById('sort-select'),
            unreadOnlyCheckbox: document.getElementById('unread-only'),
            selectAllCheckbox: document.getElementById('select-all'),
            
            // 容器元素
            syncStatus: document.getElementById('sync-status'),
            emailList: document.getElementById('email-list'),
            emailDetail: document.getElementById('email-detail'),
            batchActions: document.getElementById('batch-actions'),
            
            // 分頁元素
            pageStart: document.getElementById('page-start'),
            pageEnd: document.getElementById('page-end'),
            totalCountSpan: document.getElementById('total-count'),
            prevPageBtn: document.getElementById('prev-page'),
            nextPageBtn: document.getElementById('next-page'),
            pageNumbers: document.getElementById('page-numbers'),
            
            // 對話框元素
            loadingOverlay: document.getElementById('loading-overlay'),
            confirmDialog: document.getElementById('confirm-dialog'),
            notification: document.getElementById('notification'),
            
            // 其他元素
            selectedCount: document.getElementById('selected-count'),
            detailSubject: document.getElementById('detail-subject'),
            detailSender: document.getElementById('detail-sender'),
            detailTime: document.getElementById('detail-time'),
            detailAttachments: document.getElementById('detail-attachments'),
            detailBody: document.getElementById('detail-body'),
            closeDetailBtn: document.getElementById('close-detail')
        };
    }
    
    /**
     * 設置事件監聽器
     */
    setupEventListeners() {
        // 同步按鈕
        this.addEventListenerSafe(this.elements.syncBtn, 'click', () => this.syncEmails());
        
        // 自動同步按鈕
        this.addEventListenerSafe(this.elements.autoSyncBtn, 'click', () => this.toggleAutoSync());
        
        // 搜尋功能
        this.addEventListenerSafe(this.elements.searchBtn, 'click', () => this.searchEmails());
        this.addEventListenerSafe(this.elements.searchInput, 'keypress', (e) => {
            if (e.key === 'Enter') this.searchEmails();
        });
        
        // 篩選和排序
        this.addEventListenerSafe(this.elements.sortSelect, 'change', () => this.loadEmails());
        this.addEventListenerSafe(this.elements.unreadOnlyCheckbox, 'change', () => this.loadEmails());
        
        // 全選功能
        this.addEventListenerSafe(this.elements.selectAllCheckbox, 'change', (e) => this.toggleSelectAll(e.target.checked));
        
        // 分頁按鈕
        this.addEventListenerSafe(this.elements.prevPageBtn, 'click', () => this.previousPage());
        this.addEventListenerSafe(this.elements.nextPageBtn, 'click', () => this.nextPage());
        
        // 批量操作
        this.addEventListenerSafe(document.getElementById('batch-mark-read'), 'click', () => this.batchMarkRead());
        this.addEventListenerSafe(document.getElementById('batch-delete'), 'click', () => this.batchDelete());
        this.addEventListenerSafe(document.getElementById('batch-process'), 'click', () => this.batchProcess());
        
        // 郵件詳情
        this.addEventListenerSafe(this.elements.closeDetailBtn, 'click', () => this.closeEmailDetail());
        
        // 對話框
        this.addEventListenerSafe(document.getElementById('dialog-close'), 'click', () => this.closeDialog());
        this.addEventListenerSafe(document.getElementById('dialog-cancel'), 'click', () => this.closeDialog());
        
        // 通知
        this.addEventListenerSafe(document.querySelector('.notification-close'), 'click', () => this.closeNotification());
    }
    
    /**
     * 安全地添加事件監聽器
     */
    addEventListenerSafe(element, event, handler) {
        if (element) {
            element.addEventListener(event, handler);
            this.eventListeners.set(element, { event, handler });
        }
    }
    
    
    
    /**
     * 載入郵件列表
     */
    async loadEmails() {
        if (this.isLoading) return;
        
        this.isLoading = true;
        this.showLoading();
        
        try {
            const params = new URLSearchParams({
                limit: this.pageSize,
                offset: (this.currentPage - 1) * this.pageSize,
                order_desc: 'true'
            });
            
            // 移除寄件者篩選功能 - 顯示所有郵件
            // if (this.currentSender !== 'all') {
            //     params.append('sender', this.currentSender);
            // }
            
            // 添加排序
            const sortValue = this.elements.sortSelect?.value || 'received_time_desc';
            const [field, order] = sortValue.split('_');
            params.append('order_by', field);
            params.append('order_desc', order === 'desc' ? 'true' : 'false');
            
            const response = await fetch(`/api/emails?${params}`);
            const result = await response.json();
            
            if (result.success) {
                this.emails = result.data.emails;
                this.totalCount = result.data.total_count;
                this.renderEmailList();
                this.updatePagination();
            } else {
                this.showNotification('載入郵件失敗: ' + result.message, 'error');
            }
            
        } catch (error) {
            console.error('載入郵件失敗:', error);
            this.showNotification('載入郵件失敗', 'error');
        } finally {
            this.isLoading = false;
            this.hideLoading();
        }
    }
    
    /**
     * 渲染郵件列表
     */
    renderEmailList() {
        if (!this.elements.emailList) return;
        
        if (this.emails.length === 0) {
            this.elements.emailList.innerHTML = `
                <div class="loading-placeholder">
                    <p>沒有郵件</p>
                </div>
            `;
            return;
        }
        
        const emailItems = this.emails.map(email => this.createEmailItem(email)).join('');
        this.elements.emailList.innerHTML = emailItems;
        
        // 重新綁定事件
        this.bindEmailItemEvents();
    }
    
    /**
     * 創建郵件項目 HTML
     */
    createEmailItem(email) {
        const receivedTime = new Date(email.received_time).toLocaleString('zh-TW');
        const isUnread = !email.is_read;
        const isSelected = this.selectedEmails.has(email.id);
        
        return `
            <div class="email-item ${isUnread ? 'unread' : ''} ${isSelected ? 'selected' : ''}" 
                 data-email-id="${email.id}">
                <div class="email-checkbox">
                    <input type="checkbox" ${isSelected ? 'checked' : ''} 
                           onchange="emailInbox.toggleEmailSelection(${email.id}, this.checked)">
                </div>
                <div class="email-sender" title="${this.escapeHtml(email.sender)}">
                    ${this.escapeHtml(this.truncateText(email.sender, 25))}
                </div>
                <div class="email-subject" title="${this.escapeHtml(email.subject)}">
                    ${this.escapeHtml(this.truncateText(email.subject, 50))}
                </div>
                <div class="email-time">
                    ${receivedTime}
                </div>
                <div class="email-attachment">
                    ${email.has_attachments ? `📎 ${email.attachment_count}` : ''}
                </div>
                <div class="email-actions">
                    <button class="action-btn redo" onclick="emailInbox.redoEmail(${email.id})" title="重做">
                        🔄
                    </button>
                    <button class="action-btn delete" onclick="emailInbox.deleteEmail(${email.id})" title="刪除">
                        ❌
                    </button>
                    <button class="action-btn process" onclick="emailInbox.processEmail(${email.id})" title="處理">
                        🚀
                    </button>
                </div>
            </div>
        `;
    }
    
    /**
     * 綁定郵件項目事件
     */
    bindEmailItemEvents() {
        const emailItems = document.querySelectorAll('.email-item');
        emailItems.forEach(item => {
            item.addEventListener('click', (e) => {
                if (e.target.type === 'checkbox' || e.target.classList.contains('action-btn')) {
                    return;
                }
                
                const emailId = parseInt(item.dataset.emailId);
                this.showEmailDetail(emailId);
            });
        });
    }
    
    /**
     * 顯示郵件詳情
     */
    async showEmailDetail(emailId) {
        try {
            const response = await fetch(`/api/emails/${emailId}`);
            const result = await response.json();
            
            if (result.success) {
                const email = result.data;
                this.renderEmailDetail(email);
                this.elements.emailDetail.classList.remove('hidden');
                this.elements.emailDetail.classList.add('show');
            } else {
                this.showNotification('載入郵件詳情失敗: ' + result.message, 'error');
            }
            
        } catch (error) {
            console.error('載入郵件詳情失敗:', error);
            this.showNotification('載入郵件詳情失敗', 'error');
        }
    }
    
    /**
     * 渲染郵件詳情
     */
    renderEmailDetail(email) {
        if (!this.elements.emailDetail) return;
        
        this.elements.detailSubject.textContent = email.subject;
        this.elements.detailSender.textContent = email.sender;
        this.elements.detailTime.textContent = new Date(email.received_time).toLocaleString('zh-TW');
        this.elements.detailBody.textContent = email.body || '(無內容)';
        
        // 附件處理
        if (email.attachments && email.attachments.length > 0) {
            const attachmentHtml = email.attachments.map(att => 
                `<span class="attachment-item">${att.filename} (${this.formatFileSize(att.size_bytes)})</span>`
            ).join(', ');
            this.elements.detailAttachments.innerHTML = attachmentHtml;
        } else {
            this.elements.detailAttachments.textContent = '無附件';
        }
    }
    
    /**
     * 關閉郵件詳情
     */
    closeEmailDetail() {
        this.elements.emailDetail.classList.add('hidden');
        this.elements.emailDetail.classList.remove('show');
    }
    
    /**
     * 同步郵件
     */
    async syncEmails() {
        if (this.isLoading) return;
        
        this.showSyncStatus('正在同步郵件...');
        
        try {
            const response = await fetch('/api/sync', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Cache-Control': 'no-cache',
                    'Pragma': 'no-cache'
                },
                body: JSON.stringify({
                    max_emails: 100
                })
            });
            
            const result = await response.json();
            
            if (result.success) {
                this.showNotification('郵件同步成功', 'success');
                this.loadEmails();
                this.updateStatistics();
            } else {
                this.showNotification('郵件同步失敗: ' + result.message, 'error');
            }
            
        } catch (error) {
            console.error('同步郵件失敗:', error);
            this.showNotification('郵件同步失敗', 'error');
        } finally {
            this.hideSyncStatus();
        }
    }
    
    /**
     * 切換自動同步
     */
    toggleAutoSync() {
        this.autoSyncEnabled = !this.autoSyncEnabled;
        
        if (this.autoSyncEnabled) {
            this.startAutoSync();
            this.elements.autoSyncBtn.textContent = '停止自動同步';
            this.elements.autoSyncBtn.classList.add('active');
        } else {
            this.stopAutoSync();
            this.elements.autoSyncBtn.textContent = '自動同步';
            this.elements.autoSyncBtn.classList.remove('active');
        }
    }
    
    /**
     * 啟動自動同步
     */
    startAutoSync() {
        if (this.autoSyncInterval) {
            clearInterval(this.autoSyncInterval);
        }
        
        this.autoSyncInterval = setInterval(() => {
            this.syncEmails();
        }, 300000); // 5分鐘
    }
    
    /**
     * 停止自動同步
     */
    stopAutoSync() {
        if (this.autoSyncInterval) {
            clearInterval(this.autoSyncInterval);
            this.autoSyncInterval = null;
        }
    }
    
    /**
     * 搜尋郵件
     */
    async searchEmails() {
        const searchTerm = this.elements.searchInput?.value.trim();
        if (!searchTerm) {
            this.loadEmails();
            return;
        }
        
        this.showLoading();
        
        try {
            const params = new URLSearchParams({
                q: searchTerm,
                fields: 'subject,body,sender',
                limit: this.pageSize
            });
            
            if (this.currentSender !== 'all') {
                params.append('sender', this.currentSender);
            }
            
            const response = await fetch(`/api/search?${params}`);
            const result = await response.json();
            
            if (result.success) {
                this.emails = result.data.emails;
                this.totalCount = result.data.total_count;
                this.renderEmailList();
                this.updatePagination();
            } else {
                this.showNotification('搜尋失敗: ' + result.message, 'error');
            }
            
        } catch (error) {
            console.error('搜尋郵件失敗:', error);
            this.showNotification('搜尋郵件失敗', 'error');
        } finally {
            this.hideLoading();
        }
    }
    
    /**
     * 切換郵件選擇狀態
     */
    toggleEmailSelection(emailId, selected) {
        if (selected) {
            this.selectedEmails.add(emailId);
        } else {
            this.selectedEmails.delete(emailId);
        }
        
        this.updateBatchActions();
        this.updateEmailItemSelection(emailId, selected);
    }
    
    /**
     * 全選/取消全選
     */
    toggleSelectAll(selectAll) {
        this.selectedEmails.clear();
        
        if (selectAll) {
            this.emails.forEach(email => {
                this.selectedEmails.add(email.id);
            });
        }
        
        // 更新所有複選框
        const checkboxes = document.querySelectorAll('.email-item input[type="checkbox"]');
        checkboxes.forEach(cb => {
            cb.checked = selectAll;
        });
        
        // 更新項目樣式
        const emailItems = document.querySelectorAll('.email-item');
        emailItems.forEach(item => {
            if (selectAll) {
                item.classList.add('selected');
            } else {
                item.classList.remove('selected');
            }
        });
        
        this.updateBatchActions();
    }
    
    /**
     * 更新郵件項目選擇狀態
     */
    updateEmailItemSelection(emailId, selected) {
        const emailItem = document.querySelector(`[data-email-id="${emailId}"]`);
        if (emailItem) {
            if (selected) {
                emailItem.classList.add('selected');
            } else {
                emailItem.classList.remove('selected');
            }
        }
    }
    
    /**
     * 更新批量操作面板
     */
    updateBatchActions() {
        const selectedCount = this.selectedEmails.size;
        
        if (selectedCount > 0) {
            this.elements.batchActions.classList.remove('hidden');
            this.elements.selectedCount.textContent = selectedCount;
        } else {
            this.elements.batchActions.classList.add('hidden');
        }
    }
    
    /**
     * 批量標記已讀
     */
    async batchMarkRead() {
        if (this.selectedEmails.size === 0) return;
        
        // 實作批量標記已讀邏輯
        console.log('Batch mark read:', Array.from(this.selectedEmails));
        this.showNotification(`已標記 ${this.selectedEmails.size} 封郵件為已讀`, 'success');
    }
    
    /**
     * 批量刪除
     */
    async batchDelete() {
        if (this.selectedEmails.size === 0) return;
        
        const confirmed = await this.showConfirmDialog(
            '確認刪除',
            `您確定要刪除 ${this.selectedEmails.size} 封郵件嗎？此操作無法撤銷。`
        );
        
        if (confirmed) {
            // 實作批量刪除邏輯
            console.log('Batch delete:', Array.from(this.selectedEmails));
            this.showNotification(`已刪除 ${this.selectedEmails.size} 封郵件`, 'success');
            this.selectedEmails.clear();
            this.updateBatchActions();
            this.loadEmails();
        }
    }
    
    /**
     * 批量處理
     */
    async batchProcess() {
        if (this.selectedEmails.size === 0) return;
        
        // 實作批量處理邏輯
        console.log('Batch process:', Array.from(this.selectedEmails));
        this.showNotification(`已啟動 ${this.selectedEmails.size} 封郵件的處理流程`, 'success');
    }
    
    /**
     * 刪除單一郵件
     */
    async deleteEmail(emailId) {
        const confirmed = await this.showConfirmDialog(
            '確認刪除',
            '您確定要刪除這封郵件嗎？此操作無法撤銷。'
        );
        
        if (confirmed) {
            try {
                const response = await fetch(`/api/emails/${emailId}`, {
                    method: 'DELETE'
                });
                
                const result = await response.json();
                
                if (result.success) {
                    this.showNotification('郵件已刪除', 'success');
                    this.loadEmails();
                    this.updateStatistics();
                } else {
                    this.showNotification('刪除失敗: ' + result.message, 'error');
                }
                
            } catch (error) {
                console.error('刪除郵件失敗:', error);
                this.showNotification('刪除郵件失敗', 'error');
            }
        }
    }
    
    /**
     * 重做郵件處理
     */
    async redoEmail(emailId) {
        // 實作重做邏輯
        console.log('Redo email:', emailId);
        this.showNotification('已重新開始處理郵件', 'success');
    }
    
    /**
     * 處理郵件
     */
    async processEmail(emailId) {
        // 實作處理邏輯
        console.log('Process email:', emailId);
        this.showNotification('已啟動郵件處理流程', 'success');
    }
    
    /**
     * 更新統計資訊
     */
    async updateStatistics() {
        try {
            const response = await fetch('/api/statistics');
            const result = await response.json();
            
            if (result.success) {
                this.statistics = result.data;
                this.renderStatistics();
            }
            
        } catch (error) {
            console.error('更新統計資訊失敗:', error);
        }
    }
    
    /**
     * 渲染統計資訊
     */
    renderStatistics() {
        if (this.elements.totalEmails) {
            this.elements.totalEmails.textContent = this.statistics.total_emails || 0;
        }
        if (this.elements.unreadEmails) {
            this.elements.unreadEmails.textContent = this.statistics.unread_emails || 0;
        }
        if (this.elements.totalSenders) {
            this.elements.totalSenders.textContent = this.statistics.total_senders || 0;
        }
        if (this.elements.allCount) {
            this.elements.allCount.textContent = this.statistics.total_emails || 0;
        }
    }
    
    /**
     * 更新分頁
     */
    updatePagination() {
        const start = (this.currentPage - 1) * this.pageSize + 1;
        const end = Math.min(this.currentPage * this.pageSize, this.totalCount);
        
        if (this.elements.pageStart) this.elements.pageStart.textContent = start;
        if (this.elements.pageEnd) this.elements.pageEnd.textContent = end;
        if (this.elements.totalCountSpan) this.elements.totalCountSpan.textContent = this.totalCount;
        
        // 更新分頁按鈕狀態
        if (this.elements.prevPageBtn) {
            this.elements.prevPageBtn.disabled = this.currentPage <= 1;
        }
        if (this.elements.nextPageBtn) {
            this.elements.nextPageBtn.disabled = end >= this.totalCount;
        }
    }
    
    /**
     * 上一頁
     */
    previousPage() {
        if (this.currentPage > 1) {
            this.currentPage--;
            this.loadEmails();
        }
    }
    
    /**
     * 下一頁
     */
    nextPage() {
        const maxPage = Math.ceil(this.totalCount / this.pageSize);
        if (this.currentPage < maxPage) {
            this.currentPage++;
            this.loadEmails();
        }
    }
    
    /**
     * 顯示載入狀態
     */
    showLoading() {
        if (this.elements.loadingOverlay) {
            this.elements.loadingOverlay.classList.remove('hidden');
        }
    }
    
    /**
     * 隱藏載入狀態
     */
    hideLoading() {
        if (this.elements.loadingOverlay) {
            this.elements.loadingOverlay.classList.add('hidden');
        }
    }
    
    /**
     * 顯示同步狀態
     */
    showSyncStatus(message) {
        if (this.elements.syncStatus) {
            this.elements.syncStatus.querySelector('.status-text').textContent = message;
            this.elements.syncStatus.classList.remove('hidden');
        }
    }
    
    /**
     * 隱藏同步狀態
     */
    hideSyncStatus() {
        if (this.elements.syncStatus) {
            this.elements.syncStatus.classList.add('hidden');
        }
    }
    
    /**
     * 顯示確認對話框
     */
    showConfirmDialog(title, message) {
        return new Promise((resolve) => {
            const dialog = this.elements.confirmDialog;
            if (!dialog) {
                resolve(false);
                return;
            }
            
            dialog.querySelector('#dialog-title').textContent = title;
            dialog.querySelector('#dialog-message').textContent = message;
            dialog.classList.remove('hidden');
            
            const confirmBtn = dialog.querySelector('#dialog-confirm');
            const cancelBtn = dialog.querySelector('#dialog-cancel');
            
            const handleConfirm = () => {
                dialog.classList.add('hidden');
                confirmBtn.removeEventListener('click', handleConfirm);
                cancelBtn.removeEventListener('click', handleCancel);
                resolve(true);
            };
            
            const handleCancel = () => {
                dialog.classList.add('hidden');
                confirmBtn.removeEventListener('click', handleConfirm);
                cancelBtn.removeEventListener('click', handleCancel);
                resolve(false);
            };
            
            confirmBtn.addEventListener('click', handleConfirm);
            cancelBtn.addEventListener('click', handleCancel);
        });
    }
    
    /**
     * 關閉對話框
     */
    closeDialog() {
        if (this.elements.confirmDialog) {
            this.elements.confirmDialog.classList.add('hidden');
        }
    }
    
    /**
     * 顯示通知
     */
    showNotification(message, type = 'info') {
        if (!this.elements.notification) return;
        
        const notification = this.elements.notification;
        notification.className = `notification ${type}`;
        notification.querySelector('.notification-text').textContent = message;
        notification.classList.remove('hidden');
        
        // 自動隱藏
        setTimeout(() => {
            this.closeNotification();
        }, 5000);
    }
    
    /**
     * 關閉通知
     */
    closeNotification() {
        if (this.elements.notification) {
            this.elements.notification.classList.add('hidden');
        }
    }
    
    /**
     * 設置自動同步
     */
    setupAutoSync() {
        // 可以在這裡添加自動同步的初始化邏輯
    }
    
    /**
     * 工具方法：截斷文字
     */
    truncateText(text, maxLength) {
        if (!text) return '';
        if (text.length <= maxLength) return text;
        return text.substring(0, maxLength) + '...';
    }
    
    /**
     * 工具方法：HTML轉義
     */
    escapeHtml(text) {
        if (!text) return '';
        return text
            .replace(/&/g, '&amp;')
            .replace(/</g, '&lt;')
            .replace(/>/g, '&gt;')
            .replace(/"/g, '&quot;')
            .replace(/'/g, '&#39;');
    }
    
    /**
     * 工具方法：格式化檔案大小
     */
    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
    
    /**
     * 清理資源
     */
    cleanup() {
        // 清理事件監聽器
        this.eventListeners.forEach(({ element, event, handler }) => {
            element.removeEventListener(event, handler);
        });
        this.eventListeners.clear();
        
        // 清理自動同步
        this.stopAutoSync();
    }
}

/**
 * 郵件詳情頁面類
 */
class EmailDetail {
    constructor(emailData) {
        this.email = emailData;
        this.elements = {};
    }
    
    initialize() {
        this.initializeElements();
        this.setupEventListeners();
        this.renderEmailDetail();
    }
    
    initializeElements() {
        this.elements = {
            markReadBtn: document.getElementById('mark-read-btn'),
            deleteBtn: document.getElementById('delete-btn'),
            processBtn: document.getElementById('process-btn'),
            startEqcBtn: document.getElementById('start-eqc-process'),
            generateReportBtn: document.getElementById('generate-report'),
            codeAnalysisBtn: document.getElementById('code-analysis'),
            openGtkBtn: document.getElementById('open-gtk'),
            ftSummaryBtn: document.getElementById('ft-summary'),
            oneClickBtn: document.getElementById('one-click-complete')
        };
    }
    
    setupEventListeners() {
        // 郵件操作
        this.elements.markReadBtn?.addEventListener('click', () => this.markAsRead());
        this.elements.deleteBtn?.addEventListener('click', () => this.deleteEmail());
        this.elements.processBtn?.addEventListener('click', () => this.processEmail());
        
        // 業務流程操作
        this.elements.startEqcBtn?.addEventListener('click', () => this.startEqcProcess());
        this.elements.generateReportBtn?.addEventListener('click', () => this.generateReport());
        this.elements.codeAnalysisBtn?.addEventListener('click', () => this.codeAnalysis());
        
        // 系統整合
        this.elements.openGtkBtn?.addEventListener('click', () => this.openGtkInterface());
        this.elements.ftSummaryBtn?.addEventListener('click', () => this.openFtSummary());
        this.elements.oneClickBtn?.addEventListener('click', () => this.oneClickComplete());
    }
    
    renderEmailDetail() {
        // 郵件詳情已在 HTML 中渲染
        // 這裡可以添加動態內容更新
    }
    
    // 業務操作方法
    async markAsRead() {
        console.log('Mark as read:', this.email.id);
    }
    
    async deleteEmail() {
        console.log('Delete email:', this.email.id);
    }
    
    async processEmail() {
        console.log('Process email:', this.email.id);
    }
    
    async startEqcProcess() {
        console.log('Start EQC process:', this.email.id);
    }
    
    async generateReport() {
        console.log('Generate report:', this.email.id);
    }
    
    async codeAnalysis() {
        console.log('Code analysis:', this.email.id);
    }
    
    async openGtkInterface() {
        console.log('Open GTK interface:', this.email.id);
    }
    
    async openFtSummary() {
        console.log('Open FT Summary:', this.email.id);
    }
    
    async oneClickComplete() {
        console.log('One click complete:', this.email.id);
    }
}

// 全局實例
let emailInbox = null;

// 頁面載入完成後初始化
document.addEventListener('DOMContentLoaded', function() {
    // 如果有初始化數據，在 HTML 中已經處理
    // 這裡是備用的初始化邏輯
    if (typeof window.emailInbox === 'undefined' && window.location.pathname === '/') {
        emailInbox = new EmailInbox();
        emailInbox.initialize();
    }
});