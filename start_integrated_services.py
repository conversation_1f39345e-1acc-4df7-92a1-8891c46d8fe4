#!/usr/bin/env python3
"""
整合服務啟動腳本
同時啟動 Flask (郵件收件夾) + FastAPI (FT-EQC 處理) 服務
"""

import os
import subprocess
import sys
import time
import signal
from pathlib import Path

# 設定預設端口
FLASK_PORT = 5000
FASTAPI_PORT = 8010

def start_integrated_service():
    """啟動整合服務"""
    print("🚀 啟動整合服務...")
    
    try:
        # 使用整合的應用程式
        process = subprocess.Popen([
            sys.executable, "email_inbox_app.py",
            "--host", "0.0.0.0",
            "--port", str(FLASK_PORT),
            "--fastapi-port", str(FASTAPI_PORT),
            "--auto-sync",
            "--sync-interval", "300"
        ], stdout=subprocess.PIPE, stderr=subprocess.STDOUT, text=True)
        
        print(f"✅ 整合服務已啟動 (PID: {process.pid})")
        print(f"📧 郵件收件夾: http://localhost:{FLASK_PORT}")
        print(f"🚀 FT-EQC 處理: http://localhost:{FASTAPI_PORT}/ui")
        print(f"📚 FT-EQC API 文檔: http://localhost:{FASTAPI_PORT}/docs")
        print("\n按 Ctrl+C 停止服務...")
        
        # 實時輸出日誌
        for line in iter(process.stdout.readline, ''):
            print(line.strip())
        
        process.wait()
        
    except KeyboardInterrupt:
        print("\n🛑 正在停止服務...")
        process.terminate()
        process.wait()
        print("✅ 服務已停止")
    except Exception as e:
        print(f"❌ 啟動服務失敗: {e}")

def start_separate_services():
    """分別啟動兩個服務（傳統方式）"""
    print("🚀 啟動分離服務...")
    
    processes = []
    
    try:
        # 啟動 Flask 服務
        flask_process = subprocess.Popen([
            sys.executable, "email_inbox_app.py",
            "--host", "0.0.0.0",
            "--port", str(FLASK_PORT),
            "--no-fastapi"
        ])
        processes.append(("Flask", flask_process))
        
        # 等待 Flask 服務啟動
        time.sleep(2)
        
        # 啟動 FastAPI 服務
        fastapi_process = subprocess.Popen([
            sys.executable, "-m", "uvicorn",
            "src.presentation.api.ft_eqc_api:app",
            "--host", "0.0.0.0",
            "--port", str(FASTAPI_PORT),
            "--workers", "1"
        ])
        processes.append(("FastAPI", fastapi_process))
        
        print(f"✅ Flask 服務已啟動 (PID: {flask_process.pid})")
        print(f"✅ FastAPI 服務已啟動 (PID: {fastapi_process.pid})")
        print(f"📧 郵件收件夾: http://localhost:{FLASK_PORT}")
        print(f"🚀 FT-EQC 處理: http://localhost:{FASTAPI_PORT}/ui")
        print(f"📚 FT-EQC API 文檔: http://localhost:{FASTAPI_PORT}/docs")
        print("\n按 Ctrl+C 停止所有服務...")
        
        # 等待所有進程
        while True:
            time.sleep(1)
            for name, process in processes:
                if process.poll() is not None:
                    print(f"⚠️ {name} 服務意外停止")
                    return
        
    except KeyboardInterrupt:
        print("\n🛑 正在停止所有服務...")
        for name, process in processes:
            process.terminate()
            process.wait()
            print(f"✅ {name} 服務已停止")
    except Exception as e:
        print(f"❌ 啟動服務失敗: {e}")
        for name, process in processes:
            if process.poll() is None:
                process.terminate()

def main():
    """主函數"""
    import argparse
    
    global FLASK_PORT, FASTAPI_PORT
    
    parser = argparse.ArgumentParser(description='整合服務啟動器')
    parser.add_argument('--mode', choices=['integrated', 'separate'], default='integrated',
                       help='啟動模式: integrated (整合) 或 separate (分離)')
    parser.add_argument('--flask-port', type=int, default=FLASK_PORT,
                       help=f'Flask 服務端口 (預設: {FLASK_PORT})')
    parser.add_argument('--fastapi-port', type=int, default=FASTAPI_PORT,
                       help=f'FastAPI 服務端口 (預設: {FASTAPI_PORT})')
    
    args = parser.parse_args()
    
    FLASK_PORT = args.flask_port
    FASTAPI_PORT = args.fastapi_port
    
    print("=" * 50)
    print("🌟 郵件收件夾 + FT-EQC 整合服務")
    print("=" * 50)
    print(f"模式: {args.mode}")
    print(f"Flask 端口: {FLASK_PORT}")
    print(f"FastAPI 端口: {FASTAPI_PORT}")
    print("=" * 50)
    
    if args.mode == 'integrated':
        start_integrated_service()
    else:
        start_separate_services()

if __name__ == '__main__':
    main()