<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>郵件詳情 - {{ email.subject }}</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/email_inbox.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/base.css') }}">
</head>
<body>
    <div class="email-detail-page">
        <!-- 頂部導航 -->
        <header class="detail-header">
            <div class="header-left">
                <a href="{{ url_for('email_web.inbox') }}" class="btn btn-back">
                    ← 返回收件夾
                </a>
                <h1>郵件詳情</h1>
            </div>
            <div class="header-right">
                <button id="mark-read-btn" class="btn btn-secondary">
                    {% if email.is_read %}已讀{% else %}標記已讀{% endif %}
                </button>
                <button id="delete-btn" class="btn btn-danger">刪除</button>
                <button id="process-btn" class="btn btn-primary">處理</button>
            </div>
        </header>

        <!-- 郵件資訊 -->
        <div class="email-info-card">
            <div class="email-header">
                <h2 class="email-subject">{{ email.subject }}</h2>
                <div class="email-meta">
                    <div class="meta-row">
                        <span class="meta-label">寄件者:</span>
                        <span class="meta-value">{{ email.sender }}</span>
                    </div>
                    <div class="meta-row">
                        <span class="meta-label">時間:</span>
                        <span class="meta-value">{{ email.received_time }}</span>
                    </div>
                    <div class="meta-row">
                        <span class="meta-label">郵件 ID:</span>
                        <span class="meta-value">{{ email.message_id }}</span>
                    </div>
                    {% if email.attachments %}
                    <div class="meta-row">
                        <span class="meta-label">附件:</span>
                        <span class="meta-value">{{ email.attachment_count }} 個附件</span>
                    </div>
                    {% endif %}
                </div>
            </div>

            <!-- 附件區域 -->
            {% if email.attachments %}
            <div class="attachments-section">
                <h3>📎 附件 ({{ email.attachment_count }})</h3>
                <div class="attachments-list">
                    {% for attachment in email.attachments %}
                    <div class="attachment-item">
                        <div class="attachment-info">
                            <span class="attachment-name">{{ attachment.filename }}</span>
                            <span class="attachment-size">{{ "%.1f"|format(attachment.size_bytes / 1024) }} KB</span>
                            <span class="attachment-type">{{ attachment.content_type }}</span>
                        </div>
                        <div class="attachment-actions">
                            <button class="btn btn-sm btn-secondary" onclick="downloadAttachment({{ attachment.id }})">
                                下載
                            </button>
                            <button class="btn btn-sm btn-primary" onclick="processAttachment({{ attachment.id }})">
                                處理
                            </button>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
            {% endif %}

            <!-- 郵件內容 -->
            <div class="email-content-section">
                <h3>📄 郵件內容</h3>
                <div class="email-content">
                    <pre class="email-body">{{ email.body or '(無內容)' }}</pre>
                </div>
            </div>

            <!-- 處理狀態 -->
            {% if email.process_status %}
            <div class="process-status-section">
                <h3>⚙️ 處理狀態</h3>
                <div class="process-status-list">
                    {% for status in email.process_status %}
                    <div class="process-step {% if status.status == 'completed' %}completed{% elif status.status == 'failed' %}failed{% elif status.status == 'processing' %}processing{% endif %}">
                        <div class="step-info">
                            <span class="step-name">{{ status.step_name }}</span>
                            <span class="step-status">{{ status.status }}</span>
                        </div>
                        <div class="step-details">
                            {% if status.started_at %}
                            <span class="step-time">開始: {{ status.started_at }}</span>
                            {% endif %}
                            {% if status.completed_at %}
                            <span class="step-time">完成: {{ status.completed_at }}</span>
                            {% endif %}
                            {% if status.error_message %}
                            <span class="step-error">錯誤: {{ status.error_message }}</span>
                            {% endif %}
                        </div>
                        <div class="step-progress">
                            <div class="progress-bar">
                                <div class="progress-fill" style="width: {{ status.progress_percentage }}%"></div>
                            </div>
                            <span class="progress-text">{{ status.progress_percentage }}%</span>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
            {% endif %}
        </div>

        <!-- 操作面板 -->
        <div class="action-panel">
            <div class="action-group">
                <h4>業務流程操作</h4>
                <button id="start-eqc-process" class="btn btn-primary" onclick="window.open('/ft-eqc', '_blank')">
                    🚀 啟動 EQC 處理
                </button>
                <button id="generate-report" class="btn btn-secondary">
                    📊 生成報告
                </button>
                <button id="code-analysis" class="btn btn-secondary">
                    🔍 程式碼分析
                </button>
            </div>
            
            <div class="action-group">
                <h4>系統整合</h4>
                <button id="open-gtk" class="btn btn-secondary">
                    🖥️ 開啟 GTK 介面
                </button>
                <button id="ft-summary" class="btn btn-secondary">
                    📋 FT Summary
                </button>
                <button id="one-click-complete" class="btn btn-primary">
                    ⚡ 一鍵完成
                </button>
            </div>
        </div>
    </div>

    <!-- 載入中遮罩 -->
    <div id="loading-overlay" class="loading-overlay hidden">
        <div class="loading-content">
            <div class="loading-spinner large"></div>
            <p>處理中...</p>
        </div>
    </div>

    <!-- 確認對話框 -->
    <div id="confirm-dialog" class="dialog-overlay hidden">
        <div class="dialog-content">
            <div class="dialog-header">
                <h3 id="dialog-title">確認操作</h3>
                <button id="dialog-close" class="btn btn-close">✕</button>
            </div>
            <div class="dialog-body">
                <p id="dialog-message">您確定要執行此操作嗎？</p>
            </div>
            <div class="dialog-footer">
                <button id="dialog-cancel" class="btn btn-secondary">取消</button>
                <button id="dialog-confirm" class="btn btn-primary">確認</button>
            </div>
        </div>
    </div>

    <!-- 通知訊息 -->
    <div id="notification" class="notification hidden">
        <div class="notification-content">
            <span class="notification-icon">ℹ️</span>
            <span class="notification-text">訊息內容</span>
            <button class="notification-close">✕</button>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="{{ url_for('static', filename='js/email_inbox.js') }}"></script>
    <script>
        // 初始化頁面數據
        const emailData = {{ email|tojson|safe }};
        
        // 初始化郵件詳情頁面
        document.addEventListener('DOMContentLoaded', function() {
            const emailDetail = new EmailDetail(emailData);
            emailDetail.initialize();
        });

        // 附件操作函數
        function downloadAttachment(attachmentId) {
            // 實作附件下載邏輯
            console.log('Download attachment:', attachmentId);
        }

        function processAttachment(attachmentId) {
            // 實作附件處理邏輯
            console.log('Process attachment:', attachmentId);
        }
    </script>
</body>
</html>