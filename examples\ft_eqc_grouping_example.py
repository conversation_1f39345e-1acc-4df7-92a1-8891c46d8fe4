"""
FT-EQC 分組處理器使用範例
展示如何使用完整的資料夾掃描、檔案分類、智能配對功能
"""

import os
import sys
from pathlib import Path

# 添加專案根目錄到 Python 路徑
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.infrastructure.adapters.excel.ft_eqc_grouping_processor import (
    FTEQCGroupingProcessor, 
    process_ft_eqc_grouping
)


def demonstrate_basic_usage():
    """基本使用範例"""
    print("🔄 基本使用範例")
    print("=" * 50)
    
    # 方法1: 使用便捷函數
    folder_path = "doc"  # 或任何包含 CSV 檔案的資料夾
    
    try:
        result = process_ft_eqc_grouping(folder_path)
        
        print(f"📁 掃描資料夾: {folder_path}")
        print(f"📊 處理結果:")
        print(f"  • 總 CSV 檔案數: {result.statistics['total_csv_files']}")
        print(f"  • FT 檔案數: {result.statistics['ft_files_count']}")
        print(f"  • EQC 檔案數: {result.statistics['eqc_files_count']}")
        print(f"  • 成功配對數: {result.statistics['successful_matches']}")
        print(f"  • EQC RT 檔案數: {result.statistics['eqc_rt_count']}")
        print(f"  • 配對成功率: {result.statistics['matching_rate']:.2%}")
        
        # 顯示配對結果
        if result.matched_pairs:
            print(f"\n✅ 成功配對:")
            for i, (ft_file, eqc_file) in enumerate(result.matched_pairs, 1):
                print(f"  {i}. {os.path.basename(ft_file)} ↔ {os.path.basename(eqc_file)}")
        
        # 顯示未配對的 EQC RT 檔案
        if result.unmatched_eqc:
            print(f"\n📋 EQC RT 檔案 (未配對):")
            for i, eqc_file in enumerate(result.unmatched_eqc, 1):
                print(f"  {i}. {os.path.basename(eqc_file)}")
                
    except Exception as e:
        print(f"❌ 處理失敗: {e}")


def demonstrate_advanced_usage():
    """進階使用範例"""
    print("\n🔧 進階使用範例")
    print("=" * 50)
    
    # 方法2: 使用詳細的處理器類別
    processor = FTEQCGroupingProcessor()
    folder_path = "doc"
    
    try:
        # 步驟1: 發現所有 CSV 檔案
        csv_files = processor.discover_csv_files(folder_path)
        print(f"🔍 發現 {len(csv_files)} 個 CSV 檔案")
        
        # 步驟2: 分類檔案
        ft_files = processor.classify_ft_files(csv_files)
        eqc_files = processor.classify_eqc_files(csv_files)
        
        print(f"📂 檔案分類結果:")
        print(f"  • FT 檔案: {len(ft_files)} 個")
        for ft_file in ft_files:
            print(f"    - {os.path.basename(ft_file)}")
        
        print(f"  • EQC 檔案: {len(eqc_files)} 個")
        for eqc_file in eqc_files:
            print(f"    - {os.path.basename(eqc_file)}")
        
        # 步驟3: 檢測 CTA 格式
        print(f"\n🔧 CTA 格式檢測:")
        for ft_file in ft_files:
            cta_info = processor.detect_cta_format(ft_file)
            if cta_info['format'] > 0:
                print(f"  • {os.path.basename(ft_file)}: CTA {cta_info['format']} 格式")
                print(f"    匹配模式: {cta_info['match_mode']}")
                if cta_info['bypass_columns']:
                    print(f"    過濾欄位: {cta_info['bypass_columns']}")
        
        # 步驟4: 執行完整處理
        result = processor.process_folder(folder_path)
        print(f"\n⚡ 最終處理結果:")
        print(f"  • 處理時間: {result.statistics['processing_timestamp']}")
        print(f"  • 配對成功率: {result.statistics['matching_rate']:.2%}")
        
    except Exception as e:
        print(f"❌ 進階處理失敗: {e}")


def demonstrate_integration_with_excel():
    """與 Excel 處理系統整合範例"""
    print("\n📊 Excel 整合範例")
    print("=" * 50)
    
    try:
        # 導入 Excel 轉換器
        from src.infrastructure.adapters.excel.csv_to_excel_converter import CSVToExcelConverter
        
        # 步驟1: 執行分組
        folder_path = "doc"
        result = process_ft_eqc_grouping(folder_path)
        
        # 步驟2: 對配對結果進行 Excel 處理
        excel_converter = CSVToExcelConverter()
        
        processed_files = []
        for ft_file, eqc_file in result.matched_pairs:
            print(f"🔄 處理配對: {os.path.basename(ft_file)} + {os.path.basename(eqc_file)}")
            
            # 這裡可以實作更複雜的 FT-EQC 合併邏輯
            # 例如：使用 CopyRowsToNewFile 邏輯合併檔案
            # merged_file = merge_ft_eqc_files(ft_file, eqc_file)
            # excel_file = excel_converter.auto_convert_csv_to_excel(merged_file)
            # processed_files.append(excel_file)
        
        # 步驟3: 處理 EQC RT 檔案
        for eqc_rt_file in result.unmatched_eqc:
            print(f"📋 處理 EQC RT: {os.path.basename(eqc_rt_file)}")
            
            # 對 EQC RT 檔案進行 Excel 轉換
            try:
                excel_file = excel_converter.auto_convert_csv_to_excel(eqc_rt_file)
                processed_files.append(excel_file)
                print(f"  ✅ 已轉換為: {os.path.basename(excel_file)}")
            except Exception as e:
                print(f"  ❌ 轉換失敗: {e}")
        
        print(f"\n🎉 處理完成！共處理 {len(processed_files)} 個檔案")
        
    except ImportError as e:
        print(f"⚠️  Excel 整合需要額外模組: {e}")
    except Exception as e:
        print(f"❌ Excel 整合失敗: {e}")


def demonstrate_recursive_processing():
    """遞迴處理子資料夾範例"""
    print("\n📁 遞迴處理範例")
    print("=" * 50)
    
    # 創建測試目錄結構範例
    test_structure = {
        "主資料夾": ["FT_main_20250607.csv", "EQC_main_20250607.csv"],
        "子資料夾1": ["FT_sub1_20250608.csv", "EQC_sub1_20250608.csv"],
        "子資料夾2/深層": ["CTA_deep_20250609.csv", "onlineeqc_deep_20250609.csv"]
    }
    
    print("📂 支援的目錄結構:")
    for folder, files in test_structure.items():
        print(f"  {folder}/")
        for file in files:
            print(f"    ├── {file}")
    
    print("\n✨ 分組處理器會自動:")
    print("  • 遞迴掃描所有子資料夾")
    print("  • 識別所有 CSV 檔案類型")
    print("  • 跨資料夾進行智能配對")
    print("  • 生成完整的分組結果")


if __name__ == "__main__":
    print("🎯 FT-EQC 分組處理器 - 完整使用範例")
    print("=" * 60)
    
    # 執行各種使用範例
    demonstrate_basic_usage()
    demonstrate_advanced_usage()
    demonstrate_integration_with_excel()
    demonstrate_recursive_processing()
    
    print("\n" + "=" * 60)
    print("💡 使用建議:")
    print("  1. 對於簡單需求，使用 process_ft_eqc_grouping() 便捷函數")
    print("  2. 對於複雜需求，使用 FTEQCGroupingProcessor 類別")
    print("  3. 結合 Excel 處理器可實現完整的資料處理流水線")
    print("  4. 支援任意深度的子資料夾遞迴掃描")
    print("  5. 自動識別 CTA 格式並採用相應的配對策略")
    print("\n🎉 範例執行完成！")