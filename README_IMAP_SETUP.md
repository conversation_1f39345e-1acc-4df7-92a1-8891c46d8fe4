# Outlook IMAP 設定指南

## 概述

本指南說明如何設定 Outlook 以支援 IMAP 連接，並使用 `test_imap_simple.py` 測試腳本來讀取郵件。

## 前置準備

### 1. 啟用 Outlook IMAP 功能

1. **登入 Outlook 網頁版**
   - 前往 [https://outlook.office365.com](https://outlook.office365.com)
   - 使用您的 Microsoft 帳號登入

2. **進入設定頁面**
   - 點擊右上角的 ⚙️ 設定圖示
   - 選擇「檢視所有 Outlook 設定」

3. **啟用 IMAP**
   - 在左側選單選擇「郵件」→「同步電子郵件」
   - 在「POP 和 IMAP」區段中
   - 勾選「讓裝置和應用程式使用 IMAP」
   - 點擊「儲存」

### 2. 建立應用程式密碼

由於 Microsoft 帳戶啟用了雙重驗證，您需要建立應用程式專用密碼：

1. **前往 Microsoft 帳戶安全性設定**
   - 訪問 [https://account.microsoft.com/security](https://account.microsoft.com/security)
   - 登入您的 Microsoft 帳戶

2. **建立應用程式密碼**
   - 點擊「進階安全性選項」
   - 找到「應用程式密碼」區段
   - 點擊「建立新的應用程式密碼」
   - 輸入應用程式名稱（例如：「IMAP 測試」）
   - 記下生成的密碼（16 個字元，包含字母和數字）

⚠️ **重要提醒：**
- 應用程式密碼只會顯示一次，請務必記錄保存
- 在 IMAP 連接時使用此密碼，不是您的一般 Microsoft 帳戶密碼

## 使用測試腳本

### 執行腳本

```bash
python3 test_imap_simple.py
```

### 輸入資訊

1. **輸入 Outlook 帳號**
   ```
   請輸入 Outlook 帳號: <EMAIL>
   ```

2. **輸入應用程式密碼**
   ```
   請輸入應用程式密碼: [輸入 16 位應用程式密碼]
   ```

### 預期輸出

```
=== 簡單 IMAP 收信測試 ===

請輸入 Outlook 帳號: <EMAIL>
請輸入應用程式密碼: ****************
🔗 正在連接到 Outlook IMAP 伺服器...
🔐 正在驗證帳號...
✅ 連接成功！
📧 成功連接到 <EMAIL>

📧 正在讀取最新 10 封郵件...
📬 找到 25 封郵件，顯示最新 10 封：

1. 主旨: 測試郵件
   寄件者: <EMAIL>
   時間: 2025-07-09 10:30:00
   附件: 有 (2個)
   附件檔案: document.pdf, data.csv
   內容預覽: 這是一封測試郵件的內容...

2. 主旨: 工作報告
   寄件者: <EMAIL>
   時間: 2025-07-09 09:15:00
   附件: 無
   內容預覽: 本週工作進度報告...
```

## 技術細節

### IMAP 連接參數

- **伺服器**: `outlook.office365.com`
- **端口**: `993` (SSL/TLS)
- **加密**: SSL/TLS
- **認證**: 使用應用程式密碼

### 支援的功能

- ✅ 讀取收件夾郵件
- ✅ 顯示郵件基本資訊（主旨、寄件者、時間）
- ✅ 附件資訊檢查
- ✅ 郵件內容預覽
- ✅ 中文編碼支援
- ✅ 錯誤處理和重試

### 限制

- 只支援讀取功能，不支援寫入
- 不支援即時監控（需要額外實作）
- 附件只顯示資訊，不自動下載

## 常見問題

### Q1: 連接失敗，顯示「IMAP 錯誤」

**可能原因：**
1. 帳號或密碼錯誤
2. 未啟用 IMAP 功能
3. 未使用應用程式密碼

**解決方法：**
1. 確認帳號無誤
2. 檢查是否已啟用 IMAP（參考上方設定步驟）
3. 使用應用程式密碼，不是一般密碼

### Q2: 中文郵件顯示亂碼

**解決方法：**
腳本已包含中文編碼處理，支援 UTF-8、Big5、GB2312 等常見編碼。

### Q3: 無法讀取某些郵件

**可能原因：**
1. 郵件格式特殊
2. 權限不足

**解決方法：**
腳本會跳過有問題的郵件並繼續處理其他郵件。

### Q4: 想要讀取更多郵件

**修改方法：**
編輯 `test_imap_simple.py` 中的 `read_emails(mail, count=10)` 的 `count` 參數。

## 進階功能

### 監控新郵件

如需實作即時監控功能，可以考慮：
1. 使用 IMAP IDLE 命令
2. 定期輪詢檢查
3. 整合到現有的郵件處理系統

### 附件下載

可以擴展腳本來下載附件：
```python
def download_attachment(part, filename):
    with open(filename, 'wb') as f:
        f.write(part.get_payload(decode=True))
```

## 安全性建議

1. **保護應用程式密碼**
   - 不要在程式碼中硬編碼密碼
   - 使用環境變數或配置檔案
   - 定期更新應用程式密碼

2. **網路安全**
   - 確保使用 SSL/TLS 連接
   - 在安全的網路環境中使用

3. **權限管理**
   - 只給予必要的權限
   - 定期檢查已授權的應用程式

## 支援

如果遇到問題，請檢查：
1. 網路連接是否正常
2. Outlook 設定是否正確
3. 應用程式密碼是否有效
4. 防火牆是否阻擋連接

---

*最後更新: 2025-07-09*