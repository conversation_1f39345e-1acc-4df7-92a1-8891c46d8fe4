# POP3 郵件系統實作說明

## 概述

本專案成功實作了完整的 POP3 郵件讀取和 SMTP 郵件發送系統，支援公司內部郵件伺服器 `hcmail.gmt.com.tw`。

## 功能特色

### ✅ 已實作功能

1. **POP3 郵件讀取器**
   - 支援 SSL/非SSL 連接
   - 郵件讀取和解析
   - 附件處理
   - 郵件搜尋
   - 連接管理和錯誤處理

2. **SMTP 郵件發送器**
   - 支援 TLS/SSL 連接
   - HTML 和純文字郵件
   - 附件發送
   - 多收件人支援
   - 發送統計

3. **郵件工廠模式**
   - 支援多種郵件讀取器類型
   - 統一的配置管理
   - 環境變數配置

4. **完整測試套件**
   - 連接測試
   - 郵件讀取測試
   - 郵件發送測試
   - 完整循環測試

## 檔案結構

```
src/infrastructure/adapters/
├── pop3/
│   ├── __init__.py
│   ├── pop3_adapter.py        # POP3 底層適配器
│   └── pop3_email_reader.py   # POP3 郵件讀取器
├── smtp/
│   ├── __init__.py
│   └── smtp_sender.py         # SMTP 郵件發送器
└── email_reader_factory.py   # 郵件讀取器工廠

測試和範例：
├── test_pop3_integration.py   # 整合測試
├── pop3_usage_example.py      # 使用範例
└── debug_connection.py        # 連接調試工具
```

## 配置設定

### 環境變數配置 (.env)

```env
# POP3 收信設定
POP3_SERVER=hcmail.gmt.com.tw
POP3_PORT=1100
POP3_USE_SSL=false
POP3_TIMEOUT=30

# SMTP 發信設定
SMTP_SERVER=hcmail.gmt.com.tw
SMTP_PORT=2500
SMTP_USE_AUTH=true
SMTP_USE_TLS=false
SMTP_TIMEOUT=30

# 郵件帳號設定
EMAIL_ADDRESS=telowyield1
EMAIL_PASSWORD=GMTgmt88TE

# 郵件處理設定
EMAIL_CHECK_INTERVAL=60
EMAIL_MAX_FETCH_COUNT=50
EMAIL_DELETE_AFTER_READ=false
```

## 使用方法

### 1. 基本使用

```python
import asyncio
from src.infrastructure.adapters.email_reader_factory import EmailReaderFactory, EmailReaderType
from src.infrastructure.adapters.smtp.smtp_sender import SMTPSender, SMTPConfig, EmailMessage

async def main():
    # 建立 POP3 郵件讀取器
    email_reader = EmailReaderFactory.create_from_env_config(EmailReaderType.POP3)
    
    # 連接和讀取郵件
    if await email_reader.connect():
        emails = await email_reader.read_emails(count=10)
        
        for email in emails:
            print(f"主旨: {email.subject}")
            print(f"寄件者: {email.sender}")
            print(f"附件: {len(email.attachments)} 個")
            
            # 解析郵件
            parsing_result = await email_reader.parse_email(email)
            if parsing_result.is_success:
                print(f"廠商代碼: {parsing_result.vendor_code}")
        
        await email_reader.disconnect()

asyncio.run(main())
```

### 2. 發送郵件

```python
from email_config import EmailConfigManager

# 取得配置
config_manager = EmailConfigManager()
smtp_config_data = config_manager.get_smtp_config()
account_config = config_manager.get_email_account()

# 建立 SMTP 配置
smtp_config = SMTPConfig(
    server=smtp_config_data.server,
    port=smtp_config_data.port,
    username=account_config.email_address,
    password=account_config.password,
    use_auth=smtp_config_data.use_auth,
    use_tls=smtp_config_data.use_tls
)

# 建立發送器並發送郵件
smtp_sender = SMTPSender(smtp_config)
message = EmailMessage(
    to=["<EMAIL>"],
    subject="測試郵件",
    body="這是測試郵件內容",
    html_body="<h1>測試郵件</h1><p>HTML 內容</p>"
)

success = smtp_sender.send_email(message)
```

### 3. 郵件搜尋

```python
# 搜尋符合條件的郵件
search_criteria = {
    "subject": "測試",
    "sender": "<EMAIL>",
    "date_range": {
        "start": datetime(2025, 1, 1),
        "end": datetime(2025, 12, 31)
    }
}

filtered_emails = await email_reader.search_emails(search_criteria)
```

## 測試和調試

### 1. 執行完整測試

```bash
python3 test_pop3_integration.py
```

### 2. 執行使用範例

```bash
python3 pop3_usage_example.py
```

### 3. 調試連接問題

```bash
python3 debug_connection.py
```

## 測試結果

最新測試結果：

```
============================================================
  POP3 整合測試
============================================================

✅ 配置測試 通過
✅ POP3 連接測試 通過
✅ SMTP 連接測試 通過
❌ 郵件讀取測試 失敗（沒有郵件）
✅ 郵件發送測試 通過
✅ 完整循環測試 通過

📊 測試結果: 5/6 通過
```

## 核心特性

### POP3 適配器 (pop3_adapter.py)

- **連接管理**: 支援 SSL/非SSL 連接
- **郵件操作**: 讀取、刪除、重置會話
- **錯誤處理**: 完整的異常處理和重試機制
- **統計資訊**: 連接狀態和伺服器資訊

### POP3 郵件讀取器 (pop3_email_reader.py)

- **介面實作**: 完整實作 EmailReader 介面
- **郵件解析**: 自動解析郵件內容和附件
- **格式處理**: 自動修正郵件地址格式
- **快取機制**: 支援郵件快取和統計

### SMTP 發送器 (smtp_sender.py)

- **多格式支援**: HTML 和純文字郵件
- **附件處理**: 支援多種檔案類型
- **收件人管理**: 支援 TO, CC, BCC
- **發送統計**: 詳細的發送統計和錯誤追蹤

### 郵件工廠 (email_reader_factory.py)

- **工廠模式**: 統一的郵件讀取器建立介面
- **配置管理**: 支援環境變數和配置檔案
- **多類型支援**: 預留 Outlook 和 IMAP 支援

## 系統整合

### 與現有系統整合

1. **配置管理**: 使用現有的 `email_config.py` 配置系統
2. **日誌系統**: 整合現有的 `LoggerManager`
3. **數據模型**: 使用現有的 `EmailData` 和 `EmailParsingResult` 模型
4. **錯誤處理**: 遵循現有的錯誤處理模式

### 擴展性設計

1. **介面設計**: 使用抽象介面，易於擴展
2. **工廠模式**: 支援多種郵件讀取器類型
3. **配置靈活**: 支援多種配置來源
4. **模組化**: 清晰的模組劃分，易於維護

## 注意事項

1. **郵件地址格式**: 系統會自動將不完整的郵件地址（如 `telowyield1`）補完為完整格式（如 `<EMAIL>`）

2. **POP3 特性**: POP3 不支援伺服器端搜尋，搜尋功能在客戶端執行

3. **連接管理**: 使用連接上下文管理器確保資源正確釋放

4. **錯誤處理**: 完整的異常處理，包括網路錯誤、認證錯誤等

## 下一步發展

1. **IMAP 支援**: 添加 IMAP 郵件讀取器
2. **即時監控**: 實作郵件即時監控功能
3. **批次處理**: 添加批次郵件處理功能
4. **效能優化**: 優化大量郵件處理效能

## 總結

POP3 郵件系統實作成功完成，提供了完整的郵件讀取和發送功能。系統通過了大部分測試，可以正常連接到公司郵件伺服器，發送和接收郵件。系統設計遵循 SOLID 原則，易於維護和擴展。