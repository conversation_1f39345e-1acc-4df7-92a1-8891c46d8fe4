{"mcpServers": {"context7": {"command": "npx", "args": ["-y", "@upstash/context7-mcp"]}, "serena": {"command": "uvx", "args": ["--from", "git+https://github.com/oraios/serena", "serena-mcp-server"]}, "playwright": {"command": "npx", "args": ["-y", "@executeautomation/playwright-mcp-server"]}, "sequential-thinking": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"]}}}