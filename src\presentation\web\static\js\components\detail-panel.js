/**
 * 詳細面板組件模組 v2.2 (2025-06-23 20:45)
 * 處理詳細資料的展開/收合、內容渲染和互動功能
 * 新增: 下載按鈕生成功能
 */

class DetailPanelComponent {
    constructor() {
        this.isExpanded = false;
        this.currentData = null;
        this.version = "2.2"; // 版本標識
        
        this.elements = {
            detailContent: null,
            summaryPreview: null,
            detailChevron: null,
            detailHeader: null,
            eqcDetailItem: null,
            emptyState: null,
            statusBadge: null
        };
        
        this.summaryElements = {
            failCount: null,
            passCount: null,
            matchCount: null,  // 🔧 改為匹配數量
            searchMethod: null,
            searchStatus: null,  // 🔧 新增搜尋狀態
            highlight: null
        };
    }
    
    /**
     * 初始化詳細面板組件
     */
    init() {
        console.log('📖 初始化詳細面板組件...');
        
        // 獲取主要 DOM 元素
        this.elements.detailContent = DOMManager.get('detailContent');
        this.elements.summaryPreview = DOMManager.get('summaryPreview');
        this.elements.detailChevron = DOMManager.get('detailChevron');
        this.elements.detailHeader = DOMManager.querySelector('.detail-header');
        this.elements.eqcDetailItem = DOMManager.get('eqcDetailItem');
        this.elements.emptyState = DOMManager.get('emptyState');
        this.elements.statusBadge = DOMManager.get('statusBadge');
        
        // 獲取摘要元素
        this.summaryElements.failCount = DOMManager.get('summaryFailCount');
        this.summaryElements.passCount = DOMManager.get('summaryPassCount');
        this.summaryElements.matchCount = DOMManager.get('summaryMatchRate');  // 🔧 元素ID保持不變，但語義改為匹配數量
        this.summaryElements.searchMethod = DOMManager.get('summarySearchMethod');
        this.summaryElements.searchStatus = DOMManager.get('summarySearchStatus');  // 🔧 新增搜尋狀態元素
        this.summaryElements.highlight = DOMManager.get('summaryHighlight');
        
        // 設置事件監聽器
        this.setupEventListeners();
        
        // 初始化狀態
        this.reset();
        
        console.log('✅ 詳細面板組件初始化完成');
    }
    
    /**
     * 設置事件監聽器
     */
    setupEventListeners() {
        console.log('🔧 設置事件監聽器...');
        console.log('🔍 detailHeader 元素:', this.elements.detailHeader);
        
        if (this.elements.detailHeader) {
            // 🔧 強制清除可能存在的 onclick 屬性（避免重複事件）
            this.elements.detailHeader.onclick = null;
            this.elements.detailHeader.removeAttribute('onclick');

            // 🔧 防止重複綁定事件監聽器
            if (!this.elements.detailHeader.hasAttribute('data-event-bound')) {
                this.elements.detailHeader.addEventListener('click', (event) => {
                    console.log('🖱️ 詳細面板頭部被點擊');
                    event.preventDefault();
                    this.toggle();
                });
                this.elements.detailHeader.setAttribute('data-event-bound', 'true');
                console.log('✅ 詳細面板事件監聽器已綁定');
            } else {
                console.log('📖 詳細面板事件監聽器已存在，跳過重複綁定');
            }
        } else {
            console.warn('⚠️ detailHeader 元素未找到，無法綁定事件');
            // 🔧 嘗試替代方案：直接通過選擇器綁定
            const headerElement = document.querySelector('.detail-header');
            if (headerElement) {
                console.log('🔧 找到替代的 detail-header 元素，嘗試綁定');
                headerElement.addEventListener('click', (event) => {
                    console.log('🖱️ 替代方案：詳細面板頭部被點擊');
                    event.preventDefault();
                    this.toggle();
                });
                console.log('✅ 替代方案事件監聽器已綁定');
            }
        }
    }
    
    /**
     * 重置面板狀態
     */
    reset() {
        this.isExpanded = false;
        this.currentData = null;
        
        // 顯示詳細項目，隱藏空狀態
        DOMManager.show('eqcDetailItem');
        DOMManager.hide('emptyState');
        
        // 重置摘要數據
        this.updateSummary(0, 0, '0%', '未知', '未知');
        
        // 確保處於收合狀態
        this.collapse();
    }
    
    /**
     * 切換展開/收合狀態
     */
    toggle() {
        console.log(`📖 切換詳細面板: ${this.isExpanded ? '收合' : '展開'}`);
        console.log('🔍 當前狀態:', {
            isExpanded: this.isExpanded,
            detailContent: !!this.elements.detailContent,
            summaryPreview: !!this.elements.summaryPreview,
            statusBadge: !!this.elements.statusBadge
        });

        this.isExpanded = !this.isExpanded;

        if (this.isExpanded) {
            console.log('➡️ 執行展開操作');
            this.expand();
        } else {
            console.log('⬅️ 執行收合操作');
            this.collapse();
        }
        
        console.log(`✅ 狀態切換完成，當前展開狀態: ${this.isExpanded}`);
    }
    
    /**
     * 展開詳細資料
     */
    expand() {
        if (!this.elements.detailContent || !this.elements.summaryPreview) return;

        // 檢查是否有數據，決定顯示內容
        if (this.currentData) {
            // 有數據：顯示詳細內容，隱藏摘要預覽
            DOMManager.show('detailContent');
            DOMManager.hide('summaryPreview');
        } else {
            // 無數據：顯示提示信息
            DOMManager.setHTML('detailContent', `
                <div style="padding: 20px; text-align: center; color: #6c757d;">
                    <i class="fas fa-info-circle" style="font-size: 2em; margin-bottom: 10px;"></i>
                    <p>尚未開始處理，請先選擇資料夾並執行處理</p>
                </div>
            `);
            DOMManager.show('detailContent');
            DOMManager.hide('summaryPreview');
        }

        // 旋轉箭頭
        if (this.elements.detailChevron) {
            this.elements.detailChevron.style.transform = 'rotate(90deg)';
        }

        // 更新狀態徽章
        if (this.elements.statusBadge) {
            this.elements.statusBadge.textContent = '點擊收合詳情';
            console.log('✅ 狀態徽章已更新為: 點擊收合詳情');
        } else {
            console.warn('⚠️ statusBadge 元素未找到，無法更新狀態文字');
        }

        // 🔧 移除重複的狀態設置，狀態由 toggle() 方法統一管理
        console.log('📖 詳細面板已展開');
    }
    
    /**
     * 收合詳細資料
     */
    collapse() {
        if (!this.elements.detailContent || !this.elements.summaryPreview) return;

        // 隱藏詳細內容，顯示摘要預覽
        DOMManager.hide('detailContent');
        DOMManager.show('summaryPreview');

        // 重置箭頭
        if (this.elements.detailChevron) {
            this.elements.detailChevron.style.transform = 'rotate(0deg)';
        }

        // 更新狀態徽章
        if (this.elements.statusBadge) {
            this.elements.statusBadge.textContent = '點擊查看詳情';
            console.log('✅ 狀態徽章已更新為: 點擊查看詳情');
        } else {
            console.warn('⚠️ statusBadge 元素未找到，無法更新狀態文字');
        }

        // 🔧 移除重複的狀態設置，狀態由 toggle() 方法統一管理
        console.log('📖 詳細面板已收合');
    }
    
    /**
     * 渲染詳細內容
     * @param {Object} data - 要渲染的數據
     */
    async renderDetailContent(data) {
        if (!data) {
            console.warn('⚠️ 沒有數據可渲染');
            return;
        }

        this.currentData = data;

        // 隱藏空狀態，顯示詳細項目
        DOMManager.hide('emptyState');
        DOMManager.show('eqcDetailItem');

        // 更新摘要數據（異步讀取真實數據）
        await this.updateSummaryFromData(data);

        // 自動更新 CODE 區間設定
        this.updateCodeRegionsFromResults(data);

        // 🔧 不要在這裡設置狀態徽章文字，讓 toggle() 方法統一管理
        if (this.elements.statusBadge) {
            this.elements.statusBadge.className = 'status-badge success';
        }

        // 渲染詳細內容HTML（現在包含真實數據）
        const contentHTML = this.generateDetailHTML(data);
        DOMManager.setHTML('detailContent', contentHTML);

        // 🔧 不要默認收合，讓自動展開邏輯統一處理狀態
        // 只確保內容已準備好，狀態由 eqc-processor.js 的自動展開邏輯控制

        console.log('📖 詳細內容渲染完成');
    }

    /**
     * 從處理結果自動更新 CODE 區間設定
     * @param {Object} data - 處理結果數據
     */
    updateCodeRegionsFromResults(data) {
        console.log('🔧 開始自動更新 CODE 區間設定...');
        console.log('📊 完整數據結構:', data);

        // 檢查是否有區間檢測結果 - 修復數據路徑
        let regionResult = data.region_result;
        
        // 如果直接路徑沒有，檢查是否在 data.data 中
        if (!regionResult && data.data && data.data.region_result) {
            regionResult = data.data.region_result;
            console.log('🔍 從 data.data.region_result 找到區間數據');
        }
        
        // 🔧 新增：從 step2_advanced.data 中獲取區間資訊
        if (!regionResult && data.step2_advanced && data.step2_advanced.data) {
            const step2Data = data.step2_advanced.data;
            if (step2Data.main_region_start && step2Data.main_region_end) {
                regionResult = {
                    code_region: {
                        start_column_number: step2Data.main_region_start,
                        end_column_number: step2Data.main_region_end
                    },
                    backup_region: {
                        start_column_number: step2Data.backup_region_start,
                        end_column_number: step2Data.backup_region_end
                    }
                };
                console.log('🔍 從 step2_advanced.data 構建區間數據:', regionResult);
            }
        }
        
        if (!regionResult) {
            console.log('⚠️ 沒有區間檢測結果，跳過自動更新');
            console.log('📋 可用數據路徑:', Object.keys(data));
            return;
        }

        console.log('🔍 regionResult 數據:', regionResult);

        // 更新主要 CODE 區間（只有在欄位為空時才自動填入）
        if (regionResult.code_region) {
            const mainRegion = regionResult.code_region;
            console.log('🔍 主要區間數據:', mainRegion);

            if (mainRegion.start_column_number && mainRegion.end_column_number) {
                const currentMainStart = DOMManager.getValue('mainStart');
                const currentMainEnd = DOMManager.getValue('mainEnd');
                
                // 只有當欄位為空時才自動填入
                if (!currentMainStart || !currentMainEnd) {
                    console.log(`🎯 自動設定主要區間（欄位為空）: ${mainRegion.start_column_number}-${mainRegion.end_column_number}`);
                    
                    DOMManager.setValue('mainStart', mainRegion.start_column_number.toString());
                    DOMManager.setValue('mainEnd', mainRegion.end_column_number.toString());

                    // 觸發欄位計算更新
                    if (typeof updateFieldCount === 'function') {
                        updateFieldCount();
                    }
                } else {
                    console.log(`🔒 主要區間已有值，保持現有設定: ${currentMainStart}-${currentMainEnd}`);
                }
            }
        }

        // 更新備用 CODE 區間
        console.log('🔍 檢查備用區間是否存在:', !!regionResult.backup_region);

        if (regionResult.backup_region) {
            const backupRegion = regionResult.backup_region;
            console.log('🔍 備用區間完整數據:', backupRegion);
            console.log('🔍 備用區間屬性檢查:');
            console.log('  - backup_start_column:', backupRegion.backup_start_column);
            console.log('  - backup_end_column:', backupRegion.backup_end_column);
            console.log('  - start_column_number:', backupRegion.start_column_number);
            console.log('  - end_column_number:', backupRegion.end_column_number);
            console.log('  - found:', backupRegion.found);

            // 檢查多種可能的備用區間數據格式
            let backupStart = null;
            let backupEnd = null;

            // 格式1: backup_start_column / backup_end_column
            if (backupRegion.backup_start_column && backupRegion.backup_end_column) {
                backupStart = backupRegion.backup_start_column;
                backupEnd = backupRegion.backup_end_column;
                const backupLength = backupEnd - backupStart + 1;
                console.log(`✅ 使用格式1 (動態檢測): backup_start_column / backup_end_column`);
                console.log(`   📊 備用區間: 第${backupStart}-${backupEnd}欄 (${backupLength}個欄位)`);
            }
            // 格式2: start_column_number / end_column_number
            else if (backupRegion.start_column_number && backupRegion.end_column_number) {
                backupStart = backupRegion.start_column_number;
                backupEnd = backupRegion.end_column_number;
                console.log('✅ 使用格式2: start_column_number / end_column_number');
            }

            if (backupStart && backupEnd) {
                const currentBackupStart = DOMManager.getValue('backupStart');
                const currentBackupEnd = DOMManager.getValue('backupEnd');
                
                // 只有當欄位為空時才自動填入
                if (!currentBackupStart || !currentBackupEnd) {
                    console.log(`🎯 自動設定備用區間（欄位為空）: ${backupStart}-${backupEnd}`);

                    // 檢查 DOM 元素是否存在
                    const backupStartElement = document.getElementById('backupStart');
                    const backupEndElement = document.getElementById('backupEnd');

                    console.log('🔍 DOM 元素檢查:');
                    console.log('  - backupStart 元素:', !!backupStartElement);
                    console.log('  - backupEnd 元素:', !!backupEndElement);

                    if (backupStartElement && backupEndElement) {
                        DOMManager.setValue('backupStart', backupStart.toString());
                        DOMManager.setValue('backupEnd', backupEnd.toString());

                        console.log(`✅ 備用區間自動帶入成功: ${backupStart}-${backupEnd}`);

                        // 觸發欄位計算更新
                        if (typeof updateFieldCount === 'function') {
                            updateFieldCount();
                            console.log('✅ 欄位計算已更新');
                        }
                    } else {
                        console.log('❌ DOM 元素不存在，無法自動帶入');
                    }
                } else {
                    console.log(`🔒 備用區間已有值，保持現有設定: ${currentBackupStart}-${currentBackupEnd}`);
                }
            } else {
                console.log('⚠️ 備用區間數據格式不符合自動帶入條件');
                console.log('  - backupStart:', backupStart);
                console.log('  - backupEnd:', backupEnd);
            }
        } else {
            console.log('⚠️ 沒有備用區間數據');
        }

        // 檢查是否有檢測到的區間
        if (regionResult.code_region || regionResult.backup_region) {
            console.log('✅ CODE 區間自動帶入完成');

            // 顯示成功提示
            if (typeof StatusManager !== 'undefined') {
                StatusManager.showToast('CODE 區間已自動檢測並設定', 'success');
            }
        } else {
            console.log('⚠️ 未檢測到有效的 CODE 區間');
        }
    }
    
    /**
     * 從數據更新摘要
     * @param {Object} data - 數據對象
     */
    async updateSummaryFromData(data) {
        let failCount = 0;
        let passCount = 0;
        let matchCount = 0;  // 🔧 改為匹配數量
        let searchMethod = '未知';
        let searchStatus = '未知';  // 🔧 新增搜尋狀態

        console.log('🔍 分析處理結果數據:', data);

        // 🎯 優先從 EQCTOTALDATA.xlsx 讀取真實數據
        // 修復數據路徑 - 檢查多個可能的路徑
        let folderPath = data.folder_path || (data.data && data.data.folder_path) || (data.data && data.data.doc_directory);
        
        // 🔧 詳細日誌追蹤路徑提取
        console.log('🔍 路徑提取調試:');
        console.log('  - data.folder_path:', data.folder_path);
        console.log('  - data.data?.folder_path:', data.data && data.data.folder_path);
        console.log('  - data.data?.doc_directory:', data.data && data.data.doc_directory);
        console.log('  - 最終使用路徑:', folderPath);
        
        // 🔧 如果沒有找到路徑，嘗試從其他地方獲取
        if (!folderPath) {
            // 嘗試從Step 1結果中的檔案路徑推導
            if (data.step1_online_eqc && data.step1_online_eqc.data) {
                const step1Data = data.step1_online_eqc.data;
                if (step1Data.eqctotaldata_download_path) {
                    // 從完整路徑中提取資料夾路徑
                    folderPath = step1Data.eqctotaldata_download_path.replace(/[\/\\][^\/\\]*$/, '');
                    console.log('🔧 從Step1檔案路徑推導:', folderPath);
                }
            }
            
            // 最後嘗試從全局狀態獲取當前資料夾路徑
            if (!folderPath) {
                const folderInput = document.getElementById('folderPath');
                if (folderInput && folderInput.value.trim()) {
                    folderPath = folderInput.value.trim();
                    console.log('🔧 從輸入框獲取路徑:', folderPath);
                }
            }
        }
        
        // 🔧 智能路徑處理：如果路徑看起來像是上傳解壓縮的路徑，嘗試添加 /doc/20250523
        if (folderPath && folderPath.includes('extracted_') && !folderPath.includes('doc')) {
            // 嘗試查找是否有 doc 子目錄
            const possibleDocPath = folderPath + '/doc/20250523';
            console.log('🔧 嘗試doc子目錄路徑:', possibleDocPath);
            
            // 這裡我們先嘗試原路徑，如果失敗再嘗試doc路徑
            // 這個邏輯在 fetchRealEQCData 中處理
        }
        
        const realData = await this.fetchRealEQCData(folderPath);
        if (realData && realData.status === 'success') {
            // 保存真實數據到實例變數，供其他方法使用
            this.realEQCData = realData;

            failCount = realData.online_eqc_fail || 0;
            passCount = realData.eqc_rt_pass || 0;
            const matchedCount = realData.matched_count || 0;  // 實際配對成功數
            matchCount = `${matchedCount}/${failCount}`;  // 🔧 格式：實際配對成功數/Online EQC FAIL數
            searchMethod = realData.search_method || '主要區間';
            searchStatus = realData.search_status || '成功';  // 🔧 新增搜尋狀態

            console.log(`📊 從 EQCTOTALDATA.xlsx 讀取真實數據: FAIL=${failCount}, PASS=${passCount}, 匹配數量=${matchCount}, 搜尋狀態=${searchStatus}`);
        } else {
            // 回退到原有邏輯
            console.log('⚠️ 無法讀取 EQCTOTALDATA.xlsx，使用處理結果數據');

            // 從多個可能的數據來源提取數據
            // 檢查進階處理結果格式
            if (data.statistics) {
                failCount = data.statistics.fail_count || 0;
                passCount = data.statistics.pass_count || 0;
            }

            // 檢查舊格式的 eqcResult
            if (data.eqcResult) {
                failCount = data.eqcResult.onlineEQC_fail_count || failCount;
                passCount = data.eqcResult.eqc_rt_pass_count || passCount;
            }

            // 🔧 從處理結果中讀取匹配數量 - 修復數據路徑
            let dualSearchData = data.dual_search_result?.dual_search_result;
            
            // 如果直接路徑沒有，檢查是否在 data.data 中
            if (!dualSearchData && data.data && data.data.dual_search_result?.dual_search_result) {
                dualSearchData = data.data.dual_search_result.dual_search_result;
                console.log('🔍 從 data.data.dual_search_result 找到雙重搜尋數據');
            }
            
            if (dualSearchData) {
                const totalMatched = dualSearchData.total_matched || 0;  // 實際配對成功數
                matchCount = `${totalMatched}/${failCount}`;  // 🔧 格式：實際配對成功數/Online EQC FAIL數

                if (dualSearchData.search_method === 'main_region_complete') {
                    searchMethod = '主要區間';
                } else if (dualSearchData.search_method === 'backup_region_mapping') {
                    searchMethod = '備用區間';
                } else if (dualSearchData.search_method) {
                    searchMethod = dualSearchData.search_method;
                }
            }

            // 檢查區間檢測結果 - 修復數據路徑
            let regionResult = data.region_result;
            if (!regionResult && data.data && data.data.region_result) {
                regionResult = data.data.region_result;
            }
            
            if (regionResult) {
                if (regionResult.code_region?.start_column_number) {
                    searchMethod = '主要區間檢測成功';
                } else if (regionResult.backup_region?.start_column_number) {
                    searchMethod = '備用區間檢測成功';
                }
            }
        }

        console.log(`📊 最終統計數據: FAIL=${failCount}, PASS=${passCount}, 匹配數量=${matchCount}, 搜尋方法=${searchMethod}, 搜尋狀態=${searchStatus}`);

        this.updateSummary(failCount, passCount, matchCount, searchMethod, searchStatus);
    }

    /**
     * 從 EQCTOTALDATA.xlsx 讀取真實數據
     * @param {string} folderPath - 資料夾路徑
     * @returns {Promise<Object|null>} 真實數據或 null
     */
    async fetchRealEQCData(folderPath) {
        try {
            if (!folderPath) {
                console.warn('⚠️ 沒有資料夾路徑，無法讀取真實數據');
                return null;
            }

            console.log('🔍 開始從 EQCTOTALDATA.xlsx 讀取真實數據...');
            console.log('📁 API調用路徑:', folderPath);

            // 🔧 嘗試多個可能的路徑
            const pathsToTry = [folderPath];
            
            // 如果是解壓縮路徑且不包含doc，添加doc子路徑
            if (folderPath.includes('extracted_') && !folderPath.includes('doc')) {
                pathsToTry.push(folderPath + '/doc/20250523');
                pathsToTry.push(folderPath + '\\doc\\20250523');
            }
            
            // 如果路徑以斜線結尾，移除它後再試
            if (folderPath.endsWith('/') || folderPath.endsWith('\\')) {
                const trimmedPath = folderPath.slice(0, -1);
                pathsToTry.push(trimmedPath);
                if (trimmedPath.includes('extracted_') && !trimmedPath.includes('doc')) {
                    pathsToTry.push(trimmedPath + '/doc/20250523');
                }
            }
            
            console.log('🔍 將嘗試以下路徑:', pathsToTry);

            // 依序嘗試每個路徑
            for (let i = 0; i < pathsToTry.length; i++) {
                const currentPath = pathsToTry[i];
                console.log(`🔍 嘗試路徑 ${i + 1}/${pathsToTry.length}: ${currentPath}`);
                
                const requestBody = { folder_path: currentPath };
                
                try {
                    const response = await fetch('http://localhost:8010/api/analyze_eqc_real_data', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify(requestBody)
                    });

                    if (response.ok) {
                        const result = await response.json();
                        if (result.status === 'success') {
                            console.log(`✅ 成功讀取真實數據 (路徑 ${i + 1}): ${currentPath}`, result);
                            
                            // 記錄成功的路徑
                            console.log(`📋 API版本: ${result.api_version}, 時間戳: ${result.timestamp}`);
                            return result;
                        } else {
                            console.log(`⚠️ 路徑 ${i + 1} API返回錯誤: ${result.message}`);
                        }
                    } else {
                        console.log(`⚠️ 路徑 ${i + 1} HTTP錯誤: ${response.status} ${response.statusText}`);
                    }
                } catch (error) {
                    console.log(`⚠️ 路徑 ${i + 1} 請求異常: ${error.message}`);
                }
            }
            
            console.warn('❌ 所有路徑都嘗試失敗，無法讀取真實數據');
            return null;

        } catch (error) {
            console.error('❌ 讀取 EQCTOTALDATA.xlsx 真實數據失敗:', error);
            
            // 🔧 增強錯誤處理：嘗試解析統一化錯誤回應格式
            if (error.message && error.message.includes('{')) {
                try {
                    const errorData = JSON.parse(error.message.split('HTTP')[1] || '{}');
                    if (errorData.api_version) {
                        console.log(`📋 錯誤回應 API版本: ${errorData.api_version}, 時間戳: ${errorData.timestamp}`);
                    }
                } catch (parseError) {
                    // 忽略 JSON 解析錯誤，保持原有錯誤處理邏輯
                }
            }
            
            return null;
        }
    }
    
    /**
     * 手動測試真實數據分析功能（調試用）
     * @param {string} testPath - 測試路徑，可選，默認使用輸入框的值
     */
    async testRealDataAnalysis(testPath = null) {
        console.log('🧪 開始手動測試真實數據分析功能...');
        
        const folderPath = testPath || document.getElementById('folderPath')?.value?.trim() || 'D:\\project\\python\\outlook_summary\\doc\\20250523';
        console.log('🔍 測試路徑:', folderPath);
        
        const result = await this.fetchRealEQCData(folderPath);
        
        if (result && result.status === 'success') {
            console.log('✅ 測試成功！數據:', result);
            
            // 手動更新顯示
            this.updateSummary(
                result.online_eqc_fail || 0,
                result.eqc_rt_pass || 0,
                `${result.matched_count || 0}/${result.online_eqc_fail || 0}`,
                result.search_method || '主要區間',
                result.search_status || '成功'
            );
            
            console.log('✅ 顯示已更新！');
            return result;
        } else {
            console.log('❌ 測試失敗，結果:', result);
            return null;
        }
    }
    
    /**
     * 更新摘要顯示
     * @param {number} failCount - 失敗數量
     * @param {number} passCount - 通過數量
     * @param {string} matchCount - 匹配數量（格式：實際配對成功數/Online EQC FAIL數）
     * @param {string} searchMethod - 搜尋方法
     * @param {string} searchStatus - 搜尋狀態
     */
    updateSummary(failCount, passCount, matchCount, searchMethod, searchStatus = '未知') {
        if (this.summaryElements.failCount) {
            this.summaryElements.failCount.textContent = failCount;
        }
        if (this.summaryElements.passCount) {
            this.summaryElements.passCount.textContent = passCount;
        }
        if (this.summaryElements.matchCount) {
            this.summaryElements.matchCount.textContent = matchCount;  // 🔧 顯示匹配數量
        }
        if (this.summaryElements.searchMethod) {
            this.summaryElements.searchMethod.textContent = searchMethod;
        }
        if (this.summaryElements.searchStatus) {
            this.summaryElements.searchStatus.textContent = searchStatus;  // 🔧 顯示搜尋狀態
        }
        
        // 更新高亮提示
        if (this.summaryElements.highlight) {
            const totalCount = failCount + passCount;
            if (totalCount > 0) {
                this.summaryElements.highlight.textContent = 
                    `點擊展開查看詳細資料和前10項FAIL/PASS摘要 (共 ${totalCount} 項)`;
            } else {
                this.summaryElements.highlight.textContent = 
                    '點擊展開查看詳細資料和前10項FAIL/PASS摘要';
            }
        }
    }
    
    /**
     * 生成詳細內容HTML
     * @param {Object} data - 數據對象
     * @returns {string} HTML字符串
     */
    generateDetailHTML(data) {
        let contentHTML = '';
        
        // 🔧 移除詳細面板中的下載按鈕，使用舊版浮動下載對話框
        // contentHTML += this.generateDownloadButtonsHTML(data);
        
        // CODE區間資訊卡片
        contentHTML += this.generateCodeRegionHTML(data);
        
        // 統計資訊
        contentHTML += this.generateStatisticsHTML(data);
        
        // 處理結果摘要 - 已移除
        // contentHTML += this.generateProcessingSummaryHTML(data);
        
        // Site 分布資訊
        contentHTML += this.generateSiteDistributionHTML(data);
        
        return contentHTML;
    }
    
    /**
     * 生成 CODE 區間資訊 HTML
     * @param {Object} data - 數據對象
     * @returns {string} HTML字符串
     */
    generateCodeRegionHTML(data) {
        // 修復數據路徑
        let regionResult = data.region_result;
        if (!regionResult && data.data && data.data.region_result) {
            regionResult = data.data.region_result;
        }
        
        // 🔧 新增：從 step2_advanced.data 中獲取區間資訊
        if (!regionResult && data.step2_advanced && data.step2_advanced.data) {
            const step2Data = data.step2_advanced.data;
            if (step2Data.main_region_start && step2Data.main_region_end) {
                regionResult = {
                    code_region: {
                        start_column_number: step2Data.main_region_start,
                        end_column_number: step2Data.main_region_end
                    },
                    backup_region: {
                        start_column_number: step2Data.backup_region_start,
                        end_column_number: step2Data.backup_region_end
                    }
                };
            }
        }
        
        const regionData = regionResult?.code_region || {};
        const backupData = regionResult?.backup_region || {};

        console.log('🔍 generateCodeRegionHTML 收到的數據:');
        console.log('- regionData:', regionData);
        console.log('- backupData:', backupData);

        if (!regionData.start_column_number && !this.hasBackupRegion(backupData)) {
            return '';
        }

        return `
            <div class="code-info-card">
                <div class="code-info-title">
                    <i class="fas fa-code"></i> CODE 區間檢測結果
                </div>
                ${regionData.start_column_number ? `
                    <div class="code-range">
                        <strong>主要 CODE 區間:</strong>
                        <span style="color: #667eea; font-family: monospace;">第${regionData.start_column_number}-${regionData.end_column_number}欄</span>
                        <span style="color: #666;">(${regionData.end_column_number - regionData.start_column_number + 1} 個欄位)</span>
                    </div>
                ` : ''}
                <div class="code-range">
                    <strong>備用 CODE 區間:</strong>
                    ${this.renderBackupRegionInfo(backupData)}
                </div>
            </div>
        `;
    }

    /**
     * 檢查是否有備用區間
     * @param {Object} backupData - 備用區間數據
     * @returns {boolean} 是否有備用區間
     */
    hasBackupRegion(backupData) {
        if (!backupData) return false;

        return !!(
            (backupData.backup_start_column && backupData.backup_end_column) ||
            (backupData.start_column_number && backupData.end_column_number) ||
            (backupData.found && backupData.backup_start_column && backupData.backup_end_column)
        );
    }

    /**
     * 渲染備用區間信息
     * @param {Object} backupData - 備用區間數據
     * @returns {string} HTML字符串
     */
    renderBackupRegionInfo(backupData) {
        console.log('🔍 renderBackupRegionInfo 收到的 backupData:', backupData);

        // 檢查多種可能的備用區間數據格式
        let backupStart = null;
        let backupEnd = null;

        if (backupData) {
            // 格式1: backup_start_column / backup_end_column
            if (backupData.backup_start_column && backupData.backup_end_column) {
                backupStart = backupData.backup_start_column;
                backupEnd = backupData.backup_end_column;
            }
            // 格式2: start_column_number / end_column_number
            else if (backupData.start_column_number && backupData.end_column_number) {
                backupStart = backupData.start_column_number;
                backupEnd = backupData.end_column_number;
            }
            // 格式3: found 標記 + 其他屬性
            else if (backupData.found && backupData.backup_start_column && backupData.backup_end_column) {
                backupStart = backupData.backup_start_column;
                backupEnd = backupData.backup_end_column;
            }
        }

        if (backupStart && backupEnd) {
            const count = backupEnd - backupStart + 1;
            console.log(`✅ 找到備用區間: ${backupStart}-${backupEnd} (${count} 個欄位)`);
            return `
                <span style="color: #28a745; font-family: monospace;">第${backupStart}-${backupEnd}欄</span>
                <span style="color: #666;">(${count} 個欄位)</span>
            `;
        } else {
            console.log('⚠️ 未檢測到備用區間');
            return `<span style="color: #dc3545;">未檢測到備用區間</span>`;
        }
    }
    
    /**
     * 生成統計資訊 HTML
     * @param {Object} data - 數據對象
     * @returns {string} HTML字符串
     */
    generateStatisticsHTML(data) {
        // 修復數據路徑
        let dualSearchData = data.dual_search_result?.dual_search_result || {};
        if (!dualSearchData.total_matched && data.data && data.data.dual_search_result?.dual_search_result) {
            dualSearchData = data.data.dual_search_result.dual_search_result;
        }

        // 🎯 優先使用真實數據，如果沒有則使用處理結果數據
        let failCount = this.realEQCData?.online_eqc_fail || data.eqcResult?.onlineEQC_fail_count || 0;
        let passCount = this.realEQCData?.eqc_rt_pass || data.eqcResult?.eqc_rt_pass_count || 0;

        // 🔧 計算匹配數量格式：實際配對成功數/Online EQC FAIL數
        let matchCountDisplay;
        if (this.realEQCData) {
            const matchedCount = this.realEQCData.matched_count || 0;
            matchCountDisplay = `${matchedCount}/${failCount}`;
        } else {
            const totalMatched = dualSearchData.total_matched || 0;
            matchCountDisplay = `${totalMatched}/${failCount}`;
        }

        console.log(`📊 generateStatisticsHTML 使用數據: FAIL=${failCount}, PASS=${passCount}, 匹配數量=${matchCountDisplay} (來源: ${this.realEQCData ? 'EQCTOTALDATA.xlsx' : '處理結果'})`);

        return `
            <div class="stats-grid-new">
                <div class="stat-card">
                    <div class="value">${failCount}</div>
                    <div class="label">Online EQC FAIL</div>
                </div>
                <div class="stat-card">
                    <div class="value">${passCount}</div>
                    <div class="label">EQC RT PASS</div>
                </div>
                <div class="stat-card">
                    <div class="value">${matchCountDisplay}</div>
                    <div class="label">匹配數量</div>
                </div>
                <div class="stat-card">
                    <div class="value">${dualSearchData.success !== false ? '成功' : '失敗'}</div>
                    <div class="label">搜尋狀態</div>
                </div>
            </div>
        `;
    }
    
    /**
     * 生成處理結果摘要 HTML
     * @param {Object} data - 數據對象
     * @returns {string} HTML字符串
     */
    generateProcessingSummaryHTML(data) {
        console.log('📋 生成處理結果摘要，收到數據:', data);
        
        // 檢查雙重搜尋結果
        let searchStatus = '未知';
        let searchMethod = '未檢測';
        let searchSuccess = false;
        
        if (data?.dual_search_result?.dual_search_result) {
            const dualData = data.dual_search_result.dual_search_result;
            searchSuccess = dualData.success;
            
            if (dualData.search_method === 'main_region_complete') {
                searchMethod = '主要區間完全匹配';
            } else if (dualData.search_method === 'backup_region_mapped') {
                searchMethod = '備用區間映射匹配';
            } else if (dualData.search_method) {
                searchMethod = dualData.search_method;
            }
            
            searchStatus = searchSuccess ? '成功' : '失敗';
        }
        
        // 檢查整體處理狀態
        let overallStatus = '成功';
        let overallDesc = '所有步驟成功完成';
        
        if (data?.status === 'error' || data?.error) {
            overallStatus = '失敗';
            overallDesc = data?.message || data?.error || '處理過程中發生錯誤';
        } else if (!searchSuccess) {
            overallStatus = '部分成功';
            overallDesc = '處理完成但搜尋匹配失敗';
        }
        
        const overallClass = overallStatus === '成功' ? 'success' : (overallStatus === '失敗' ? 'error' : 'warning');
        const searchClass = searchSuccess ? 'success' : 'warning';
        
        return `
            <div class="processing-summary">
                <h4><i class="fas fa-clipboard-check"></i> 處理結果摘要</h4>
                <div class="summary-grid">
                    <div class="summary-item ${overallClass}">
                        <div class="summary-icon">
                            <i class="fas fa-${overallStatus === '成功' ? 'check-circle' : (overallStatus === '失敗' ? 'times-circle' : 'exclamation-triangle')}"></i>
                        </div>
                        <div class="summary-text">
                            <div class="summary-title">整體處理狀態</div>
                            <div class="summary-desc">${overallDesc}</div>
                        </div>
                    </div>
                    <div class="summary-item ${searchClass}">
                        <div class="summary-icon">
                            <i class="fas fa-${searchSuccess ? 'sync' : 'exclamation-triangle'}"></i>
                        </div>
                        <div class="summary-text">
                            <div class="summary-title">雙重搜尋機制</div>
                            <div class="summary-desc">${searchMethod} (${searchStatus})</div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }
    
    /**
     * 生成 Site 分布資訊 HTML
     * @param {Object} data - 數據對象
     * @returns {string} HTML字符串
     */
    generateSiteDistributionHTML(data) {
        console.log('🌐 生成Site分布HTML，收到數據:', data);

        // 🔍 詳細調試數據結構
        console.log('🔍 Site數據調試:');
        console.log('  - data.summary_data:', data?.summary_data);
        console.log('  - this.realEQCData:', this.realEQCData);
        console.log('  - 所有可能路徑:', {
            path1: data?.summary_data?.site_details,
            path2: data?.strategy_b_result?.site_statistics,
            path3: data?.eqcResult?.site_distribution,
            path4: this.realEQCData?.summary_data,
            path5: data?.summary_data?.site_stats,
            path6: this.realEQCData?.summary_data?.site_stats
        });

        // 檢查多種可能的數據來源
        let siteData = null;

        // 🎯 優先從 realEQCData 的 summary_data.site_details 讀取 (最準確的數據)
        if (this.realEQCData?.summary_data?.site_details) {
            siteData = {
                site_details: this.realEQCData.summary_data.site_details,
                site_stats: this.realEQCData.summary_data.site_stats
            };
            console.log('✅ 從realEQCData.summary_data.site_details找到Site數據');
        }
        // 方式2: 從data.summary_data.site_details檢查
        else if (data?.summary_data?.site_details) {
            siteData = {
                site_details: data.summary_data.site_details,
                site_stats: data.summary_data.site_stats
            };
            console.log('✅ 從data.summary_data.site_details找到Site數據');
        }
        // 方式3: 從data.summary_data.site_stats檢查 (舊格式兼容)
        else if (data?.summary_data?.site_stats) {
            siteData = { site_stats: data.summary_data.site_stats };
            console.log('✅ 從data.summary_data.site_stats找到Site數據');
        }
        // 方式4: 從strategy_b_result檢查
        else if (data?.strategy_b_result?.site_statistics) {
            siteData = { site_stats: data.strategy_b_result.site_statistics };
            console.log('✅ 從strategy_b_result找到Site數據');
        }
        // 方式5: 從eqcResult檢查
        else if (data?.eqcResult?.site_distribution) {
            siteData = { site_stats: data.eqcResult.site_distribution };
            console.log('✅ 從eqcResult找到Site數據');
        }
        // 方式6: 從realEQCData.summary_data.site_stats檢查
        else if (this.realEQCData?.summary_data?.site_stats) {
            siteData = { site_stats: this.realEQCData.summary_data.site_stats };
            console.log('✅ 從realEQCData.summary_data.site_stats找到Site數據');
        }
        
        // 🔧 容錯處理：嘗試從其他可能的數據結構中解析
        if (!siteData || !siteData.site_stats) {
            console.log('⚠️ 標準路徑未找到Site數據，嘗試容錯解析...');

            // 嘗試從statistics中解析
            if (data?.statistics?.site_stats) {
                siteData = { site_stats: data.statistics.site_stats };
                console.log('✅ 從data.statistics.site_stats找到Site數據');
            }
            // 嘗試從data.data中解析（API響應可能有嵌套）
            else if (data?.data?.summary_data?.site_stats) {
                siteData = { site_stats: data.data.summary_data.site_stats };
                console.log('✅ 從data.data.summary_data.site_stats找到Site數據');
            }
            // 最後嘗試：直接搜索任何包含數字鍵的對象（Site統計的特徵）
            else {
                const potentialSiteData = this._findSiteDataRecursively(data);
                if (potentialSiteData) {
                    siteData = { site_stats: potentialSiteData };
                    console.log('✅ 通過遞歸搜索找到Site數據');
                }
            }
        }

        if (!siteData || !siteData.site_stats) {
            console.log('❌ 所有方法都未找到Site分布數據');
            console.log('📋 完整數據結構:', JSON.stringify(data, null, 2));
            return `
                <div style="margin-top: 20px;">
                    <h4><i class="fas fa-chart-pie"></i> Site 分布統計</h4>
                    <div style="text-align: center; padding: 20px; color: #6c757d; background: #f8f9fa; border-radius: 8px;">
                        <i class="fas fa-info-circle" style="margin-bottom: 10px;"></i>
                        <p>Site分布功能已實作，但此次處理未檢測到Site欄位</p>
                        <small>請確認資料夾中的CSV檔案包含Site_No或類似欄位</small>
                    </div>
                </div>
            `;
        }
        
        // 動態生成Site統計 - 使用REF檔案的詳細BIN分布方式
        let siteHTML = '';

        // 🎯 優先使用新格式的 site_details (包含詳細BIN分布)
        if (siteData.site_details) {
            console.log('🎯 使用詳細的site_details數據:', siteData.site_details);

            // 處理 site1 和 site2
            ['site1', 'site2'].forEach((siteKey, index) => {
                const siteDetails = siteData.site_details[siteKey];
                if (siteDetails && siteDetails.length > 0) {
                    const siteNumber = siteKey.replace('site', '');

                    // 計算總數
                    const total = siteDetails.reduce((sum, item) => sum + (item.count || 0), 0);

                    // 生成該Site的BIN分布詳情
                    const binItems = this._generateSiteBinItemsFromDetails(siteDetails);

                    siteHTML += `
                        <div style="padding: 12px; background: #fff; border-radius: 8px; border: 1px solid #e3e6f0;">
                            <h6 style="margin: 0 0 10px 0; color: #667eea; font-size: 12px; font-weight: bold;">
                                Site${siteNumber} (總數: ${total})
                            </h6>
                            ${binItems}
                        </div>
                    `;
                }
            });
        }
        // 🔄 回退到舊格式的 site_stats (兼容性)
        else if (siteData.site_stats) {
            console.log('🔄 使用舊格式的site_stats數據:', siteData.site_stats);

            const siteStats = siteData.site_stats;
            const siteKeys = Object.keys(siteStats).sort();

            siteKeys.forEach((siteKey, index) => {
                const siteInfo = siteStats[siteKey];
                const siteNumber = siteKey.replace('site', '').replace('_stats', '') || (index + 1);
                const total = siteInfo.total || 0;

                // 生成該Site的BIN分布詳情
                const binItems = this._generateSiteBinItems(siteInfo);

                siteHTML += `
                    <div style="padding: 12px; background: #fff; border-radius: 8px; border: 1px solid #e3e6f0;">
                        <h6 style="margin: 0 0 10px 0; color: #667eea; font-size: 12px; font-weight: bold;">
                            Site${siteNumber} (總數: ${total})
                        </h6>
                        ${binItems}
                    </div>
                `;
            });
        }
        
        return `
            <div style="margin-top: 20px;">
                <h4><i class="fas fa-chart-pie"></i> Site 分布統計</h4>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
                    ${siteHTML}
                </div>
            </div>
        `;
    }

    /**
     * 遞歸搜索Site數據的輔助方法
     * @param {Object} obj - 要搜索的對象
     * @returns {Object|null} Site統計數據或null
     */
    _findSiteDataRecursively(obj) {
        if (!obj || typeof obj !== 'object') return null;

        // 檢查當前對象是否是Site統計格式
        // Site統計的特徵：鍵是數字，值包含total、pass、fail等屬性
        if (this._isSiteStatsFormat(obj)) {
            console.log('🎯 找到Site統計格式數據:', obj);
            return obj;
        }

        // 遞歸搜索子對象
        for (const key in obj) {
            if (obj.hasOwnProperty(key)) {
                const result = this._findSiteDataRecursively(obj[key]);
                if (result) return result;
            }
        }

        return null;
    }

    /**
     * 檢查對象是否符合Site統計格式
     * @param {Object} obj - 要檢查的對象
     * @returns {boolean} 是否符合格式
     */
    _isSiteStatsFormat(obj) {
        if (!obj || typeof obj !== 'object') return false;

        const keys = Object.keys(obj);
        if (keys.length === 0) return false;

        // 檢查是否所有鍵都是數字，且值包含統計屬性
        return keys.every(key => {
            const isNumericKey = /^\d+$/.test(key);
            const value = obj[key];
            const hasStatsProps = value &&
                typeof value === 'object' &&
                ('total' in value || 'pass' in value || 'fail' in value);
            return isNumericKey && hasStatsProps;
        });
    }

    /**
     * 從詳細的site_details生成BIN分布項目 - 新格式
     * @param {Array} siteDetails - Site詳細BIN分布數組
     * @returns {string} BIN項目的HTML
     */
    _generateSiteBinItemsFromDetails(siteDetails) {
        if (!siteDetails || !Array.isArray(siteDetails)) {
            return '<div style="color: #6c757d; font-size: 11px;">無數據</div>';
        }

        console.log('🎯 生成Site BIN項目，詳細數據:', siteDetails);

        // 將BIN1置頂，其他按數量降序排列
        const bin1Items = siteDetails.filter(item => item.bin === '1');
        const otherItems = siteDetails.filter(item => item.bin !== '1')
                                     .sort((a, b) => (b.count || 0) - (a.count || 0));

        let html = '';

        // 先顯示BIN1 (PASS)
        bin1Items.forEach(item => {
            html += this._createBinItem(item.bin, item.count, item.percentage.toFixed(1), item.definition, true);
        });

        // 再顯示其他BIN (FAIL)
        otherItems.forEach(item => {
            html += this._createBinItem(item.bin, item.count, item.percentage.toFixed(1), item.definition, false);
        });

        return html || '<div style="color: #6c757d; font-size: 11px;">無BIN數據</div>';
    }

    /**
     * 生成Site的BIN分布項目 - 對應REF檔案的renderSiteBinItems函數 (舊格式兼容)
     * @param {Object} siteInfo - Site統計信息
     * @returns {string} BIN項目的HTML
     */
    _generateSiteBinItems(siteInfo) {
        if (!siteInfo || !siteInfo.bins) {
            // 如果沒有詳細的bins數據，使用簡化顯示
            const passCount = siteInfo.pass || 0;
            const failCount = siteInfo.fail || 0;
            const total = siteInfo.total || 0;
            const passRate = total > 0 ? ((passCount / total) * 100).toFixed(1) : '0.0';

            let html = '';

            // 顯示PASS (假設為BIN1)
            if (passCount > 0) {
                html += this._createBinItem(1, passCount, passRate, 'All Pass', true);
            }

            // 顯示FAIL (假設為其他BIN)
            if (failCount > 0) {
                const failRate = total > 0 ? ((failCount / total) * 100).toFixed(1) : '0.0';
                html += this._createBinItem('FAIL', failCount, failRate, 'FAIL Items', false);
            }

            return html;
        }

        // 如果有詳細的bins數據，按REF檔案方式處理
        const bins = Object.entries(siteInfo.bins);

        // 將BIN1置頂，其他按數量降序排列
        const bin1Item = bins.find(([bin, data]) => bin === '1');
        const otherBins = bins.filter(([bin, data]) => bin !== '1')
                              .sort(([,a], [,b]) => (b.count || 0) - (a.count || 0))
                              .slice(0, 9); // 最多9個（因為BIN1已佔1個）

        let html = '';

        // 先顯示BIN1 (PASS)
        if (bin1Item) {
            const [bin, data] = bin1Item;
            const total = siteInfo.total || 0;
            const percentage = total > 0 ? ((data.count / total) * 100).toFixed(1) : '0.0';
            html += this._createBinItem(bin, data.count, percentage, 'All Pass', true);
        }

        // 再顯示其他BIN (FAIL)
        otherBins.forEach(([bin, data]) => {
            const total = siteInfo.total || 0;
            const percentage = total > 0 ? ((data.count / total) * 100).toFixed(1) : '0.0';
            const definition = data.definition || '定義不明';
            html += this._createBinItem(bin, data.count, percentage, definition, false);
        });

        return html;
    }

    /**
     * 創建單個BIN項目的HTML - 對應REF檔案的樣式
     * @param {string|number} bin - BIN編號
     * @param {number} count - 數量
     * @param {string} percentage - 百分比
     * @param {string} definition - 定義
     * @param {boolean} isPass - 是否為PASS
     * @returns {string} BIN項目HTML
     */
    _createBinItem(bin, count, percentage, definition, isPass) {
        const bgColor = isPass ? '#f0fff4' : '#fff5f5';
        const textColor = isPass ? '#22c55e' : '#dc3545';
        const borderColor = isPass ? '#22c55e' : '#dc3545';

        return `
            <div style="
                font-size: 11px;
                margin-bottom: 4px;
                padding: 6px 8px;
                border-radius: 4px;
                background: ${bgColor};
                color: ${textColor};
                border-left: 3px solid ${borderColor};
                transition: all 0.3s ease;
            "
            onmouseover="this.style.transform='translateX(2px)'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)';"
            onmouseout="this.style.transform='translateX(0)'; this.style.boxShadow='none';">
                BIN${bin}: <strong>${count}</strong> (${percentage}%)
                <span style="font-style: italic; opacity: 0.8; margin-left: 4px;">(${definition})</span>
            </div>
        `;
    }
    
    /**
     * 生成下載按鈕 HTML
     * @param {Object} data - 數據對象
     * @returns {string} HTML字符串
     */
    generateDownloadButtonsHTML(data) {
        console.log('🔽🔽🔽 DetailPanel v2.2 生成下載按鈕，收到數據:', data);
        console.log('🔍 數據結構分析:');
        console.log('  - data.step1_online_eqc:', data?.step1_online_eqc);
        console.log('  - data.step1_online_eqc.data:', data?.step1_online_eqc?.data);
        
        // 從 Step 1 結果中獲取檔案資訊
        const step1Data = data?.step1_online_eqc?.data;
        if (!step1Data) {
            console.log('⚠️ 沒有 Step 1 數據，跳過下載按鈕生成');
            console.log('📋 可用的數據鍵值:', Object.keys(data || {}));
            return '';
        }
        
        console.log('📁 Step 1 數據內容:', step1Data);
        console.log('📁 下載路徑:');
        console.log('  - eqctotaldata_download_path:', step1Data.eqctotaldata_download_path);
        console.log('  - eqctotaldata_raw_download_path:', step1Data.eqctotaldata_raw_download_path);
        
        const buttons = [];
        
        // EQCTOTALDATA.xlsx 按鈕
        if (step1Data.eqctotaldata_download_path) {
            buttons.push(this.generateSingleDownloadButton(
                step1Data.eqctotaldata_download_path,
                'EQCTOTALDATA.xlsx',
                'fas fa-file-excel',
                'linear-gradient(135deg, #28a745, #20c997)'
            ));
        }
        
        // EQCTOTALDATA_RAW.csv 按鈕  
        if (step1Data.eqctotaldata_raw_download_path) {
            buttons.push(this.generateSingleDownloadButton(
                step1Data.eqctotaldata_raw_download_path,
                'EQCTOTALDATA_RAW.csv',
                'fas fa-file-csv',
                'linear-gradient(135deg, #17a2b8, #007bff)'
            ));
        }
        
        // EQCTOTALDATA.csv 按鈕（如果有的話）
        if (step1Data.eqc_total_file) {
            const csvPath = step1Data.eqctotaldata_download_path 
                ? step1Data.eqctotaldata_download_path.replace('EQCTOTALDATA.xlsx', 'EQCTOTALDATA.csv')
                : null;
            if (csvPath) {
                buttons.push(this.generateSingleDownloadButton(
                    csvPath,
                    'EQCTOTALDATA.csv',
                    'fas fa-file-csv',
                    'linear-gradient(135deg, #6f42c1, #6610f2)'
                ));
            }
        }
        
        if (buttons.length === 0) {
            console.log('⚠️ 沒有可下載的檔案');
            return '';
        }
        
        console.log(`✅ 生成了 ${buttons.length} 個下載按鈕`);
        
        return `
            <div style="
                margin-bottom: 20px;
                padding: 15px;
                background: linear-gradient(135deg, #f8f9fa, #e9ecef);
                border-radius: 12px;
                border: 1px solid #dee2e6;
            ">
                <div style="
                    display: flex;
                    align-items: center;
                    margin-bottom: 12px;
                    color: #495057;
                ">
                    <i class="fas fa-download" style="
                        font-size: 16px;
                        color: #28a745;
                        margin-right: 8px;
                    "></i>
                    <h4 style="
                        margin: 0;
                        font-size: 14px;
                        font-weight: 600;
                    ">處理完成檔案下載</h4>
                </div>
                <div style="
                    display: flex;
                    flex-wrap: wrap;
                    gap: 8px;
                ">
                    ${buttons.join('')}
                </div>
            </div>
        `;
    }
    
    /**
     * 生成單個下載按鈕
     * @param {string} fullPath - 完整檔案路徑
     * @param {string} fileName - 檔案名稱  
     * @param {string} iconClass - 圖示類別
     * @param {string} gradient - 漸層背景
     * @returns {string} 按鈕 HTML
     */
    generateSingleDownloadButton(fullPath, fileName, iconClass, gradient) {
        const buttonId = `download-btn-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
        
        return `
            <button
                id="${buttonId}"
                data-full-path="${fullPath}"
                data-file-name="${fileName}"
                onclick="downloadFileByDataAttr('${buttonId}')"
                style="
                    background: ${gradient};
                    color: white;
                    border: none;
                    padding: 8px 12px;
                    border-radius: 6px;
                    cursor: pointer;
                    font-size: 11px;
                    font-weight: 500;
                    display: flex;
                    align-items: center;
                    gap: 6px;
                    transition: all 0.3s ease;
                    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
                "
                onmouseover="this.style.transform='translateY(-1px)'; this.style.boxShadow='0 4px 8px rgba(0,0,0,0.2)';"
                onmouseout="this.style.transform='translateY(0px)'; this.style.boxShadow='0 2px 4px rgba(0,0,0,0.1)';">
                <i class="${iconClass}"></i>
                <span>${fileName}</span>
            </button>
        `;
    }
    
    /**
     * 獲取當前狀態
     * @returns {Object} 當前狀態
     */
    getCurrentStatus() {
        return {
            isExpanded: this.isExpanded,
            hasData: !!this.currentData
        };
    }
}

// 創建全局實例
const detailPanel = new DetailPanelComponent();

// 導出模組
if (typeof module !== 'undefined' && module.exports) {
    module.exports = DetailPanelComponent;
} else if (typeof window !== 'undefined') {
    window.DetailPanelComponent = DetailPanelComponent;
    window.detailPanel = detailPanel;
}
