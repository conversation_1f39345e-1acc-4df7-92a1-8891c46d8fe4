# CSV to Excel Converter 系統架構文檔 v3.0

**創建日期**: 2025-06-18  
**版本**: 3.0  
**檔案**: `csv_to_excel_converter.py` (1564行)  
**狀態**: ❌ 超過CLAUDE.md限制 (>500行)

---

## 📊 系統架構圖 (TXT格式)

```
================================================================================
                    CSV to Excel Converter 系統架構圖 (1564行)
================================================================================

┌─────────────────────────────────────────────────────────────────────────────┐
│                          CsvToExcelConverter 主類                            │
│                           (1564行 > 500行限制)                              │
└─────────────────────────────────────────────────────────────────────────────┘
                                    │
                                    ▼
┌─────────────────────────────────────────────────────────────────────────────┐
│                           🎯 核心8步驟流程                                    │
├─────────────────────────────────────────────────────────────────────────────┤
│  convert_csv_to_excel() ←── 主入口函數                                      │
│     │                                                                       │
│     ├── 步驟1: _safe_read_csv()          ─→ CSV安全讀取                      │
│     ├── 步驟2: complete_csv_structure()  ─→ 結構補全(C7-C11)                │
│     ├── 步驟3: assign_bin_numbers_df()   ─→ BIN號碼分配(第6行)              │
│     ├── 步驟4: apply_bin1_protection_df() ─→ BIN1保護機制                   │
│     ├── 步驟5: apply_device_bin_classification() ─→ 向量化BIN分類            │
│     ├── 步驟6: site_finder整合           ─→ Site欄位查找                     │
│     ├── 步驟7: strategy_b_processor整合  ─→ Site統計分析                     │
│     └── 步驟8: 生成Excel檔案            ─→ xlsxwriter輸出                   │
└─────────────────────────────────────────────────────────────────────────────┘
                                    │
                                    ▼
┌─────────────────────────────────────────────────────────────────────────────┐
│                        🔧 核心處理函數 (約800行)                             │
├─────────────────────────────────────────────────────────────────────────────┤
│  💾 DataFrame處理:                                                          │
│     ├── _safe_read_csv()                ─→ 安全CSV讀取                       │
│     ├── complete_csv_structure()        ─→ 確保基本結構                      │
│     └── assign_bin_numbers_df()         ─→ 動態BIN分配                       │
│                                                                             │
│  🛡️ 向量化優化:                                                             │
│     ├── apply_bin1_protection_df()      ─→ BIN1保護邏輯                      │
│     ├── apply_device_bin_classification() ─→ 設備分類                        │
│     ├── _vectorized_safe_float_conversion() ─→ 數值轉換                      │
│     ├── _vectorized_failure_check()     ─→ 失敗檢測                          │
│     └── _vectorized_zero_check()        ─→ 零值檢測                          │
│                                                                             │
│  📊 統計計算:                                                               │
│     ├── calculate_bin_statistics()      ─→ BIN統計                           │
│     └── calculate_site_statistics()     ─→ Site統計                          │
└─────────────────────────────────────────────────────────────────────────────┘
                                    │
                                    ▼
┌─────────────────────────────────────────────────────────────────────────────┐
│                       📋 外部組件整合 (約300行)                              │
├─────────────────────────────────────────────────────────────────────────────┤
│  🔍 SiteColumnFinder:                                                       │
│     ├── find_site_column()             ─→ 動態Site欄位查找                   │
│     └── find_site_column_with_log()    ─→ 詳細查找記錄                       │
│                                                                             │
│  📈 StrategyBProcessor:                                                      │
│     ├── execute_strategy_b()           ─→ Site統計分析                       │
│     └── get_site_statistics()          ─→ Site數據提取                       │
│                                                                             │
│  📊 SummaryGenerator:                                                        │
│     ├── generate_summary()             ─→ Summary工作表生成                  │
│     ├── calculate_bin_statistics()     ─→ BIN統計計算                        │
│     └── calculate_site_statistics()    ─→ Site統計計算                       │
└─────────────────────────────────────────────────────────────────────────────┘
                                    │
                                    ▼
┌─────────────────────────────────────────────────────────────────────────────┐
│                        🎨 Excel輸出模組 (約464行)                            │
├─────────────────────────────────────────────────────────────────────────────┤
│  📋 主資料工作表:                                                            │
│     ├── create_main_worksheet()        ─→ 主資料表創建                       │
│     ├── write_batch_data()             ─→ 批量數據寫入                       │
│     ├── apply_red_formatting()         ─→ 紅色格式標記                       │
│     └── create_hyperlinks()            ─→ BIN超連結創建                      │
│                                                                             │
│  📊 Summary工作表:                                                           │
│     ├── create_summary_worksheet()     ─→ Summary表創建                      │
│     ├── write_summary_data()           ─→ Summary數據寫入                    │
│     └── format_summary_layout()        ─→ Summary格式設定                    │
│                                                                             │
│  🔧 格式處理:                                                               │
│     ├── setup_excel_formats()          ─→ Excel格式定義                      │
│     ├── apply_eqc_formatting()         ─→ EQC特殊格式                        │
│     └── freeze_panes_setup()           ─→ 凍結視窗設定                       │
└─────────────────────────────────────────────────────────────────────────────┘
```

## 📊 數據流向圖

```
================================================================================
                              📊 數據流向圖
================================================================================

CSV檔案輸入
    │
    ▼
┌─────────────┐    ┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│ 步驟1: 讀取  │───▶│ 步驟2: 補全  │───▶│ 步驟3: BIN分配│───▶│ 步驟4: BIN1保護│
│_safe_read_csv│    │complete_csv │    │assign_bin   │    │apply_bin1   │
└─────────────┘    └─────────────┘    └─────────────┘    └─────────────┘
                                                              │
                                                              ▼
┌─────────────┐    ┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│步驟8: Excel輸出│◀──│步驟7: Summary│◀──│步驟6: Site統計│◀──│步驟5: 設備分類 │
│xlsxwriter   │    │generate     │    │strategy_b   │    │apply_device │
└─────────────┘    └─────────────┘    └─────────────┘    └─────────────┘
```

## 🔧 核心8步驟詳細說明

### 步驟1: CSV安全讀取 (`_safe_read_csv`)
- **功能**: 安全讀取CSV檔案，處理編碼和格式問題
- **輸入**: CSV檔案路徑
- **輸出**: pandas DataFrame
- **特色**: 自動處理不同編碼，統一欄數

### 步驟2: 結構補全 (`complete_csv_structure`)
- **功能**: 確保CSV有必要的基本結構
- **處理**: C7-C11欄位補全
- **規則**: 
  - C7: "0.00.01" (測試項目分組)
  - C8: "Test_Time" (測試項目名稱)
  - C10: "none" (Max限值)
  - C11: "none" (Min限值)

### 步驟3: BIN號碼分配 (`assign_bin_numbers_df`)
- **功能**: 動態分配BIN號碼到第6行
- **邏輯**: 從C欄開始，BIN號碼從5開始遞增
- **結果**: 每個測試項目對應一個唯一BIN號碼

### 步驟4: BIN1保護機制 (`apply_bin1_protection_df`)
- **功能**: 向量化BIN1設備保護邏輯
- **保護條件**: BIN1設備會失敗且數值為0的測試項目
- **動作**: 放寬限值至"none"
- **性能**: 向量化處理，支援百萬次檢查/秒

### 步驟5: 設備BIN分類 (`apply_device_bin_classification`)
- **功能**: 向量化所有設備的BIN分類
- **邏輯**: 比較測試值與上下限，分配對應BIN
- **優化**: 使用numpy廣播操作，大幅提升性能

### 步驟6: Site欄位查找 (`site_finder整合`)
- **功能**: 動態查找Site欄位位置
- **外部組件**: SiteColumnFinder
- **搜尋邏輯**: 在第8行中查找包含"site"但不包含"site_check"的欄位

### 步驟7: Site統計分析 (`strategy_b_processor整合`)
- **功能**: 執行Site統計分析
- **外部組件**: StrategyBProcessor
- **統計內容**: 各Site的Pass/Fail數量和比率

### 步驟8: Excel檔案生成 (`xlsxwriter輸出`)
- **功能**: 生成完整的Excel檔案
- **包含**: 主資料工作表 + Summary工作表
- **格式**: 紅色標記、超連結、凍結視窗

## 📋 主要函數清單

### 🔧 核心處理函數
| 函數名稱 | 功能說明 | 參數 | 返回值 |
|---------|---------|------|--------|
| `convert_csv_to_excel()` | 主要轉換函數 | csv_path, excel_path | ConversionResult |
| `_safe_read_csv()` | 安全CSV讀取 | csv_file_path | DataFrame |
| `complete_csv_structure()` | 結構補全 | DataFrame | DataFrame |
| `assign_bin_numbers_df()` | BIN分配 | DataFrame | DataFrame |
| `apply_bin1_protection_df()` | BIN1保護 | DataFrame | DataFrame, positions |
| `apply_device_bin_classification()` | 設備分類 | DataFrame | DataFrame, positions |

### 🎨 Excel輸出函數
| 函數名稱 | 功能說明 | 參數 | 返回值 |
|---------|---------|------|--------|
| `create_main_worksheet()` | 主工作表創建 | workbook, data | worksheet |
| `create_summary_worksheet()` | Summary表創建 | workbook, summary_data | worksheet |
| `write_batch_data()` | 批量寫入 | worksheet, data | None |
| `apply_red_formatting()` | 紅色標記 | worksheet, positions | None |
| `create_hyperlinks()` | 超連結創建 | worksheet, fail_devices | None |

### 🛡️ 向量化優化函數
| 函數名稱 | 功能說明 | 參數 | 返回值 |
|---------|---------|------|--------|
| `_vectorized_safe_float_conversion()` | 數值轉換 | data_array | numeric_array |
| `_vectorized_failure_check()` | 失敗檢測 | values, max_limit, min_limit | bool_array |
| `_vectorized_zero_check()` | 零值檢測 | values | bool_array |

## 🔗 外部組件依賴

### SiteColumnFinder
- **檔案**: `site_column_finder.py`
- **功能**: 動態Site欄位查找
- **整合點**: 步驟6

### StrategyBProcessor  
- **檔案**: `strategy_b_processor.py`
- **功能**: Site統計分析
- **整合點**: 步驟7

### SummaryGenerator
- **檔案**: `summary_generator.py`
- **功能**: Summary工作表生成
- **整合點**: 步驟7-8

### AdvancedPerformanceManager
- **檔案**: `advanced_performance_manager.py`
- **功能**: 性能監控和記憶體管理
- **整合點**: 全程監控

## ⚠️ 問題分析

### 🔴 檔案大小問題
- **當前狀態**: 1564行 (超過CLAUDE.md限制214%)
- **限制要求**: ≤500行
- **主要原因**: 
  - Excel輸出邏輯過於冗長 (~464行)
  - 向量化優化函數較多 (~300行)
  - 外部組件整合邏輯 (~300行)

### 🔴 性能瓶頸
根據實際測試結果：
- **Excel輸出**: 88.7% (最大瓶頸)
- **向量化BIN分類**: 5.2%
- **BIN1保護**: 3.0%
- **其他步驟**: <3%

### 🔴 ft_summary_generator.py 行數問題
**為什麼525行？**
```
ft_summary_generator.py (525行) 包含：
├── 批量檔案掃描邏輯 (66-109行)
├── CSV檔案過濾處理 (111-143行) 
├── Summary區塊創建 (145-187行)
├── 橫向拼接計算邏輯 (189-237行)
├── 顏色映射建立 (239-280行)
├── Excel格式化輸出 (282-426行) ⬅️ 佔用最多行數
└── 測試和驗證邏輯 (428-525行)
```

**問題**: 重複實現了大量Excel輸出邏輯，應該直接使用`csv_to_excel_converter.convert_csv_to_excel()`

## 💡 改善建議

### 1. 模組化拆分
建議將`csv_to_excel_converter.py`拆分為：
- **核心轉換模組** (4步驟，約300行)
- **向量化優化模組** (約200行)
- **Excel輸出模組** (約400行)
- **EQC格式處理模組** (約200行)

### 2. ft_summary_generator簡化
- 移除重複的Excel輸出邏輯
- 直接使用`csv_to_excel_converter.convert_csv_to_excel()`
- 專注於橫向拼接邏輯
- 目標：從525行減少至≤300行

### 3. 性能優化
- 優化xlsxwriter操作（主要瓶頸）
- 減少記憶體使用
- 改善批量寫入效率

---

**文檔版本**: v3.0  
**最後更新**: 2025-06-18  
**分析基準**: CLAUDE.md功能替換原則與500行限制