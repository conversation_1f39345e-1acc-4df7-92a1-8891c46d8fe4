#!/usr/bin/env python3
"""
CTA 格式解析器
解析 CTA (Chip Test Assistant) 格式的 CSV 檔案
提取各區段資料和設備測試資料
"""

import pandas as pd
from typing import Dict, List, Any, Optional, Tuple


class CTAParser:
    """CTA 格式解析器"""
    
    def __init__(self):
        """初始化解析器"""
        # CTA 格式的區段標識
        self.section_markers = {
            'GENERAL': '[GENERAL]',
            'WAFERCONFIG': '[WAFERCONFIG]', 
            'SUMMARY': '[SUMMARY]',
            'SWBININFO': '[SWBININFO]',
            'HARDWAREBIN': '[HARDWAREBIN]',
            'PARAMINFO': '[PARAMINFO]',
            'Data': '[Data]'
        }
        
    def parse_sections(self, file_path: str) -> Dict[str, List[str]]:
        """
        解析 CTA 檔案的各個區段
        
        Args:
            file_path: CTA 檔案路徑
            
        Returns:
            字典，包含各區段的內容行
        """
        sections = {}
        
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                lines = f.readlines()
                
            current_section = None
            
            for i, line in enumerate(lines):
                line = line.strip()
                
                # 檢查是否是區段標記
                for section_name, marker in self.section_markers.items():
                    if marker in line:
                        current_section = section_name
                        sections[section_name] = []
                        break
                else:
                    # 如果當前在某個區段中，添加行內容
                    if current_section and line:
                        sections[current_section].append(line)
                        
        except Exception as e:
            raise ValueError(f"解析 CTA 檔案失敗: {e}")
            
        return sections
    
    def extract_device_data(self, file_path: str) -> pd.DataFrame:
        """
        提取 CTA 檔案中的設備測試資料
        
        Args:
            file_path: CTA 檔案路徑
            
        Returns:
            包含設備資料的 DataFrame
        """
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                lines = f.readlines()
                
            # 尋找 [Data] 區段
            data_start_line = None
            header_line = None
            
            for i, line in enumerate(lines):
                if '[Data]' in line:
                    data_start_line = i
                    # 尋找標頭行 (通常在 [Data] 後的 1-3 行內)
                    for j in range(i + 1, min(i + 5, len(lines))):
                        if 'Serial_No' in lines[j] and 'SW_Bin' in lines[j] and 'Site_No' in lines[j]:
                            header_line = j
                            break
                    break
                    
            if data_start_line is None or header_line is None:
                raise ValueError("找不到 Data 區段或標頁行")
                
            # 從標頁行開始讀取資料
            data_lines = []
            
            # 標頭行
            header = lines[header_line].strip().split(',')
            
            # 資料行 (從標頁行的下一行開始)
            for i in range(header_line + 1, len(lines)):
                line = lines[i].strip()
                if line and not line.startswith('['):  # 跳過空行和新區段
                    # 分割 CSV 行，考慮可能的引號包圍
                    values = self._parse_csv_line(line)
                    if len(values) >= len(header):  # 確保有足夠的欄位
                        data_lines.append(values[:len(header)])  # 只取標頁對應的欄位數
                        
            if not data_lines:
                raise ValueError("沒有找到設備資料")
                
            # 建立 DataFrame
            df = pd.DataFrame(data_lines, columns=header)
            
            # 資料清理
            df = self._clean_device_data(df)
            
            return df
            
        except Exception as e:
            raise ValueError(f"提取設備資料失敗: {e}")
    
    def _parse_csv_line(self, line: str) -> List[str]:
        """
        解析 CSV 行，處理逗號分隔值
        
        Args:
            line: CSV 行字串
            
        Returns:
            分割後的值列表
        """
        # 簡單的 CSV 解析，處理基本情況
        # 對於更複雜的 CSV（包含引號、轉義字符等），可以使用 csv 模組
        values = []
        current_value = ""
        in_quotes = False
        
        for char in line:
            if char == '"' and not in_quotes:
                in_quotes = True
            elif char == '"' and in_quotes:
                in_quotes = False
            elif char == ',' and not in_quotes:
                values.append(current_value.strip())
                current_value = ""
            else:
                current_value += char
                
        # 添加最後一個值
        values.append(current_value.strip())
        
        return values
    
    def _clean_device_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        清理設備資料
        
        Args:
            df: 原始 DataFrame
            
        Returns:
            清理後的 DataFrame
        """
        # 移除完全空白的行
        df = df.dropna(how='all')
        
        # 確保關鍵欄位存在
        required_columns = ['Serial_No', 'SW_Bin', 'Site_No']
        for col in required_columns:
            if col not in df.columns:
                raise ValueError(f"缺少必要欄位: {col}")
                
        # 移除 Serial_No 為空的行
        df = df[df['Serial_No'].notna() & (df['Serial_No'] != '')]
        
        # 轉換數據類型
        try:
            df['SW_Bin'] = pd.to_numeric(df['SW_Bin'], errors='coerce')
            df['Site_No'] = pd.to_numeric(df['Site_No'], errors='coerce')
        except Exception:
            pass  # 如果轉換失敗，保持原始格式
            
        # 移除關鍵欄位為 NaN 的行
        df = df.dropna(subset=['Serial_No', 'SW_Bin', 'Site_No'])
        
        # 重設索引
        df = df.reset_index(drop=True)
        
        return df
    
    def get_summary_info(self, file_path: str) -> Dict[str, Any]:
        """
        獲取 CTA 檔案的摘要資訊
        
        Args:
            file_path: CTA 檔案路徑
            
        Returns:
            包含摘要資訊的字典
        """
        try:
            sections = self.parse_sections(file_path)
            device_data = self.extract_device_data(file_path)
            
            # 統計資訊
            total_devices = len(device_data)
            bin_distribution = device_data['SW_Bin'].value_counts().to_dict()
            site_distribution = device_data['Site_No'].value_counts().to_dict()
            
            # Pass/Fail 統計 (假設 BIN 1 為 Pass)
            pass_devices = len(device_data[device_data['SW_Bin'] == 1])
            fail_devices = total_devices - pass_devices
            
            summary = {
                'file_path': file_path,
                'sections_found': list(sections.keys()),
                'total_devices': total_devices,
                'pass_devices': pass_devices,
                'fail_devices': fail_devices,
                'pass_rate': pass_devices / total_devices if total_devices > 0 else 0,
                'bin_distribution': bin_distribution,
                'site_distribution': site_distribution,
                'columns': list(device_data.columns)
            }
            
            return summary
            
        except Exception as e:
            raise ValueError(f"獲取摘要資訊失敗: {e}")