
[ 系統流程圖：EQC 進階處理自動化流程 ]

+---------------------------------------------------------------------------------+
|                                                                                 |
|   [使用者] --- (HTTP POST) ---> /api/process_eqc_advanced (API 端點)             |
|       |                                                                         |
+-------|-------------------------------------------------------------------------+
        |
        v
+---------------------------------------------------------------------------------+
| [服務層] src/presentation/api/services/eqc_processing_service.py                |
|                                                                                 |
|   [ EQCProcessingService.process_eqc_advanced() ]                               |
|       |                                                                         |
|       | 1. 呼叫前置處理 (如果需要)                                                |
|       |    - process_online_eqc() -> 生成 EQCTOTALDATA.csv                      |
|       |                                                                         |
|       '------------> 2. 呼叫核心處理流程                                         |
|                                                                                 |
+-----------------------|---------------------------------------------------------+
                        |
                        v
+---------------------------------------------------------------------------------+
| [核心協同者] eqc_standard_processor.py                                          |
|                                                                                 |
|   [ StandardEQCProcessor.process_code_comparison_pipeline() ]                   |
|       |                                                                         |
|       |---[ 階段 1: 讀取與準備 ]------------------------------------------------|
|       |   - _read_eqc_total_data() : 讀取 EQCTOTALDATA.csv                      |
|       |   - _detect_code_regions_from_summary() : 自動檢測 Main/Backup 區間     |
|       |                                                                         |
|       |---[ 階段 2: 核心搜尋 ]--------------------------------------------------|
|       |   - _perform_dual_search() : 執行雙重搜尋 (Main -> Backup)              |
|       |       - 逐行分析，記錄 PASS/FAIL                                        |
|       |                                                                         |
|       |---[ 階段 3: 程式碼對比 ]------------------------------------------------|
|       |   - _compare_and_highlight_code() : 比較 Golden IC 與 Fail IC           |
|       |       - 產生差異數據和高亮指令                                          |
|       |                                                                         |
|       '------------> [ 階段 4: 生成報告 ]                                        |
|                           - 呼叫底層 Excel 處理器                               |
|                                                                                 |
+---------------------------|-----------------------------------------------------+
                            |
                            v
+---------------------------------------------------------------------------------+
| [基礎設施層] src/infrastructure/adapters/excel/eqc/eqc_step6_excel_processor.py |
|                                                                                 |
|   [ EQCStep6ExcelProcessor ]                                                    |
|       |                                                                         |
|       |--- 1. process_and_highlight_results()                                  |
|       |      - 建立新的 Excel 檔案                                             |
|       |      - 填入雙重搜尋結果                                                |
|       |      - 根據高亮指令對儲存格上色                                        |
|       |      - 輸出: EQCTOTALDATA_Step6_HighlightedEQCRT_... .xlsx              |
|       |                                                                         |
|       '--- 2. rename_step6_to_final_report()                                   |
|              - 將 Step6 檔案重命名                                            |
|              - 輸出: EQCTOTALDATA.xlsx (最終報告)                               |
|                                                                                 |
+---------------------------------------------------------------------------------+
        |
        v
+---------------------------------------------------------------------------------+
|                                                                                 |
|   [最終產出] EQCTOTALDATA.xlsx                                                  |
|       - 包含所有處理結果                                                        |
|       - 差異處已高亮顯示                                                        |
|                                                                                 |
+---------------------------------------------------------------------------------+

[ 圖例 ]
  ---> : 資料流或呼叫方向
  ...  : 表示處理過程
  []   : 模組、類別或方法
  ()   : 動作或協定

