#!/usr/bin/env python3
"""
簡化測試：只測試批量處理器是否可以匯入
"""

def test_batch_processor():
    """測試批量處理器匯入"""
    try:
        from batch_csv_to_excel_processor import BatchCsvToExcelProcessor
        print("✅ BatchCsvToExcelProcessor 匯入成功")
        
        # 測試初始化
        processor = BatchCsvToExcelProcessor(enable_logging=False)
        print("✅ 批量處理器初始化成功")
        
        return True
    except Exception as e:
        print(f"❌ 批量處理器測試失敗: {e}")
        return False

if __name__ == "__main__":
    print("🎯 簡化測試")
    print("=" * 30)
    
    if test_batch_processor():
        print("🎉 核心功能測試通過！")
        print("💡 API 應該可以正常啟動")
    else:
        print("❌ 核心功能測試失敗")