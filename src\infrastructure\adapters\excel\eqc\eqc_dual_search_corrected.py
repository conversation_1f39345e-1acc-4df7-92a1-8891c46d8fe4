#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
EQC 雙重搜尋機制
- 主要區間：100%完全匹配驗證
- 備用區間：動態映射到主要區間前N個欄位
- 支援前端自定義 CODE 區間設定
"""

import logging
from typing import Dict, List, Any, Optional, Tuple

class EQCDualSearchCorrected:
    """
    EQC 雙重搜尋機制
    - 主要區間：100%完全匹配驗證（預設第298-335欄，支援自定義）
    - 備用區間：動態映射匹配（預設第1565-1600欄，支援自定義）
    - 映射邏輯：備用區間N個欄位對應主要區間前N個欄位
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def perform_corrected_dual_search(self, 
                                    eqctotaldata_rows: List[str],
                                    main_region: Dict[str, Any],
                                    backup_region: Dict[str, Any],
                                    code_regions: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        執行修正版雙重搜尋機制
        
        Args:
            eqctotaldata_rows: EQCTOTALDATA.csv 的所有行
            main_region: 主要程式碼區間資訊 (第298-335欄)
            backup_region: 備用程式碼區間資訊 (第1565-1600欄)
            
        Returns:
            Dict: 匹配結果和插入位置資訊
        """
        try:
            self.logger.info("🔍 開始執行修正版雙重搜尋機制...")
            
            # 處理前端 CODE 區間設定（優先使用前端設定）
            if code_regions and any(v is not None for v in code_regions.values()):
                self.logger.info("🎯 使用前端自定義 CODE 區間設定")
                
                # 覆蓋主要區間設定
                if code_regions.get('main_start') is not None and code_regions.get('main_end') is not None:
                    main_region = {
                        'start_column_number': code_regions['main_start'],
                        'end_column_number': code_regions['main_end'],
                        'start1': code_regions['main_start'] - 1,  # 轉換為索引
                        'end1': code_regions['main_end'] - 1,      # 轉換為索引
                        'found': True
                    }
                    self.logger.info(f"   主要區間: 第{code_regions['main_start']}-{code_regions['main_end']}欄 (用戶設定)")
                
                # 覆蓋備用區間設定
                if code_regions.get('backup_start') is not None and code_regions.get('backup_end') is not None:
                    backup_region = {
                        'backup_start_column': code_regions['backup_start'],
                        'backup_end_column': code_regions['backup_end'],
                        'backup_start_index': code_regions['backup_start'] - 1,  # 轉換為索引
                        'backup_end_index': code_regions['backup_end'] - 1,      # 轉換為索引
                        'found': True
                    }
                    self.logger.info(f"   備用區間: 第{code_regions['backup_start']}-{code_regions['backup_end']}欄 (用戶設定)")
            else:
                self.logger.info("🔧 使用自動檢測的 CODE 區間設定")
            
            # 讀取欄位名稱
            if len(eqctotaldata_rows) <= 7:
                return {
                    'success': False,
                    'error': '檔案格式不正確，無法讀取第8行欄位名稱'
                }
            
            header_row = eqctotaldata_rows[7].split(',')
            
            # 第一重搜尋：主要區間（要求100%完全匹配）
            main_search_result = self._search_main_region_complete_match(
                header_row, main_region
            )
            
            if main_search_result['success']:
                self.logger.info("✅ 主要區間100%完全匹配成功")
                return {
                    'search_method': 'main_region_complete',
                    'success': True,
                    'matched_positions': main_search_result['matched_positions'],
                    'insertion_strategy': 'primary_complete_match',
                    'region_used': main_region,
                    'match_rate': '100%',
                    'total_matched': main_search_result['total_matched'],
                    'required_matched': main_search_result['required_matched']
                }
            
            # 第二重搜尋：備用區間（對應主要區間前36個欄位）
            self.logger.warning("⚠️ 主要區間無法100%匹配，切換到備用區間...")
            
            backup_search_result = self._search_backup_region_with_mapping(
                header_row, main_region, backup_region
            )
            
            if backup_search_result['success']:
                self.logger.info("✅ 備用區間匹配成功")
                return {
                    'search_method': 'backup_region_mapped',
                    'success': True,
                    'matched_positions': backup_search_result['matched_positions'],
                    'insertion_strategy': 'backup_mapped_match',
                    'region_used': backup_region,
                    'mapping_info': backup_search_result['mapping_info'],
                    'match_rate': backup_search_result['match_rate'],
                    'total_matched': backup_search_result['total_matched'],
                    'required_matched': backup_search_result['required_matched'],
                    'main_failure_reason': main_search_result['failure_details']
                }
            
            # 雙重搜尋都失敗
            self.logger.error("❌ 雙重搜尋都失敗")
            return {
                'search_method': 'dual_search_failed',
                'success': False,
                'main_failure_reason': main_search_result['failure_details'],
                'backup_failure_reason': backup_search_result['failure_details'],
                'recommendation': 'manual_review_required'
            }
            
        except Exception as e:
            self.logger.error(f"修正版雙重搜尋機制執行失敗: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def _search_main_region_complete_match(self, 
                                         header_row: List[str],
                                         main_region: Dict[str, Any]) -> Dict[str, Any]:
        """
        在主要區間進行100%完全匹配搜尋
        
        Args:
            header_row: 第8行的欄位名稱
            main_region: 主要程式碼區間 (第298-335欄，38個欄位)
            
        Returns:
            Dict: 搜尋結果
        """
        try:
            self.logger.info("🎯 在主要區間進行100%完全匹配檢查...")
            
            start_idx = main_region['start1']  # 297 (索引)
            end_idx = main_region['end1']      # 334 (索引)
            total_required = end_idx - start_idx + 1  # 38個欄位
            
            matched_positions = []
            missing_positions = []
            
            # 檢查主要區間的每一個欄位
            for i in range(start_idx, end_idx + 1):
                col_num = i + 1
                
                if i < len(header_row):
                    col_name = header_row[i].strip()
                    
                    # 檢查是否為有效的程式碼欄位（非空白）
                    if col_name and col_name != "":
                        matched_positions.append({
                            'column_index': i,
                            'column_number': col_num,
                            'column_name': col_name,
                            'region_type': 'main',
                            'match_type': 'complete'
                        })
                        self.logger.info(f"   ✅ 主要區間第{col_num}欄: '{col_name}'")
                    else:
                        missing_positions.append({
                            'column_index': i,
                            'column_number': col_num,
                            'reason': 'empty_or_missing'
                        })
                        self.logger.warning(f"   ❌ 主要區間第{col_num}欄: 空白或缺失")
                else:
                    missing_positions.append({
                        'column_index': i,
                        'column_number': col_num,
                        'reason': 'beyond_file_range'
                    })
                    self.logger.warning(f"   ❌ 主要區間第{col_num}欄: 超出檔案範圍")
            
            # 檢查是否達到100%匹配
            matched_count = len(matched_positions)
            is_complete_match = (matched_count == total_required) and (len(missing_positions) == 0)
            
            if is_complete_match:
                self.logger.info(f"   🎯 主要區間100%完全匹配: {matched_count}/{total_required}")
                return {
                    'success': True,
                    'matched_positions': matched_positions,
                    'total_matched': matched_count,
                    'required_matched': total_required,
                    'match_rate': '100%'
                }
            else:
                self.logger.warning(f"   ❌ 主要區間匹配不完整: {matched_count}/{total_required}")
                return {
                    'success': False,
                    'matched_positions': matched_positions,
                    'missing_positions': missing_positions,
                    'total_matched': matched_count,
                    'required_matched': total_required,
                    'match_rate': f'{matched_count/total_required*100:.1f}%',
                    'failure_details': {
                        'missing_count': len(missing_positions),
                        'missing_list': missing_positions
                    }
                }
            
        except Exception as e:
            return {
                'success': False,
                'failure_details': f'main_region_search_error: {str(e)}'
            }
    
    def _search_backup_region_with_mapping(self, 
                                         header_row: List[str],
                                         main_region: Dict[str, Any],
                                         backup_region: Dict[str, Any]) -> Dict[str, Any]:
        """
        在備用區間進行映射匹配
        備用區間的程式碼對應到主要區間的前N個欄位（N = 備用區間大小）
        
        Args:
            header_row: 第8行的欄位名稱
            main_region: 主要程式碼區間 
            backup_region: 備用程式碼區間（動態大小，例如第1565-1600欄）
            
        Returns:
            Dict: 搜尋結果
        """
        try:
            self.logger.info("🔄 在備用區間進行映射匹配...")
            
            # 主要區間起始位置
            main_start_idx = main_region['start1']  # 297 (索引)
            main_end_idx = main_region['end1']      # 334 (索引)
            
            # 備用區間的範圍
            backup_start_idx = backup_region['backup_start_index']  # 1564 (索引)
            backup_end_idx = backup_region['backup_end_index']      # 1599 (索引)
            backup_total = backup_end_idx - backup_start_idx + 1    # 動態計算備用區間大小
            
            # 映射範圍：使用備用區間的實際大小
            main_mapping_range = backup_total  # 備用區間有多少個欄位，就映射主要區間的前多少個欄位
            
            matched_positions = []
            mapping_info = []
            missing_mappings = []
            
            self.logger.info(f"   映射規則: 備用區間{backup_total}個欄位 → 主要區間前{main_mapping_range}個欄位")
            
            # 建立映射關係：備用區間 → 主要區間前N個欄位（N = 備用區間大小）
            for i in range(backup_total):
                main_idx = main_start_idx + i      # 主要區間對應位置
                backup_idx = backup_start_idx + i  # 備用區間對應位置
                
                main_col_num = main_idx + 1
                backup_col_num = backup_idx + 1
                
                # 檢查主要區間的欄位名稱
                main_col_name = ""
                if main_idx < len(header_row):
                    main_col_name = header_row[main_idx].strip()
                
                # 檢查備用區間的欄位名稱
                backup_col_name = ""
                if backup_idx < len(header_row):
                    backup_col_name = header_row[backup_idx].strip()
                
                # 驗證映射關係
                if main_col_name and backup_col_name and main_col_name == backup_col_name:
                    matched_positions.append({
                        'main_column_index': main_idx,
                        'main_column_number': main_col_num,
                        'backup_column_index': backup_idx,
                        'backup_column_number': backup_col_num,
                        'column_name': backup_col_name,
                        'region_type': 'backup',
                        'match_type': 'mapped'
                    })
                    
                    mapping_info.append({
                        'mapping_index': i,
                        'main_position': f"第{main_col_num}欄",
                        'backup_position': f"第{backup_col_num}欄",
                        'column_name': backup_col_name,
                        'status': 'matched'
                    })
                    
                    self.logger.info(f"   ✅ 映射匹配 {i+1}: 主要第{main_col_num}欄 ↔ 備用第{backup_col_num}欄 '{backup_col_name}'")
                else:
                    missing_mappings.append({
                        'mapping_index': i,
                        'main_position': f"第{main_col_num}欄",
                        'backup_position': f"第{backup_col_num}欄",
                        'main_column_name': main_col_name,
                        'backup_column_name': backup_col_name,
                        'reason': 'name_mismatch' if main_col_name != backup_col_name else 'empty_field'
                    })
                    
                    self.logger.warning(f"   ❌ 映射失敗 {i+1}: 主要第{main_col_num}欄'{main_col_name}' ↔ 備用第{backup_col_num}欄'{backup_col_name}'")
            
            # 評估備用區間匹配結果
            matched_count = len(matched_positions)
            total_required = backup_total  # 備用區間的所有欄位都必須匹配
            is_backup_success = matched_count == total_required
            
            if is_backup_success:
                self.logger.info(f"   🎯 備用區間映射完全成功: {matched_count}/{total_required}")
                return {
                    'success': True,
                    'matched_positions': matched_positions,
                    'mapping_info': mapping_info,
                    'total_matched': matched_count,
                    'required_matched': total_required,
                    'match_rate': '100%'
                }
            else:
                self.logger.warning(f"   ❌ 備用區間映射不完整: {matched_count}/{total_required}")
                return {
                    'success': False,
                    'matched_positions': matched_positions,
                    'mapping_info': mapping_info,
                    'missing_mappings': missing_mappings,
                    'total_matched': matched_count,
                    'required_matched': total_required,
                    'match_rate': f'{matched_count/total_required*100:.1f}%',
                    'failure_details': {
                        'missing_mapping_count': len(missing_mappings),
                        'missing_mapping_list': missing_mappings
                    }
                }
            
        except Exception as e:
            return {
                'success': False,
                'failure_details': f'backup_region_mapping_error: {str(e)}'
            }


def demonstrate_dual_search_mechanism():
    """展示 EQC 雙重搜尋機制"""
    
    print("🔍 EQC 雙重搜尋機制")
    print("=" * 80)
    
    print("📊 搜尋邏輯說明:")
    print()
    
    print("1️⃣ 主要區間匹配策略:")
    print("   🎯 區間範圍: 第298-335欄（共38個欄位）")
    print("   📏 匹配要求: 100%完全匹配（38/38個欄位都必須存在且有效）")
    print("   ✅ 成功條件: 所有38個欄位都必須有有效的程式碼名稱")
    print("   ❌ 失敗條件: 任何一個欄位缺失或為空白")
    print()
    
    print("2️⃣ 備用區間映射策略:")
    print("   🎯 區間範圍: 動態檢測（預設第1565-1600欄）")
    print("   🔗 映射關係: 備用區間N個欄位 → 主要區間前N個欄位")
    print("     • 動態映射：備用區間有多少個欄位，就映射主要區間的前多少個欄位")
    print("     • 範例：備用36個欄位 → 主要區間第298-333欄")
    print("   📏 匹配要求: 所有映射關係的程式碼名稱必須完全相同")
    print("   ✅ 成功條件: 備用區間的所有欄位名稱與對應的主要區間欄位名稱完全一致")
    print()
    
    print("3️⃣ 決策流程:")
    print("   🔍 Step 1: 檢查主要區間所有欄位是否100%完整")
    print("   📊 Step 2: 如果主要區間失敗，檢查備用區間動態映射是否100%匹配")
    print("   🎯 Step 3: 根據匹配結果選擇插入策略")
    print("   ❌ Step 4: 如果雙重搜尋都失敗，需要人工檢查")
    print()
    
    print("🛡️ 關鍵特性:")
    print("   ✅ 100%完全匹配要求：確保程式碼區間完整性")
    print("   ✅ 動態區間映射：備用區間自動對應主要區間前N個欄位")
    print("   ✅ 嚴格的欄位名稱一致性檢查")
    print("   ✅ 雙重備援機制：主要區間失敗時自動切換到備用區間")


if __name__ == "__main__":
    demonstrate_dual_search_mechanism()