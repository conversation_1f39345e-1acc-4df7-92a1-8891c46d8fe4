#!/usr/bin/env python3
"""
進階性能管理器 - 基於 pandas 2024 最佳實踐
遵循 CLAUDE.md 功能替換原則：完全取代舊版本
"""

import gc
import os
import psutil
import time
import logging
import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple
from pathlib import Path
from dataclasses import dataclass, field
from contextlib import contextmanager

@dataclass
class PerformanceMetrics:
    """性能指標數據類"""
    timestamp: float
    process_time: float
    memory_usage_mb: float
    memory_peak_mb: float
    gc_collections: Dict[int, int]
    dataframe_count: int
    temp_files_count: int
    temp_files_size_mb: float

@dataclass 
class PerformanceConfig:
    """性能管理配置"""
    # 記憶體閾值
    memory_warning_threshold_mb: float = 512.0
    memory_critical_threshold_mb: float = 1024.0
    
    # 檔案清理
    max_temp_files: int = 5
    max_temp_size_mb: float = 100.0
    
    # 性能趨勢
    performance_degradation_threshold: float = 0.3  # 30%
    min_samples_for_trend: int = 3
    
    # 自動清理
    auto_gc_interval: int = 5  # 每5次處理後強制 GC
    auto_cleanup_interval: int = 10  # 每10次處理後清理

class AdvancedPerformanceManager:
    """
    進階性能管理器
    基於 pandas 2024 記憶體管理最佳實踐
    """
    
    def __init__(self, config: Optional[PerformanceConfig] = None):
        self.config = config or PerformanceConfig()
        self.metrics_history: List[PerformanceMetrics] = []
        self.process_count = 0
        self.start_time = time.time()
        self.baseline_memory = self._get_current_memory()
        
        # 設置 logging
        self.logger = logging.getLogger(__name__)
        
    def _get_current_memory(self) -> float:
        """取得當前記憶體使用量 (MB)"""
        try:
            process = psutil.Process()
            return process.memory_info().rss / 1024 / 1024
        except Exception:
            return 0.0
    
    def _get_memory_peak(self) -> float:
        """取得記憶體峰值 (MB)"""
        try:
            process = psutil.Process()
            return process.memory_info().vms / 1024 / 1024
        except Exception:
            return 0.0
    
    def _get_gc_stats(self) -> Dict[int, int]:
        """取得垃圾收集統計"""
        try:
            stats = gc.get_stats()
            return {i: stat['collections'] for i, stat in enumerate(stats)}
        except Exception:
            return {}
    
    def _count_dataframes(self) -> int:
        """計算當前記憶體中的 DataFrame 數量"""
        try:
            objects = gc.get_objects()
            return sum(1 for obj in objects if isinstance(obj, pd.DataFrame))
        except Exception:
            return 0
    
    def _get_temp_files_info(self) -> Tuple[int, float]:
        """取得臨時檔案資訊 (數量, 大小MB)"""
        try:
            logs_dir = Path("logs")
            if not logs_dir.exists():
                return 0, 0.0
            
            temp_files = list(logs_dir.glob("*.xlsx"))
            total_size = sum(f.stat().st_size for f in temp_files)
            return len(temp_files), total_size / 1024 / 1024
        except Exception:
            return 0, 0.0
    
    @contextmanager
    def performance_context(self, operation_name: str = "process"):
        """性能監控上下文管理器"""
        start_time = time.perf_counter()
        start_memory = self._get_current_memory()
        
        # 前置清理
        self._pre_operation_cleanup()
        
        try:
            print(f"🚀 開始 {operation_name} (記憶體: {start_memory:.1f} MB)")
            yield
        finally:
            # 記錄性能指標
            end_time = time.perf_counter()
            process_time = end_time - start_time
            
            metrics = PerformanceMetrics(
                timestamp=time.time(),
                process_time=process_time,
                memory_usage_mb=self._get_current_memory(),
                memory_peak_mb=self._get_memory_peak(),
                gc_collections=self._get_gc_stats(),
                dataframe_count=self._count_dataframes(),
                temp_files_count=self._get_temp_files_info()[0],
                temp_files_size_mb=self._get_temp_files_info()[1]
            )
            
            self.metrics_history.append(metrics)
            self.process_count += 1
            
            # 後置分析和清理
            self._post_operation_analysis(metrics, operation_name)
    
    def _pre_operation_cleanup(self):
        """操作前清理"""
        # 1. 定期強制垃圾收集 (基於 pandas 建議)
        if self.process_count % self.config.auto_gc_interval == 0:
            print("🗑️ 執行定期垃圾收集...")
            collected = gc.collect()
            if collected > 0:
                print(f"   清理 {collected} 個對象")
        
        # 2. 定期檔案清理
        if self.process_count % self.config.auto_cleanup_interval == 0:
            self._cleanup_temp_files()
        
        # 3. DataFrame 記憶體優化檢查
        self._optimize_dataframe_memory()
    
    def _optimize_dataframe_memory(self):
        """優化 DataFrame 記憶體使用 (基於 pandas 最佳實踐)"""
        df_count = self._count_dataframes()
        if df_count > 5:  # 如果 DataFrame 過多
            print(f"⚠️ 檢測到 {df_count} 個 DataFrame，建議檢查記憶體使用")
    
    def _cleanup_temp_files(self):
        """清理臨時檔案 (基於配置)"""
        try:
            logs_dir = Path("logs")
            if not logs_dir.exists():
                return
            
            temp_files = list(logs_dir.glob("*.xlsx"))
            if len(temp_files) <= self.config.max_temp_files:
                return
            
            # 按時間排序，保留最新的檔案
            temp_files.sort(key=lambda x: x.stat().st_mtime, reverse=True)
            files_to_delete = temp_files[self.config.max_temp_files:]
            
            deleted_count = 0
            deleted_size = 0
            
            for file_path in files_to_delete:
                try:
                    size = file_path.stat().st_size
                    file_path.unlink()
                    deleted_count += 1
                    deleted_size += size
                except Exception as e:
                    self.logger.warning(f"刪除檔案失敗: {file_path.name} - {e}")
            
            if deleted_count > 0:
                print(f"🗑️ 清理臨時檔案: {deleted_count} 個, {deleted_size/1024/1024:.1f} MB")
        
        except Exception as e:
            self.logger.error(f"檔案清理失敗: {e}")
    
    def _post_operation_analysis(self, metrics: PerformanceMetrics, operation_name: str):
        """操作後分析"""
        print(f"✅ {operation_name} 完成 - 耗時: {metrics.process_time:.3f}秒, 記憶體: {metrics.memory_usage_mb:.1f}MB")
        
        # 記憶體警告檢查
        self._check_memory_warnings(metrics)
        
        # 性能趨勢分析
        self._analyze_performance_trend()
        
        # 建議檢查
        self._generate_recommendations(metrics)
    
    def _check_memory_warnings(self, metrics: PerformanceMetrics):
        """記憶體警告檢查"""
        if metrics.memory_usage_mb > self.config.memory_critical_threshold_mb:
            print(f"🚨 記憶體使用過高: {metrics.memory_usage_mb:.1f} MB (臨界值: {self.config.memory_critical_threshold_mb} MB)")
            print("   建議立即重啟 Python 進程")
        elif metrics.memory_usage_mb > self.config.memory_warning_threshold_mb:
            print(f"⚠️ 記憶體使用警告: {metrics.memory_usage_mb:.1f} MB (警告值: {self.config.memory_warning_threshold_mb} MB)")
    
    def _analyze_performance_trend(self):
        """分析性能趨勢"""
        if len(self.metrics_history) < self.config.min_samples_for_trend:
            return
        
        recent_times = [m.process_time for m in self.metrics_history[-self.config.min_samples_for_trend:]]
        baseline_time = self.metrics_history[0].process_time
        current_avg = sum(recent_times) / len(recent_times)
        
        if baseline_time > 0:
            degradation = (current_avg - baseline_time) / baseline_time
            if degradation > self.config.performance_degradation_threshold:
                print(f"📉 性能衰減警告: {degradation*100:.1f}% (閾值: {self.config.performance_degradation_threshold*100:.1f}%)")
                print("   建議檢查記憶體使用或重啟進程")
    
    def _generate_recommendations(self, metrics: PerformanceMetrics):
        """生成優化建議"""
        recommendations = []
        
        # DataFrame 數量建議
        if metrics.dataframe_count > 10:
            recommendations.append(f"考慮減少 DataFrame 數量 (當前: {metrics.dataframe_count})")
        
        # 臨時檔案建議  
        if metrics.temp_files_size_mb > self.config.max_temp_size_mb:
            recommendations.append(f"清理臨時檔案 (當前: {metrics.temp_files_size_mb:.1f} MB)")
        
        # 記憶體增長建議
        memory_growth = metrics.memory_usage_mb - self.baseline_memory
        if memory_growth > 200:  # 增長超過 200MB
            recommendations.append(f"記憶體增長過多 (+{memory_growth:.1f} MB)")
        
        if recommendations:
            print("💡 優化建議:")
            for rec in recommendations:
                print(f"   • {rec}")
    
    def force_cleanup(self):
        """強制清理 (手動調用)"""
        print("🧹 執行強制清理...")
        
        # 1. 強制垃圾收集
        collected = gc.collect()
        print(f"   垃圾收集: 清理 {collected} 個對象")
        
        # 2. 清理臨時檔案
        self._cleanup_temp_files()
        
        # 3. 記憶體報告
        current_memory = self._get_current_memory()
        memory_saved = max(0, self.baseline_memory - current_memory)
        print(f"   記憶體: {current_memory:.1f} MB (節省: {memory_saved:.1f} MB)")
    
    def get_performance_report(self) -> str:
        """取得性能報告"""
        if not self.metrics_history:
            return "無性能資料"
        
        latest = self.metrics_history[-1]
        
        report = [
            "📊 性能報告",
            "=" * 30,
            f"處理次數: {self.process_count}",
            f"總運行時間: {(time.time() - self.start_time)/60:.1f} 分鐘",
            "",
            "最新指標:",
            f"  處理時間: {latest.process_time:.3f} 秒",
            f"  記憶體使用: {latest.memory_usage_mb:.1f} MB",
            f"  DataFrame 數量: {latest.dataframe_count}",
            f"  臨時檔案: {latest.temp_files_count} 個 ({latest.temp_files_size_mb:.1f} MB)",
        ]
        
        # 性能趨勢
        if len(self.metrics_history) >= 2:
            first_time = self.metrics_history[0].process_time
            last_time = latest.process_time
            trend = ((last_time - first_time) / first_time) * 100
            report.append(f"  性能趨勢: {trend:+.1f}%")
        
        return "\n".join(report)
    
    def should_restart(self) -> bool:
        """判斷是否應該重啟"""
        if not self.metrics_history:
            return False
        
        latest = self.metrics_history[-1]
        
        # 記憶體超出臨界值
        if latest.memory_usage_mb > self.config.memory_critical_threshold_mb:
            return True
        
        # 性能嚴重衰減
        if len(self.metrics_history) >= self.config.min_samples_for_trend:
            recent_avg = sum(m.process_time for m in self.metrics_history[-3:]) / 3
            baseline = self.metrics_history[0].process_time
            if baseline > 0 and (recent_avg / baseline) > 2.0:  # 性能衰減超過100%
                return True
        
        return False

# 全域性能管理器實例 (功能替換原則)
performance_manager = AdvancedPerformanceManager()