/mnt/d/project/python/outlook_summary/venv/lib/python3.11/site-packages/numpy/_core/getlimits.py:552: UserWarning: Signature b'\x00\xd0\xcc\xcc\xcc\xcc\xcc\xcc\xfb\xbf\x00\x00\x00\x00\x00\x00' for <class 'numpy.longdouble'> does not match any known type: falling back to type probe function.
This warnings indicates broken support for the dtype!
  machar = _get_machar(dtype)
2025-07-09 09:04:18.542 | INFO     | src.presentation.api.ft_eqc_api:<module>:59 - ✅ FT Summary 處理器載入成功
2025-07-09 09:04:18.543 | INFO     | src.presentation.api.ft_eqc_api:<module>:90 - ✅ 靜態檔案服務已啟用: /mnt/d/project/python/outlook_summary/src/presentation/web/static
INFO:     Started server process [11171]
INFO:     Waiting for application startup.
2025-07-09 09:04:18.556 | INFO     | src.presentation.api.ft_eqc_api:startup_event:920 - 🚀 FT-EQC API 服務啟動中...
2025-07-09 09:04:18.556 | INFO     | src.presentation.api.ft_eqc_api:startup_event:921 - ✅ 模組化架構已載入
2025-07-09 09:04:18.556 | INFO     | src.presentation.api.ft_eqc_api:startup_event:922 - ✅ FastAPI 依賴注入機制已啟用
2025-07-09 09:04:18.556 | INFO     | src.presentation.api.ft_eqc_api:startup_event:928 - ✅ 檔案清理服務初始化已啟動
2025-07-09 09:04:18.556 | INFO     | src.presentation.api.ft_eqc_api:startup_event:932 - ✅ 所有服務模組已初始化
INFO:     Application startup complete.
INFO:     Uvicorn running on http://0.0.0.0:8010 (Press CTRL+C to quit)
2025-07-09 09:04:18.660 | INFO     | src.presentation.api.services.cleanup_service:set_cleanup_scheduler:25 - 🔧 清理調度器已設定
2025-07-09 09:04:18.660 | INFO     | src.presentation.api.ft_eqc_api:initialize_cleanup_service_async:942 - ✅ 清理服務非同步初始化完成
2025-07-09 09:07:05.993 | INFO     | src.presentation.api.ft_eqc_api:process_ft_summary:747 - 開始 FT Summary 批量處理：D:\project\python\outlook_summary\doc\YNT8726510993
2025-07-09 09:07:05.993 | INFO     | src.presentation.api.services.api_utils:process_folder_path:65 - 🔄 路徑轉換: D:\project\python\outlook_summary\doc\YNT8726510993 -> /mnt/d/project/python/outlook_summary/doc/YNT8726510993
初始化 Summary 工作表生成器
初始化 FT_Summary 專用轉換器
初始化核心 4 步驟轉換器 (BIN1 保護: 開啟)
初始化 Summary 工作表生成器
初始化 FT_Summary 專用轉換器
初始化核心 4 步驟轉換器 (BIN1 保護: 開啟)
🚀 初始化 FT_Summary_Generator (動態欄位版)
🚀 初始化批量CSV to Excel處理器
📋 排除關鍵字：['eqctotaldata', 'summary', 'correlation', 'eqcfaildata']
🎯 FT處理關鍵字：['auto_qc', 'ft', 'final_test']
🔍 QC處理關鍵字：['qc', 'quality_control', 'eqc']
=== 反假測試檢查：記錄執行前狀態 ===
📁 輸入資料夾：/mnt/d/project/python/outlook_summary/doc/YNT8726510993
🎯 處理模式：快速模式(僅Summary)
⏰ 開始時間：2025-07-09 09:07:05
🔧 執行 CTA All-in-One 前置處理...
🚀 開始處理目錄: /mnt/d/project/python/outlook_summary/doc/YNT8726510993

📦 步驟 1: 解壓縮檔案
🔍 掃描壓縮檔案: /mnt/d/project/python/outlook_summary/doc/YNT8726510993
📋 未發現需要解壓縮的檔案

🗑️ 步驟 1.5: 清理指定副檔名檔案
🗑️ 自動刪除副檔名: dlx, mdb
✅ 成功刪除 0 個指定副檔名的檔案

🔍 步驟 2: 掃描 CTA CSV 檔案
🔍 CTA 檢測結果: {'is_cta': False, 'sections': {}, 'has_data': False, 'has_qadata': False}
🔍 CTA 檢測結果: {'is_cta': False, 'sections': {}, 'has_data': False, 'has_qadata': False}
🔍 CTA 檢測結果: {'is_cta': False, 'sections': {}, 'has_data': False, 'has_qadata': False}
🔍 CTA 檢測結果: {'is_cta': False, 'sections': {}, 'has_data': False, 'has_qadata': False}
🔍 CTA 檢測結果: {'is_cta': False, 'sections': {}, 'has_data': False, 'has_qadata': False}
🔍 CTA 檢測結果: {'is_cta': False, 'sections': {}, 'has_data': False, 'has_qadata': False}
🔍 CTA 檢測結果: {'is_cta': False, 'sections': {}, 'has_data': False, 'has_qadata': False}
🔍 CTA 檢測結果: {'is_cta': False, 'sections': {}, 'has_data': False, 'has_qadata': False}
🔍 CTA 檢測結果: {'is_cta': False, 'sections': {}, 'has_data': False, 'has_qadata': False}
🔍 CTA 檢測結果: {'is_cta': False, 'sections': {}, 'has_data': False, 'has_qadata': False}
🔍 CTA 檢測結果: {'is_cta': False, 'sections': {}, 'has_data': False, 'has_qadata': False}
🔍 CTA 檢測結果: {'is_cta': False, 'sections': {}, 'has_data': False, 'has_qadata': False}
🔍 CTA 檢測結果: {'is_cta': False, 'sections': {}, 'has_data': False, 'has_qadata': False}
🔍 CTA 檢測結果: {'is_cta': False, 'sections': {}, 'has_data': False, 'has_qadata': False}
🔍 CTA 檢測結果: {'is_cta': False, 'sections': {}, 'has_data': False, 'has_qadata': False}
🔍 CTA 檢測結果: {'is_cta': False, 'sections': {}, 'has_data': False, 'has_qadata': False}
🔍 CTA 檢測結果: {'is_cta': False, 'sections': {}, 'has_data': False, 'has_qadata': False}
🔍 CTA 檢測結果: {'is_cta': False, 'sections': {}, 'has_data': False, 'has_qadata': False}
🔍 CTA 檢測結果: {'is_cta': False, 'sections': {}, 'has_data': False, 'has_qadata': False}
🔍 CTA 檢測結果: {'is_cta': False, 'sections': {}, 'has_data': False, 'has_qadata': False}
🔍 CTA 檢測結果: {'is_cta': False, 'sections': {}, 'has_data': False, 'has_qadata': False}
🔍 CTA 檢測結果: {'is_cta': False, 'sections': {}, 'has_data': False, 'has_qadata': False}
🔍 CTA 檢測結果: {'is_cta': False, 'sections': {}, 'has_data': False, 'has_qadata': False}
🔍 CTA 檢測結果: {'is_cta': False, 'sections': {}, 'has_data': False, 'has_qadata': False}
🔍 CTA 檢測結果: {'is_cta': False, 'sections': {}, 'has_data': False, 'has_qadata': False}
🔍 CTA 檢測結果: {'is_cta': False, 'sections': {}, 'has_data': False, 'has_qadata': False}
  📋 未發現 CTA CSV 檔案

🔧 步驟 3: 處理 0 個 CTA 檔案

📋 處理摘要:
  🗂️ 目錄: /mnt/d/project/python/outlook_summary/doc/YNT8726510993
  📦 解壓縮檔案: 0
  🗑️ 删除檔案: 0
  🔧 處理 CTA 檔案: 0
  📄 輸出 CSV 檔案: 0
  📁 歸檔檔案: 0
  ⏱️ 總處理時間: 1.11 秒
   ✅ CTA處理完成: 0 個Data11檔案
🔍 掃描資料夾：/mnt/d/project/python/outlook_summary/doc/YNT8726510993
✅ 找到 26 個CSV檔案，去重後 26 個
  1. YNT8726510993_F1FC_1_07.csv (2025-06-29 09:03:18)
  2. YNT8726510993_F1FC_1_07_r1.csv (2025-06-29 09:03:40)
  3. YNT8726510993_F1FT_1_07.csv (2025-06-29 10:39:01)
  4. YNT8726510993_F1FT_1_07_r1.csv (2025-06-29 10:39:22)
  5. YNT8726510993_F1FT_1_072025-06-30 14.22.59.csv (2025-06-30 00:34:07)
  6. YNT8726510993_F1FT_1_07_r12025-06-30 14.22.59.csv (2025-06-30 00:34:28)
  7. YNT8726510993_F1FT_1_072025-07-01 04.27.21.csv (2025-06-30 14:33:06)
  8. YNT8726510993_F1FT_1_07_r12025-07-01 04.27.21.csv (2025-06-30 14:33:28)
  9. YNT8726510993_F1FT_1_072025-07-01 18.30.33.csv (2025-07-01 04:32:58)
  10. YNT8726510993_F1FT_1_07_r12025-07-01 18.30.33.csv (2025-07-01 04:33:19)
  ... 另有 16 個檔案

📊 進度：1/26
⚡ 快速FT處理：YNT8726510993_F1FC_1_07.csv
============================================================
🎯 開始FT Summary生成（簡化版）
📁 處理檔案：YNT8726510993_F1FC_1_07.csv
============================================================
🔄 執行前7步驟：YNT8726510993_F1FC_1_07.csv
CSV 讀取成功: 360 行 x 1666 欄
執行步驟 1: CSV 結構補全
結構補全: 行7 C欄 空白 → '0.00.01'
結構補全: 行8 C欄 空白 → 'Test_Time'
結構補全: 行10 C欄 空白 → 'none'
結構補全: 行11 C欄 空白 → 'none'
✅ CSV 結構補全完成
執行步驟 2: BIN 號碼分配
BIN 分配: C6 → BIN 5
BIN 分配: D6 → BIN 6
BIN 分配: E6 → BIN 7
BIN 分配: F6 → BIN 8
BIN 分配: G6 → BIN 9
BIN 分配: H6 → BIN 10
BIN 分配: I6 → BIN 11
BIN 分配: J6 → BIN 12
BIN 分配: K6 → BIN 13
BIN 分配: L6 → BIN 14
  ... (省略中間 BIN 分配) ...
BIN 分配: Col16616 → BIN 1663
BIN 分配: Col16626 → BIN 1664
BIN 分配: Col16636 → BIN 1665
BIN 分配: Col16646 → BIN 1666
BIN 分配: Col16656 → BIN 1667
BIN 分配: Col16666 → BIN 1668
✅ BIN 號碼分配完成
執行步驟 3: BIN1 保護機制 (向量化優化版本)
🔍 發現 328 個 BIN1 設備，識別時間: 0.007 秒
📊 批量資料提取完成: 0.014 秒，328 設備 × 1664 測試項目
✅ 限值有效性檢查完成: 0.003 秒，1397 個有效測試項目
🛡️ 保護邏輯檢查完成: 0.205 秒，保護 49 個測試項目

🎉 向量化 BIN1 保護完成統計:
  ✅ 檢查 328 個 BIN1 設備
  ⚡ 總處理時間: 0.230 秒
  📊 檢查次數: 458,216 次
  🚀 處理速度: 1,995,194 次檢查/秒
  🛡️ 保護項目: 49 個
  📈 性能提升: 向量化優化相比逐一檢查

📝 BIN1 保護記錄已附加到: datalog.txt
✅ BIN1 保護機制完成，共 98 個位置需要紅色標記
執行步驟 4: 全設備上下限檢查與 BIN 分類 (向量化優化版本)
📊 向量化預處理階段...
  資料規模: 348 設備 × 1664 測試項目 = 579,072 次比較
  預處理完成: 0.277 秒
⚡ 向量化測試比較階段...
  向量化比較完成: 0.007 秒
🎯 向量化 BIN 分配階段...
  BIN 分配完成: 0.001 秒
📝 失敗位置記錄階段...
  位置記錄完成: 0.004 秒

🎉 向量化 BIN 分配完成統計:
  ✅ 完成 348 個設備的 BIN 分配
  ⚡ 總處理時間: 0.289 秒
  📊 比較次數: 579,072 次
  🚀 處理速度: 2,003,689 次比較/秒
  🔴 需要標紅色的位置: 7839 個
  📈 性能提升: 向量化優化相比逐一處理
🔍 執行 Site 欄位動態查找
📝 Site 查找記錄已附加到: datalog.txt
✅ 找到 Site 欄位: E欄 (index 4) = 'Site_No'
🎯 生成 Summary 工作表: YNT8726510993_F1FC_1_07.csv
📊 BIN 統計計算: 設備資料從第13行開始，BIN 欄位在第2欄
📊 BIN 統計: 總設備 348 個，BIN 分佈 {1: 328, 1312: 1, 718: 1, 1258: 1, 690: 1, 1257: 3, 147: 1, 654: 3, 337: 1, 379: 1, 1039: 1, 511: 1, 954: 1, 422: 1, 1400: 1, 956: 1, 600: 1}
📊 Site 統計計算: 設備資料從第13行開始，Site 欄位在第5欄，BIN 欄位在第2欄
📊 Site 統計: 2 個 Site，詳細資料 {1: {'total': 178, 'pass': 166, 'fail': 12, 'pass_rate': 93.25842696629213, 'bins': {1: 166, 1312: 1, 718: 1, 1258: 1, 1257: 2, 147: 1, 337: 1, 654: 1, 1039: 1, 954: 1, 422: 1, 600: 1}}, 2: {'total': 170, 'pass': 162, 'fail': 8, 'pass_rate': 95.29411764705881, 'bins': {1: 162, 690: 1, 1257: 1, 654: 2, 379: 1, 511: 1, 1400: 1, 956: 1}}}
📋 BIN Definition 對應: 1662 個 BIN 定義
✅ Summary 生成完成: 17 個 BIN, 2 個 Site
✅ 前7步驟完成
CSV 讀取成功: 360 行 x 1666 欄
📁 生成標準Summary：YNT8726510993_F1FC_1_07_summary.csv
📊 統計: Total=348, Pass=328, Fail=20, Yield=94.253%
📊 Metadata: Computer=GAX07, Date=06/29/25 09:03:18
📄 結構: 23行 CSV格式
============================================================
🎉 FT Summary生成完成
📊 處理時間：0.92秒
📁 Summary檔案：/mnt/d/project/python/outlook_summary/doc/YNT8726510993/YNT8726510993_F1FC_1_07_summary.csv
============================================================

📊 進度：2/26
🔍 EQC檔案：YNT8726510993_F1FC_1_07_r1.csv
✅ EQC檔案全通過：YNT8726510993_F1FC_1_07_r1.csv
✅ EQC全通過記錄：YNT8726510993_F1FC_1_07_r1.csv

📊 進度：3/26
⚡ 快速FT處理：YNT8726510993_F1FT_1_07.csv
============================================================
🎯 開始FT Summary生成（簡化版）
📁 處理檔案：YNT8726510993_F1FT_1_07.csv
============================================================
🔄 執行前7步驟：YNT8726510993_F1FT_1_07.csv
CSV 讀取成功: 3492 行 x 1666 欄
執行步驟 1: CSV 結構補全
結構補全: 行7 C欄 空白 → '0.00.01'
結構補全: 行8 C欄 空白 → 'Test_Time'
結構補全: 行10 C欄 空白 → 'none'
結構補全: 行11 C欄 空白 → 'none'
✅ CSV 結構補全完成
執行步驟 2: BIN 號碼分配
BIN 分配: C6 → BIN 5
BIN 分配: D6 → BIN 6
BIN 分配: E6 → BIN 7
BIN 分配: F6 → BIN 8
BIN 分配: G6 → BIN 9
BIN 分配: H6 → BIN 10
BIN 分配: I6 → BIN 11
BIN 分配: J6 → BIN 12
BIN 分配: K6 → BIN 13
BIN 分配: L6 → BIN 14
  ... (省略中間 BIN 分配) ...
BIN 分配: Col16616 → BIN 1663
BIN 分配: Col16626 → BIN 1664
BIN 分配: Col16636 → BIN 1665
BIN 分配: Col16646 → BIN 1666
BIN 分配: Col16656 → BIN 1667
BIN 分配: Col16666 → BIN 1668
✅ BIN 號碼分配完成
執行步驟 3: BIN1 保護機制 (向量化優化版本)
🔍 發現 3197 個 BIN1 設備，識別時間: 0.002 秒
📊 批量資料提取完成: 0.146 秒，3197 設備 × 1664 測試項目
✅ 限值有效性檢查完成: 0.002 秒，1397 個有效測試項目
🛡️ 保護邏輯檢查完成: 1.501 秒，保護 49 個測試項目

🎉 向量化 BIN1 保護完成統計:
  ✅ 檢查 3197 個 BIN1 設備
  ⚡ 總處理時間: 1.651 秒
  📊 檢查次數: 4,466,209 次
  🚀 處理速度: 2,704,619 次檢查/秒
  🛡️ 保護項目: 49 個
  📈 性能提升: 向量化優化相比逐一檢查

📝 BIN1 保護記錄已附加到: datalog.txt
✅ BIN1 保護機制完成，共 98 個位置需要紅色標記
執行步驟 4: 全設備上下限檢查與 BIN 分類 (向量化優化版本)
📊 向量化預處理階段...
  資料規模: 3480 設備 × 1664 測試項目 = 5,790,720 次比較
  預處理完成: 2.729 秒
⚡ 向量化測試比較階段...
  向量化比較完成: 0.211 秒
🎯 向量化 BIN 分配階段...
  BIN 分配完成: 0.001 秒
📝 失敗位置記錄階段...
  位置記錄完成: 0.042 秒

🎉 向量化 BIN 分配完成統計:
  ✅ 完成 3480 個設備的 BIN 分配
  ⚡ 總處理時間: 2.983 秒
  📊 比較次數: 5,790,720 次
  🚀 處理速度: 1,941,272 次比較/秒
  🔴 需要標紅色的位置: 104772 個
  📈 性能提升: 向量化優化相比逐一處理
🔍 執行 Site 欄位動態查找
📝 Site 查找記錄已附加到: datalog.txt
✅ 找到 Site 欄位: E欄 (index 4) = 'Site_No'
🎯 生成 Summary 工作表: YNT8726510993_F1FT_1_07.csv
📊 BIN 統計計算: 設備資料從第13行開始，BIN 欄位在第2欄
📊 BIN 統計: 總設備 3480 個，BIN 分佈 {1: 3197, 1312: 36, 654: 22, 507: 1, 956: 57, 970: 33, 195: 1, 622: 2, 1038: 2, 38: 1, 1314: 6, 1301: 1, 1361: 2, 683: 5, 453: 2, 1257: 3, 1039: 12, 465: 1, 177: 1, 491: 2, 487: 1, 660: 10, 579: 7, 964: 12, 718: 2, 724: 2, 379: 6, 292: 1, 421: 1, 1078: 1, 511: 1, 337: 4, 1412: 1, 1417: 1, 1274: 1, 745: 1, 951: 3, 26: 1, 549: 1, 963: 1, 1053: 2, 1258: 1, 686: 1, 363: 1, 51: 1, 397: 1, 445: 1, 1303: 1, 1054: 1, 247: 1, 746: 3, 1115: 1, 143: 2, 550: 1, 954: 1, 972: 2, 15: 5, 23: 3, 14: 1, 17: 2, 28: 2, 124: 1}
📊 Site 統計計算: 設備資料從第13行開始，Site 欄位在第5欄，BIN 欄位在第2欄
📊 Site 統計: 2 個 Site，詳細資料 {1: {'total': 1744, 'pass': 1588, 'fail': 156, 'pass_rate': 91.05504587155964, 'bins': {1: 1588, 1312: 36, 654: 10, 970: 16, 1038: 2, 1314: 3, 1361: 1, 956: 30, 683: 1, 1257: 1, 465: 1, 491: 2, 660: 5, 1039: 6, 964: 5, 718: 2, 724: 1, 292: 1, 421: 1, 579: 3, 1412: 1, 379: 2, 453: 1, 951: 3, 337: 3, 397: 1, 1053: 1, 445: 1, 622: 1, 1303: 1, 746: 1, 1115: 1, 143: 1, 972: 2, 15: 3, 17: 2, 28: 2, 124: 1, 23: 1}}, 2: {'total': 1736, 'pass': 1609, 'fail': 127, 'pass_rate': 92.68433179723502, 'bins': {1: 1609, 507: 1, 956: 27, 970: 17, 195: 1, 622: 1, 38: 1, 1301: 1, 1361: 1, 453: 1, 1039: 6, 177: 1, 487: 1, 579: 4, 1314: 3, 964: 7, 379: 4, 1078: 1, 683: 4, 511: 1, 337: 1, 1417: 1, 654: 12, 1257: 2, 1274: 1, 745: 1, 26: 1, 549: 1, 963: 1, 1053: 1, 1258: 1, 686: 1, 363: 1, 51: 1, 660: 5, 1054: 1, 247: 1, 746: 2, 724: 1, 550: 1, 143: 1, 954: 1, 15: 2, 23: 2, 14: 1}}}
📋 BIN Definition 對應: 1662 個 BIN 定義
✅ Summary 生成完成: 62 個 BIN, 2 個 Site
✅ 前7步驟完成
CSV 讀取成功: 3492 行 x 1666 欄
📁 生成標準Summary：YNT8726510993_F1FT_1_07_summary.csv
📊 統計: Total=3480, Pass=3197, Fail=283, Yield=91.868%
📊 Metadata: Computer=GAX07, Date=06/29/25 10:39:01
📄 結構: 68行 CSV格式
============================================================
🎉 FT Summary生成完成
📊 處理時間：7.37秒
📁 Summary檔案：/mnt/d/project/python/outlook_summary/doc/YNT8726510993/YNT8726510993_F1FT_1_07_summary.csv
============================================================

📊 進度：4/26
🔍 EQC檔案：YNT8726510993_F1FT_1_07_r1.csv
✅ EQC檔案全通過：YNT8726510993_F1FT_1_07_r1.csv
✅ EQC全通過記錄：YNT8726510993_F1FT_1_07_r1.csv

📊 進度：5/26
⚡ 快速FT處理：YNT8726510993_F1FT_1_072025-06-30 14.22.59.csv
============================================================
🎯 開始FT Summary生成（簡化版）
📁 處理檔案：YNT8726510993_F1FT_1_072025-06-30 14.22.59.csv
============================================================
🔄 執行前7步驟：YNT8726510993_F1FT_1_072025-06-30 14.22.59.csv
CSV 讀取成功: 3492 行 x 1666 欄
執行步驟 1: CSV 結構補全
結構補全: 行7 C欄 空白 → '0.00.01'
結構補全: 行8 C欄 空白 → 'Test_Time'
結構補全: 行10 C欄 空白 → 'none'
結構補全: 行11 C欄 空白 → 'none'
✅ CSV 結構補全完成
執行步驟 2: BIN 號碼分配
BIN 分配: C6 → BIN 5
BIN 分配: D6 → BIN 6
BIN 分配: E6 → BIN 7
BIN 分配: F6 → BIN 8
BIN 分配: G6 → BIN 9
BIN 分配: H6 → BIN 10
BIN 分配: I6 → BIN 11
BIN 分配: J6 → BIN 12
BIN 分配: K6 → BIN 13
BIN 分配: L6 → BIN 14
  ... (省略中間 BIN 分配) ...
BIN 分配: Col16616 → BIN 1663
BIN 分配: Col16626 → BIN 1664
BIN 分配: Col16636 → BIN 1665
BIN 分配: Col16646 → BIN 1666
BIN 分配: Col16656 → BIN 1667
BIN 分配: Col16666 → BIN 1668
✅ BIN 號碼分配完成
執行步驟 3: BIN1 保護機制 (向量化優化版本)
🔍 發現 3157 個 BIN1 設備，識別時間: 0.001 秒
📊 批量資料提取完成: 0.116 秒，3157 設備 × 1664 測試項目
✅ 限值有效性檢查完成: 0.002 秒，1397 個有效測試項目
🛡️ 保護邏輯檢查完成: 1.624 秒，保護 49 個測試項目

🎉 向量化 BIN1 保護完成統計:
  ✅ 檢查 3157 個 BIN1 設備
  ⚡ 總處理時間: 1.743 秒
  📊 檢查次數: 4,410,329 次
  🚀 處理速度: 2,529,891 次檢查/秒
  🛡️ 保護項目: 49 個
  📈 性能提升: 向量化優化相比逐一檢查

📝 BIN1 保護記錄已附加到: datalog.txt
✅ BIN1 保護機制完成，共 98 個位置需要紅色標記
執行步驟 4: 全設備上下限檢查與 BIN 分類 (向量化優化版本)
📊 向量化預處理階段...
  資料規模: 3480 設備 × 1664 測試項目 = 5,790,720 次比較
  預處理完成: 2.996 秒
⚡ 向量化測試比較階段...
  向量化比較完成: 0.226 秒
🎯 向量化 BIN 分配階段...
  BIN 分配完成: 0.001 秒
📝 失敗位置記錄階段...
  位置記錄完成: 0.050 秒

🎉 向量化 BIN 分配完成統計:
  ✅ 完成 3480 個設備的 BIN 分配
  ⚡ 總處理時間: 3.274 秒
  📊 比較次數: 5,790,720 次
  🚀 處理速度: 1,768,738 次比較/秒
  🔴 需要標紅色的位置: 108785 個
  📈 性能提升: 向量化優化相比逐一處理
🔍 執行 Site 欄位動態查找
📝 Site 查找記錄已附加到: datalog.txt
✅ 找到 Site 欄位: E欄 (index 4) = 'Site_No'
🎯 生成 Summary 工作表: YNT8726510993_F1FT_1_072025-06-30 14.22.59.csv
📊 BIN 統計計算: 設備資料從第13行開始，BIN 欄位在第2欄
📊 BIN 統計: 總設備 3480 個，BIN 分佈 {1: 3157, 1312: 49, 1054: 1, 956: 65, 654: 24, 1314: 16, 171: 1, 1360: 1, 660: 8, 1298: 1, 970: 36, 1257: 4, 1039: 7, 385: 1, 964: 21, 361: 1, 579: 11, 951: 5, 1369: 1, 245: 1, 1038: 4, 457: 2, 1317: 1, 279: 1, 241: 2, 143: 2, 49: 1, 426: 2, 38: 4, 379: 4, 953: 1, 397: 2, 718: 4, 1035: 2, 690: 2, 552: 1, 59: 1, 325: 1, 622: 1, 1356: 1, 1348: 1, 746: 1, 1050: 1, 1053: 1, 972: 5, 57: 1, 548: 1, 1412: 1, 292: 1, 507: 1, 553: 1, 954: 3, 883: 1, 421: 1, 724: 3, 326: 1, 734: 1, 15: 1, 511: 1, 168: 1, 1396: 1, 683: 1}
📊 Site 統計計算: 設備資料從第13行開始，Site 欄位在第5欄，BIN 欄位在第2欄
📊 Site 統計: 2 個 Site，詳細資料 {1: {'total': 1744, 'pass': 1547, 'fail': 197, 'pass_rate': 88.70412844036697, 'bins': {1: 1547, 1312: 49, 956: 43, 654: 11, 1314: 7, 171: 1, 660: 4, 1298: 1, 970: 17, 1257: 1, 1039: 6, 964: 12, 457: 1, 1317: 1, 579: 5, 951: 3, 49: 1, 426: 1, 38: 4, 379: 3, 953: 1, 718: 2, 59: 1, 325: 1, 622: 1, 1356: 1, 1348: 1, 1053: 1, 397: 1, 57: 1, 507: 1, 972: 2, 553: 1, 954: 2, 883: 1, 421: 1, 143: 1, 15: 1, 511: 1, 724: 2, 1396: 1, 683: 1}}, 2: {'total': 1736, 'pass': 1610, 'fail': 126, 'pass_rate': 92.74193548387096, 'bins': {1: 1610, 1054: 1, 1314: 9, 1360: 1, 956: 22, 654: 13, 970: 19, 385: 1, 361: 1, 579: 6, 951: 2, 964: 9, 1369: 1, 660: 4, 245: 1, 1038: 4, 457: 1, 279: 1, 241: 2, 143: 1, 1039: 1, 1257: 3, 397: 1, 1035: 2, 690: 2, 552: 1, 746: 1, 1050: 1, 972: 3, 548: 1, 1412: 1, 292: 1, 718: 2, 426: 1, 724: 1, 326: 1, 954: 1, 734: 1, 168: 1, 379: 1}}}
📋 BIN Definition 對應: 1662 個 BIN 定義
✅ Summary 生成完成: 62 個 BIN, 2 個 Site
✅ 前7步驟完成
CSV 讀取成功: 3492 行 x 1666 欄
📁 生成標準Summary：YNT8726510993_F1FT_1_072025-06-30 14.22.59_summary.csv
📊 統計: Total=3480, Pass=3157, Fail=323, Yield=90.718%
📊 Metadata: Computer=GAX07, Date=06/30/25 00:34:07
📄 結構: 68行 CSV格式
============================================================
🎉 FT Summary生成完成
📊 處理時間：7.44秒
📁 Summary檔案：/mnt/d/project/python/outlook_summary/doc/YNT8726510993/YNT8726510993_F1FT_1_072025-06-30 14.22.59_summary.csv
============================================================

📊 進度：6/26
🔍 EQC檔案：YNT8726510993_F1FT_1_07_r12025-06-30 14.22.59.csv
✅ EQC檔案全通過：YNT8726510993_F1FT_1_07_r12025-06-30 14.22.59.csv
✅ EQC全通過記錄：YNT8726510993_F1FT_1_07_r12025-06-30 14.22.59.csv

📊 進度：7/26
⚡ 快速FT處理：YNT8726510993_F1FT_1_072025-07-01 04.27.21.csv
============================================================
🎯 開始FT Summary生成（簡化版）
📁 處理檔案：YNT8726510993_F1FT_1_072025-07-01 04.27.21.csv
============================================================
🔄 執行前7步驟：YNT8726510993_F1FT_1_072025-07-01 04.27.21.csv
CSV 讀取成功: 3492 行 x 1666 欄
執行步驟 1: CSV 結構補全
結構補全: 行7 C欄 空白 → '0.00.01'
結構補全: 行8 C欄 空白 → 'Test_Time'
結構補全: 行10 C欄 空白 → 'none'
結構補全: 行11 C欄 空白 → 'none'
✅ CSV 結構補全完成
執行步驟 2: BIN 號碼分配
BIN 分配: C6 → BIN 5
BIN 分配: D6 → BIN 6
BIN 分配: E6 → BIN 7
BIN 分配: F6 → BIN 8
BIN 分配: G6 → BIN 9
BIN 分配: H6 → BIN 10
BIN 分配: I6 → BIN 11
BIN 分配: J6 → BIN 12
BIN 分配: K6 → BIN 13
BIN 分配: L6 → BIN 14
  ... (省略中間 BIN 分配) ...
BIN 分配: Col16616 → BIN 1663
BIN 分配: Col16626 → BIN 1664
BIN 分配: Col16636 → BIN 1665
BIN 分配: Col16646 → BIN 1666
BIN 分配: Col16656 → BIN 1667
BIN 分配: Col16666 → BIN 1668
✅ BIN 號碼分配完成
執行步驟 3: BIN1 保護機制 (向量化優化版本)
🔍 發現 3228 個 BIN1 設備，識別時間: 0.002 秒
📊 批量資料提取完成: 0.165 秒，3228 設備 × 1664 測試項目
✅ 限值有效性檢查完成: 0.003 秒，1397 個有效測試項目
🛡️ 保護邏輯檢查完成: 2.718 秒，保護 49 個測試項目

🎉 向量化 BIN1 保護完成統計:
  ✅ 檢查 3228 個 BIN1 設備
  ⚡ 總處理時間: 2.888 秒
  📊 檢查次數: 4,509,516 次
  🚀 處理速度: 1,561,487 次檢查/秒
  🛡️ 保護項目: 49 個
  📈 性能提升: 向量化優化相比逐一檢查

📝 BIN1 保護記錄已附加到: datalog.txt
✅ BIN1 保護機制完成，共 98 個位置需要紅色標記
執行步驟 4: 全設備上下限檢查與 BIN 分類 (向量化優化版本)
📊 向量化預處理階段...
  資料規模: 3480 設備 × 1664 測試項目 = 5,790,720 次比較
  預處理完成: 2.758 秒
⚡ 向量化測試比較階段...
  向量化比較完成: 0.211 秒
🎯 向量化 BIN 分配階段...
  BIN 分配完成: 0.001 秒
📝 失敗位置記錄階段...
  位置記錄完成: 0.053 秒

🎉 向量化 BIN 分配完成統計:
  ✅ 完成 3480 個設備的 BIN 分配
  ⚡ 總處理時間: 3.024 秒
  📊 比較次數: 5,790,720 次
  🚀 處理速度: 1,915,170 次比較/秒
  🔴 需要標紅色的位置: 94898 個
  📈 性能提升: 向量化優化相比逐一處理
🔍 執行 Site 欄位動態查找
📝 Site 查找記錄已附加到: datalog.txt
✅ 找到 Site 欄位: E欄 (index 4) = 'Site_No'
🎯 生成 Summary 工作表: YNT8726510993_F1FT_1_072025-07-01 04.27.21.csv
📊 BIN 統計計算: 設備資料從第13行開始，BIN 欄位在第2欄
📊 BIN 統計: 總設備 3480 個，BIN 分佈 {1: 3228, 1312: 35, 379: 8, 654: 25, 579: 11, 1314: 8, 660: 10, 670: 1, 683: 8, 403: 1, 1039: 11, 563: 1, 956: 42, 549: 1, 38: 2, 475: 1, 1035: 1, 29: 1, 1412: 1, 1417: 1, 445: 2, 970: 18, 1054: 1, 507: 1, 143: 3, 746: 1, 724: 2, 326: 1, 241: 1, 1414: 1, 1038: 3, 84: 1, 270: 1, 527: 1, 263: 1, 682: 1, 964: 11, 1118: 1, 221: 1, 15: 3, 1474: 1, 397: 1, 963: 1, 753: 1, 1258: 1, 972: 1, 954: 1, 1257: 3, 718: 1, 353: 1, 422: 1, 1005: 1, 1303: 1, 745: 1, 550: 1, 688: 1, 112: 1, 433: 1, 69: 1, 449: 1, 409: 1, 380: 1, 951: 1, 421: 1, 119: 1, 595: 1}
📊 Site 統計計算: 設備資料從第13行開始，Site 欄位在第5欄，BIN 欄位在第2欄
📊 Site 統計: 2 個 Site，詳細資料 {1: {'total': 1744, 'pass': 1602, 'fail': 142, 'pass_rate': 91.85779816513761, 'bins': {1: 1602, 1312: 35, 654: 14, 1314: 3, 379: 3, 670: 1, 683: 4, 956: 17, 660: 4, 549: 1, 579: 5, 38: 1, 475: 1, 1035: 1, 29: 1, 1412: 1, 445: 2, 970: 8, 1039: 4, 507: 1, 724: 2, 241: 1, 1414: 1, 84: 1, 527: 1, 263: 1, 682: 1, 221: 1, 15: 2, 1474: 1, 963: 1, 753: 1, 1258: 1, 1257: 2, 964: 7, 422: 1, 1005: 1, 1303: 1, 745: 1, 550: 1, 688: 1, 433: 1, 143: 2, 421: 1, 119: 1}}, 2: {'total': 1736, 'pass': 1626, 'fail': 110, 'pass_rate': 93.66359447004609, 'bins': {1: 1626, 379: 5, 579: 6, 660: 6, 403: 1, 1039: 7, 654: 11, 563: 1, 956: 25, 1417: 1, 1054: 1, 970: 10, 143: 1, 746: 1, 326: 1, 683: 4, 1038: 3, 270: 1, 964: 4, 38: 1, 1118: 1, 1314: 5, 15: 1, 397: 1, 972: 1, 954: 1, 718: 1, 353: 1, 1257: 1, 112: 1, 69: 1, 449: 1, 409: 1, 380: 1, 951: 1, 595: 1}}}
📋 BIN Definition 對應: 1662 個 BIN 定義
✅ Summary 生成完成: 66 個 BIN, 2 個 Site
✅ 前7步驟完成
CSV 讀取成功: 3492 行 x 1666 欄
📁 生成標準Summary：YNT8726510993_F1FT_1_072025-07-01 04.27.21_summary.csv
📊 統計: Total=3480, Pass=3228, Fail=252, Yield=92.759%
📊 Metadata: Computer=GAX07, Date=06/30/25 14:33:06
📄 結構: 72行 CSV格式
============================================================
🎉 FT Summary生成完成
📊 處理時間：8.95秒
📁 Summary檔案：/mnt/d/project/python/outlook_summary/doc/YNT8726510993/YNT8726510993_F1FT_1_072025-07-01 04.27.21_summary.csv
============================================================

📊 進度：8/26
🔍 EQC檔案：YNT8726510993_F1FT_1_07_r12025-07-01 04.27.21.csv
✅ EQC檔案全通過：YNT8726510993_F1FT_1_07_r12025-07-01 04.27.21.csv
✅ EQC全通過記錄：YNT8726510993_F1FT_1_07_r12025-07-01 04.27.21.csv

📊 進度：9/26
⚡ 快速FT處理：YNT8726510993_F1FT_1_072025-07-01 18.30.33.csv
============================================================
🎯 開始FT Summary生成（簡化版）
📁 處理檔案：YNT8726510993_F1FT_1_072025-07-01 18.30.33.csv
============================================================
🔄 執行前7步驟：YNT8726510993_F1FT_1_072025-07-01 18.30.33.csv
CSV 讀取成功: 3492 行 x 1666 欄
執行步驟 1: CSV 結構補全
結構補全: 行7 C欄 空白 → '0.00.01'
結構補全: 行8 C欄 空白 → 'Test_Time'
結構補全: 行10 C欄 空白 → 'none'
結構補全: 行11 C欄 空白 → 'none'
✅ CSV 結構補全完成
執行步驟 2: BIN 號碼分配
BIN 分配: C6 → BIN 5
BIN 分配: D6 → BIN 6
BIN 分配: E6 → BIN 7
BIN 分配: F6 → BIN 8
BIN 分配: G6 → BIN 9
BIN 分配: H6 → BIN 10
BIN 分配: I6 → BIN 11
BIN 分配: J6 → BIN 12
BIN 分配: K6 → BIN 13
BIN 分配: L6 → BIN 14
  ... (省略中間 BIN 分配) ...
BIN 分配: Col16616 → BIN 1663
BIN 分配: Col16626 → BIN 1664
BIN 分配: Col16636 → BIN 1665
BIN 分配: Col16646 → BIN 1666
BIN 分配: Col16656 → BIN 1667
BIN 分配: Col16666 → BIN 1668
✅ BIN 號碼分配完成
執行步驟 3: BIN1 保護機制 (向量化優化版本)
🔍 發現 3205 個 BIN1 設備，識別時間: 0.001 秒
📊 批量資料提取完成: 0.117 秒，3205 設備 × 1664 測試項目
✅ 限值有效性檢查完成: 0.003 秒，1397 個有效測試項目
🛡️ 保護邏輯檢查完成: 2.084 秒，保護 49 個測試項目

🎉 向量化 BIN1 保護完成統計:
  ✅ 檢查 3205 個 BIN1 設備
  ⚡ 總處理時間: 2.205 秒
  📊 檢查次數: 4,477,385 次
  🚀 處理速度: 2,030,256 次檢查/秒
  🛡️ 保護項目: 49 個
  📈 性能提升: 向量化優化相比逐一檢查

📝 BIN1 保護記錄已附加到: datalog.txt
✅ BIN1 保護機制完成，共 98 個位置需要紅色標記
執行步驟 4: 全設備上下限檢查與 BIN 分類 (向量化優化版本)
📊 向量化預處理階段...
  資料規模: 3480 設備 × 1664 測試項目 = 5,790,720 次比較
  預處理完成: 3.130 秒
⚡ 向量化測試比較階段...
  向量化比較完成: 0.171 秒
🎯 向量化 BIN 分配階段...
  BIN 分配完成: 0.004 秒
📝 失敗位置記錄階段...
  位置記錄完成: 0.042 秒

🎉 向量化 BIN 分配完成統計:
  ✅ 完成 3480 個設備的 BIN 分配
  ⚡ 總處理時間: 3.348 秒
  📊 比較次數: 5,790,720 次
  🚀 處理速度: 1,729,796 次比較/秒
  🔴 需要標紅色的位置: 95815 個
  📈 性能提升: 向量化優化相比逐一處理
🔍 執行 Site 欄位動態查找
📝 Site 查找記錄已附加到: datalog.txt
✅ 找到 Site 欄位: E欄 (index 4) = 'Site_No'
🎯 生成 Summary 工作表: YNT8726510993_F1FT_1_072025-07-01 18.30.33.csv
📊 BIN 統計計算: 設備資料從第13行開始，BIN 欄位在第2欄
📊 BIN 統計: 總設備 3480 個，BIN 分佈 {1: 3205, 1312: 43, 221: 1, 654: 34, 361: 2, 956: 48, 1257: 1, 507: 1, 163: 1, 453: 2, 660: 13, 1038: 3, 1258: 1, 970: 40, 718: 3, 579: 4, 1358: 1, 48: 1, 527: 1, 751: 1, 746: 2, 46: 1, 1039: 6, 1314: 13, 734: 1, 964: 9, 426: 1, 73: 1, 683: 3, 1129: 1, 1205: 1, 951: 1, 552: 1, 293: 1, 550: 1, 954: 1, 511: 1, 616: 1, 681: 1, 622: 1, 472: 1, 61: 1, 241: 1, 610: 1, 379: 2, 118: 1, 690: 1, 1317: 1, 1307: 1, 60: 1, 972: 1, 524: 1, 124: 1, 478: 1, 367: 1, 688: 1, 433: 1, 345: 1, 217: 1, 30: 1, 1259: 1, 1131: 1, 682: 1, 745: 1, 143: 1}
📊 Site 統計計算: 設備資料從第13行開始，Site 欄位在第5欄，BIN 欄位在第2欄
📊 Site 統計: 2 個 Site，詳細資料 {1: {'total': 1744, 'pass': 1586, 'fail': 158, 'pass_rate': 90.94036697247707, 'bins': {1: 1586, 1312: 43, 221: 1, 654: 18, 361: 2, 956: 26, 507: 1, 163: 1, 453: 1, 660: 8, 1258: 1, 718: 2, 579: 4, 970: 13, 1358: 1, 48: 1, 527: 1, 46: 1, 1314: 5, 734: 1, 964: 2, 73: 1, 1039: 1, 1129: 1, 951: 1, 293: 1, 550: 1, 954: 1, 681: 1, 622: 1, 61: 1, 241: 1, 379: 2, 118: 1, 1307: 1, 60: 1, 972: 1, 124: 1, 478: 1, 688: 1, 433: 1, 345: 1, 30: 1, 746: 1, 745: 1}}, 2: {'total': 1736, 'pass': 1619, 'fail': 117, 'pass_rate': 93.26036866359448, 'bins': {1: 1619, 1257: 1, 654: 16, 956: 22, 1038: 3, 970: 27, 660: 5, 718: 1, 751: 1, 746: 1, 1039: 5, 964: 7, 426: 1, 1314: 8, 683: 3, 1205: 1, 552: 1, 511: 1, 616: 1, 472: 1, 610: 1, 690: 1, 1317: 1, 524: 1, 367: 1, 217: 1, 453: 1, 1259: 1, 1131: 1, 682: 1, 143: 1}}}
📋 BIN Definition 對應: 1662 個 BIN 定義
✅ Summary 生成完成: 65 個 BIN, 2 個 Site
✅ 前7步驟完成
CSV 讀取成功: 3492 行 x 1666 欄
📁 生成標準Summary：YNT8726510993_F1FT_1_072025-07-01 18.30.33_summary.csv
📊 統計: Total=3480, Pass=3205, Fail=275, Yield=92.098%
📊 Metadata: Computer=GAX07, Date=07/01/25 04:32:58
📄 結構: 71行 CSV格式
============================================================
🎉 FT Summary生成完成
📊 處理時間：10.07秒
📁 Summary檔案：/mnt/d/project/python/outlook_summary/doc/YNT8726510993/YNT8726510993_F1FT_1_072025-07-01 18.30.33_summary.csv
============================================================

📊 進度：10/26
🔍 EQC檔案：YNT8726510993_F1FT_1_07_r12025-07-01 18.30.33.csv
✅ EQC檔案全通過：YNT8726510993_F1FT_1_07_r12025-07-01 18.30.33.csv
✅ EQC全通過記錄：YNT8726510993_F1FT_1_07_r12025-07-01 18.30.33.csv

📊 進度：11/26
⚡ 快速FT處理：YNT8726510993_F1FT_1_072025-07-02 08.36.39.csv
============================================================
🎯 開始FT Summary生成（簡化版）
📁 處理檔案：YNT8726510993_F1FT_1_072025-07-02 08.36.39.csv
============================================================
🔄 執行前7步驟：YNT8726510993_F1FT_1_072025-07-02 08.36.39.csv
CSV 讀取成功: 3490 行 x 1666 欄
執行步驟 1: CSV 結構補全
結構補全: 行7 C欄 空白 → '0.00.01'
結構補全: 行8 C欄 空白 → 'Test_Time'
結構補全: 行10 C欄 空白 → 'none'
結構補全: 行11 C欄 空白 → 'none'
✅ CSV 結構補全完成
執行步驟 2: BIN 號碼分配
BIN 分配: C6 → BIN 5
BIN 分配: D6 → BIN 6
BIN 分配: E6 → BIN 7
BIN 分配: F6 → BIN 8
BIN 分配: G6 → BIN 9
BIN 分配: H6 → BIN 10
BIN 分配: I6 → BIN 11
BIN 分配: J6 → BIN 12
BIN 分配: K6 → BIN 13
BIN 分配: L6 → BIN 14
  ... (省略中間 BIN 分配) ...
BIN 分配: Col16616 → BIN 1663
BIN 分配: Col16626 → BIN 1664
BIN 分配: Col16636 → BIN 1665
BIN 分配: Col16646 → BIN 1666
BIN 分配: Col16656 → BIN 1667
BIN 分配: Col16666 → BIN 1668
✅ BIN 號碼分配完成
執行步驟 3: BIN1 保護機制 (向量化優化版本)
🔍 發現 3152 個 BIN1 設備，識別時間: 0.003 秒
📊 批量資料提取完成: 0.179 秒，3152 設備 × 1664 測試項目
✅ 限值有效性檢查完成: 0.003 秒，1397 個有效測試項目
🛡️ 保護邏輯檢查完成: 2.553 秒，保護 49 個測試項目

🎉 向量化 BIN1 保護完成統計:
  ✅ 檢查 3152 個 BIN1 設備
  ⚡ 總處理時間: 2.738 秒
  📊 檢查次數: 4,403,344 次
  🚀 處理速度: 1,608,346 次檢查/秒
  🛡️ 保護項目: 49 個
  📈 性能提升: 向量化優化相比逐一檢查

📝 BIN1 保護記錄已附加到: datalog.txt
✅ BIN1 保護機制完成，共 98 個位置需要紅色標記
執行步驟 4: 全設備上下限檢查與 BIN 分類 (向量化優化版本)
📊 向量化預處理階段...
  資料規模: 3478 設備 × 1664 測試項目 = 5,787,392 次比較
  預處理完成: 2.851 秒
⚡ 向量化測試比較階段...
  向量化比較完成: 0.191 秒
🎯 向量化 BIN 分配階段...
  BIN 分配完成: 0.002 秒
📝 失敗位置記錄階段...
  位置記錄完成: 0.052 秒

🎉 向量化 BIN 分配完成統計:
  ✅ 完成 3478 個設備的 BIN 分配
  ⚡ 總處理時間: 3.096 秒
  📊 比較次數: 5,787,392 次
  🚀 處理速度: 1,869,137 次比較/秒
  🔴 需要標紅色的位置: 113023 個
  📈 性能提升: 向量化優化相比逐一處理
🔍 執行 Site 欄位動態查找
📝 Site 查找記錄已附加到: datalog.txt
✅ 找到 Site 欄位: E欄 (index 4) = 'Site_No'
🎯 生成 Summary 工作表: YNT8726510993_F1FT_1_072025-07-02 08.36.39.csv
📊 BIN 統計計算: 設備資料從第13行開始，BIN 欄位在第2欄
📊 BIN 統計: 總設備 3478 個，BIN 分佈 {1: 3152, 1312: 37, 1038: 5, 579: 11, 15: 1, 682: 2, 654: 35, 951: 3, 216: 1, 956: 49, 964: 14, 1599: 1, 660: 5, 1393: 1, 724: 4, 745: 1, 970: 52, 972: 2, 379: 9, 1257: 7, 567: 1, 241: 2, 954: 3, 1314: 16, 688: 11, 1039: 8, 337: 3, 1303: 1, 746: 4, 449: 1, 550: 1, 683: 5, 373: 2, 1412: 1, 1417: 1, 572: 1, 559: 1, 433: 1, 457: 1, 245: 1, 438: 1, 734: 1, 469: 1, 499: 1, 273: 1, 950: 1, 152: 1, 300: 1, 1258: 2, 753: 1, 1385: 1, 1335: 1, 445: 1, 622: 1, 1005: 1, 21: 1, 143: 1, 1035: 1, 487: 1, 38: 1}
📊 Site 統計計算: 設備資料從第13行開始，Site 欄位在第5欄，BIN 欄位在第2欄
📊 Site 統計: 2 個 Site，詳細資料 {1: {'total': 1742, 'pass': 1573, 'fail': 169, 'pass_rate': 90.29850746268657, 'bins': {1: 1573, 1312: 37, 579: 3, 682: 2, 654: 19, 956: 21, 964: 8, 724: 3, 970: 22, 972: 1, 567: 1, 954: 1, 1314: 7, 688: 11, 1257: 3, 1303: 1, 660: 2, 241: 1, 746: 3, 1039: 2, 1412: 1, 572: 1, 559: 1, 433: 1, 457: 1, 438: 1, 1038: 1, 683: 2, 273: 1, 152: 1, 1385: 1, 373: 1, 445: 1, 622: 1, 379: 1, 21: 1, 143: 1, 1035: 1, 487: 1, 951: 1}}, 2: {'total': 1736, 'pass': 1579, 'fail': 157, 'pass_rate': 90.95622119815668, 'bins': {1: 1579, 1038: 4, 15: 1, 951: 2, 216: 1, 956: 28, 964: 6, 579: 8, 1599: 1, 660: 3, 1393: 1, 745: 1, 970: 30, 379: 8, 654: 16, 1257: 4, 241: 1, 1039: 6, 1314: 9, 337: 3, 449: 1, 550: 1, 954: 2, 683: 3, 373: 1, 1417: 1, 245: 1, 972: 1, 734: 1, 469: 1, 499: 1, 950: 1, 300: 1, 1258: 2, 753: 1, 1335: 1, 1005: 1, 724: 1, 746: 1, 38: 1}}}
📋 BIN Definition 對應: 1662 個 BIN 定義
✅ Summary 生成完成: 60 個 BIN, 2 個 Site
✅ 前7步驟完成
CSV 讀取成功: 3490 行 x 1666 欄
📁 生成標準Summary：YNT8726510993_F1FT_1_072025-07-02 08.36.39_summary.csv
📊 統計: Total=3478, Pass=3152, Fail=326, Yield=90.627%
📊 Metadata: Computer=GAX07, Date=07/01/25 18:39:02
📄 結構: 66行 CSV格式
============================================================
🎉 FT Summary生成完成
📊 處理時間：9.51秒
📁 Summary檔案：/mnt/d/project/python/outlook_summary/doc/YNT8726510993/YNT8726510993_F1FT_1_072025-07-02 08.36.39_summary.csv
============================================================

📊 進度：12/26
🔍 EQC檔案：YNT8726510993_F1FT_1_07_r12025-07-02 08.36.39.csv
✅ EQC檔案全通過：YNT8726510993_F1FT_1_07_r12025-07-02 08.36.39.csv
✅ EQC全通過記錄：YNT8726510993_F1FT_1_07_r12025-07-02 08.36.39.csv

📊 進度：13/26
⚡ 快速FT處理：YNT8726510993_F1FT_1_072025-07-02 22.28.25.csv
============================================================
🎯 開始FT Summary生成（簡化版）
📁 處理檔案：YNT8726510993_F1FT_1_072025-07-02 22.28.25.csv
============================================================
🔄 執行前7步驟：YNT8726510993_F1FT_1_072025-07-02 22.28.25.csv
CSV 讀取成功: 3492 行 x 1666 欄
執行步驟 1: CSV 結構補全
結構補全: 行7 C欄 空白 → '0.00.01'
結構補全: 行8 C欄 空白 → 'Test_Time'
結構補全: 行10 C欄 空白 → 'none'
結構補全: 行11 C欄 空白 → 'none'
✅ CSV 結構補全完成
執行步驟 2: BIN 號碼分配
BIN 分配: C6 → BIN 5
BIN 分配: D6 → BIN 6
BIN 分配: E6 → BIN 7
BIN 分配: F6 → BIN 8
BIN 分配: G6 → BIN 9
BIN 分配: H6 → BIN 10
BIN 分配: I6 → BIN 11
BIN 分配: J6 → BIN 12
BIN 分配: K6 → BIN 13
BIN 分配: L6 → BIN 14
  ... (省略中間 BIN 分配) ...
BIN 分配: Col16616 → BIN 1663
BIN 分配: Col16626 → BIN 1664
BIN 分配: Col16636 → BIN 1665
BIN 分配: Col16646 → BIN 1666
BIN 分配: Col16656 → BIN 1667
BIN 分配: Col16666 → BIN 1668
✅ BIN 號碼分配完成
執行步驟 3: BIN1 保護機制 (向量化優化版本)
🔍 發現 3218 個 BIN1 設備，識別時間: 0.001 秒
📊 批量資料提取完成: 0.126 秒，3218 設備 × 1664 測試項目
✅ 限值有效性檢查完成: 0.003 秒，1397 個有效測試項目
🛡️ 保護邏輯檢查完成: 2.019 秒，保護 49 個測試項目

🎉 向量化 BIN1 保護完成統計:
  ✅ 檢查 3218 個 BIN1 設備
  ⚡ 總處理時間: 2.150 秒
  📊 檢查次數: 4,495,546 次
  🚀 處理速度: 2,091,129 次檢查/秒
  🛡️ 保護項目: 49 個
  📈 性能提升: 向量化優化相比逐一檢查

📝 BIN1 保護記錄已附加到: datalog.txt
✅ BIN1 保護機制完成，共 98 個位置需要紅色標記
執行步驟 4: 全設備上下限檢查與 BIN 分類 (向量化優化版本)
📊 向量化預處理階段...
  資料規模: 3480 設備 × 1664 測試項目 = 5,790,720 次比較
  預處理完成: 2.934 秒
⚡ 向量化測試比較階段...
  向量化比較完成: 0.191 秒
🎯 向量化 BIN 分配階段...
  BIN 分配完成: 0.004 秒
📝 失敗位置記錄階段...
  位置記錄完成: 0.056 秒

🎉 向量化 BIN 分配完成統計:
  ✅ 完成 3480 個設備的 BIN 分配
  ⚡ 總處理時間: 3.186 秒
  📊 比較次數: 5,790,720 次
  🚀 處理速度: 1,817,784 次比較/秒
  🔴 需要標紅色的位置: 104758 個
  📈 性能提升: 向量化優化相比逐一處理
🔍 執行 Site 欄位動態查找
📝 Site 查找記錄已附加到: datalog.txt
✅ 找到 Site 欄位: E欄 (index 4) = 'Site_No'
🎯 生成 Summary 工作表: YNT8726510993_F1FT_1_072025-07-02 22.28.25.csv
📊 BIN 統計計算: 設備資料從第13行開始，BIN 欄位在第2欄
📊 BIN 統計: 總設備 3480 個，BIN 分佈 {553: 2, 1: 3218, 579: 8, 1312: 31, 1314: 3, 1599: 1, 956: 48, 245: 1, 654: 37, 472: 1, 718: 2, 970: 27, 64: 1, 940: 1, 153: 1, 1257: 7, 964: 7, 173: 2, 252: 1, 445: 2, 337: 2, 1301: 1, 660: 10, 550: 2, 453: 2, 428: 1, 491: 2, 1359: 1, 124: 1, 34: 1, 379: 9, 333: 1, 73: 1, 241: 2, 688: 1, 112: 1, 361: 1, 84: 2, 16: 1, 552: 1, 683: 4, 465: 1, 38: 2, 143: 4, 690: 2, 972: 2, 746: 1, 724: 1, 951: 3, 734: 1, 1260: 1, 954: 1, 1258: 1, 559: 1, 77: 1, 511: 1, 39: 1, 1127: 1, 1288: 1, 1039: 1, 1387: 1, 54: 1, 1356: 1, 1038: 1, 745: 1}
📊 Site 統計計算: 設備資料從第13行開始，Site 欄位在第5欄，BIN 欄位在第2欄
📊 Site 統計: 2 個 Site，詳細資料 {1: {'total': 1744, 'pass': 1603, 'fail': 141, 'pass_rate': 91.9151376146789, 'bins': {553: 1, 1: 1603, 1312: 31, 579: 3, 1314: 1, 1599: 1, 956: 22, 245: 1, 718: 1, 654: 23, 940: 1, 970: 14, 1257: 3, 964: 5, 173: 1, 1301: 1, 453: 2, 1359: 1, 124: 1, 660: 5, 73: 1, 379: 4, 241: 1, 688: 1, 112: 1, 84: 2, 552: 1, 38: 1, 972: 1, 724: 1, 734: 1, 954: 1, 951: 2, 1127: 1, 143: 1, 1356: 1, 1038: 1, 745: 1}}, 2: {'total': 1736, 'pass': 1615, 'fail': 121, 'pass_rate': 93.02995391705069, 'bins': {579: 5, 1: 1615, 1314: 2, 956: 26, 654: 14, 472: 1, 970: 13, 64: 1, 153: 1, 252: 1, 445: 2, 337: 2, 660: 5, 550: 2, 428: 1, 491: 2, 34: 1, 379: 5, 333: 1, 361: 1, 173: 1, 16: 1, 683: 4, 465: 1, 38: 1, 143: 3, 690: 2, 746: 1, 972: 1, 951: 1, 964: 2, 1257: 4, 1260: 1, 1258: 1, 559: 1, 718: 1, 77: 1, 511: 1, 39: 1, 1288: 1, 1039: 1, 241: 1, 1387: 1, 553: 1, 54: 1}}}
📋 BIN Definition 對應: 1662 個 BIN 定義
✅ Summary 生成完成: 65 個 BIN, 2 個 Site
✅ 前7步驟完成
CSV 讀取成功: 3492 行 x 1666 欄
📁 生成標準Summary：YNT8726510993_F1FT_1_072025-07-02 22.28.25_summary.csv
📊 統計: Total=3480, Pass=3218, Fail=262, Yield=92.471%
📊 Metadata: Computer=GAX07, Date=07/02/25 08:45:51
📄 結構: 71行 CSV格式
============================================================
🎉 FT Summary生成完成
📊 處理時間：8.09秒
📁 Summary檔案：/mnt/d/project/python/outlook_summary/doc/YNT8726510993/YNT8726510993_F1FT_1_072025-07-02 22.28.25_summary.csv
============================================================

📊 進度：14/26
🔍 EQC檔案：YNT8726510993_F1FT_1_07_r12025-07-02 22.28.25.csv
✅ EQC檔案全通過：YNT8726510993_F1FT_1_07_r12025-07-02 22.28.25.csv
✅ EQC全通過記錄：YNT8726510993_F1FT_1_07_r12025-07-02 22.28.25.csv

📊 進度：15/26
⚡ 快速FT處理：YNT8726510993_F1FT_1_072025-07-03 12.34.36.csv
============================================================
🎯 開始FT Summary生成（簡化版）
📁 處理檔案：YNT8726510993_F1FT_1_072025-07-03 12.34.36.csv
============================================================
🔄 執行前7步驟：YNT8726510993_F1FT_1_072025-07-03 12.34.36.csv
CSV 讀取成功: 3492 行 x 1666 欄
執行步驟 1: CSV 結構補全
結構補全: 行7 C欄 空白 → '0.00.01'
結構補全: 行8 C欄 空白 → 'Test_Time'
結構補全: 行10 C欄 空白 → 'none'
結構補全: 行11 C欄 空白 → 'none'
✅ CSV 結構補全完成
執行步驟 2: BIN 號碼分配
BIN 分配: C6 → BIN 5
BIN 分配: D6 → BIN 6
BIN 分配: E6 → BIN 7
BIN 分配: F6 → BIN 8
BIN 分配: G6 → BIN 9
BIN 分配: H6 → BIN 10
BIN 分配: I6 → BIN 11
BIN 分配: J6 → BIN 12
BIN 分配: K6 → BIN 13
BIN 分配: L6 → BIN 14
  ... (省略中間 BIN 分配) ...
BIN 分配: Col16616 → BIN 1663
BIN 分配: Col16626 → BIN 1664
BIN 分配: Col16636 → BIN 1665
BIN 分配: Col16646 → BIN 1666
BIN 分配: Col16656 → BIN 1667
BIN 分配: Col16666 → BIN 1668
✅ BIN 號碼分配完成
執行步驟 3: BIN1 保護機制 (向量化優化版本)
🔍 發現 3163 個 BIN1 設備，識別時間: 0.001 秒
📊 批量資料提取完成: 0.128 秒，3163 設備 × 1664 測試項目
✅ 限值有效性檢查完成: 0.003 秒，1397 個有效測試項目
🛡️ 保護邏輯檢查完成: 1.571 秒，保護 49 個測試項目

🎉 向量化 BIN1 保護完成統計:
  ✅ 檢查 3163 個 BIN1 設備
  ⚡ 總處理時間: 1.703 秒
  📊 檢查次數: 4,418,711 次
  🚀 處理速度: 2,595,164 次檢查/秒
  🛡️ 保護項目: 49 個
  📈 性能提升: 向量化優化相比逐一檢查

📝 BIN1 保護記錄已附加到: datalog.txt
✅ BIN1 保護機制完成，共 98 個位置需要紅色標記
執行步驟 4: 全設備上下限檢查與 BIN 分類 (向量化優化版本)
📊 向量化預處理階段...
  資料規模: 3480 設備 × 1664 測試項目 = 5,790,720 次比較
  預處理完成: 2.195 秒
⚡ 向量化測試比較階段...
  向量化比較完成: 0.182 秒
🎯 向量化 BIN 分配階段...
  BIN 分配完成: 0.002 秒
📝 失敗位置記錄階段...
  位置記錄完成: 0.035 秒

🎉 向量化 BIN 分配完成統計:
  ✅ 完成 3480 個設備的 BIN 分配
  ⚡ 總處理時間: 2.414 秒
  📊 比較次數: 5,790,720 次
  🚀 處理速度: 2,398,724 次比較/秒
  🔴 需要標紅色的位置: 106953 個
  📈 性能提升: 向量化優化相比逐一處理
🔍 執行 Site 欄位動態查找
📝 Site 查找記錄已附加到: datalog.txt
✅ 找到 Site 欄位: E欄 (index 4) = 'Site_No'
🎯 生成 Summary 工作表: YNT8726510993_F1FT_1_072025-07-03 12.34.36.csv
📊 BIN 統計計算: 設備資料從第13行開始，BIN 欄位在第2欄
📊 BIN 統計: 總設備 3480 個，BIN 分佈 {1: 3163, 1312: 57, 1258: 5, 956: 59, 972: 4, 654: 40, 1038: 5, 579: 17, 954: 2, 718: 4, 1314: 7, 465: 3, 964: 27, 302: 1, 1426: 1, 683: 1, 867: 1, 1039: 6, 638: 1, 970: 7, 545: 1, 660: 11, 1257: 10, 1417: 4, 1341: 1, 734: 2, 445: 2, 43: 1, 746: 1, 491: 3, 457: 3, 1259: 2, 143: 2, 337: 1, 550: 1, 1150: 1, 1206: 1, 194: 1, 293: 1, 241: 2, 865: 1, 552: 2, 478: 2, 326: 1, 1203: 1, 1414: 1, 453: 2, 735: 1, 1430: 1, 557: 2, 636: 1, 686: 1, 73: 1, 951: 1}
📊 Site 統計計算: 設備資料從第13行開始，Site 欄位在第5欄，BIN 欄位在第2欄
📊 Site 統計: 2 個 Site，詳細資料 {1: {'total': 1744, 'pass': 1553, 'fail': 191, 'pass_rate': 89.04816513761467, 'bins': {1: 1553, 1312: 57, 1258: 3, 972: 2, 654: 26, 1038: 3, 579: 9, 718: 3, 1314: 4, 465: 2, 956: 26, 302: 1, 1426: 1, 683: 1, 964: 11, 1039: 3, 970: 5, 1257: 6, 1417: 2, 660: 5, 734: 2, 445: 1, 43: 1, 746: 1, 491: 2, 1259: 1, 337: 1, 550: 1, 1150: 1, 194: 1, 293: 1, 478: 2, 326: 1, 457: 1, 1414: 1, 552: 1, 453: 1, 735: 1}}, 2: {'total': 1736, 'pass': 1610, 'fail': 126, 'pass_rate': 92.74193548387096, 'bins': {1: 1610, 956: 33, 954: 2, 579: 8, 964: 16, 1258: 2, 654: 14, 867: 1, 638: 1, 972: 2, 545: 1, 660: 6, 1417: 2, 1314: 3, 1341: 1, 465: 1, 445: 1, 457: 2, 1257: 4, 1038: 2, 143: 2, 1206: 1, 1039: 3, 241: 2, 1259: 1, 865: 1, 718: 1, 552: 1, 1203: 1, 1430: 1, 557: 2, 453: 1, 491: 1, 636: 1, 686: 1, 970: 2, 73: 1, 951: 1}}}
📋 BIN Definition 對應: 1662 個 BIN 定義
✅ Summary 生成完成: 54 個 BIN, 2 個 Site
✅ 前7步驟完成
CSV 讀取成功: 3492 行 x 1666 欄
📁 生成標準Summary：YNT8726510993_F1FT_1_072025-07-03 12.34.36_summary.csv
📊 統計: Total=3480, Pass=3163, Fail=317, Yield=90.891%
📊 Metadata: Computer=GAX07, Date=07/02/25 22:39:37
📄 結構: 60行 CSV格式
============================================================
🎉 FT Summary生成完成
📊 處理時間：6.48秒
📁 Summary檔案：/mnt/d/project/python/outlook_summary/doc/YNT8726510993/YNT8726510993_F1FT_1_072025-07-03 12.34.36_summary.csv
============================================================

📊 進度：16/26
🔍 EQC檔案：YNT8726510993_F1FT_1_07_r12025-07-03 12.34.36.csv
✅ EQC檔案全通過：YNT8726510993_F1FT_1_07_r12025-07-03 12.34.36.csv
✅ EQC全通過記錄：YNT8726510993_F1FT_1_07_r12025-07-03 12.34.36.csv

📊 進度：17/26
⚡ 快速FT處理：YNT8726510993_F1FT_1_072025-07-03 13.58.48.csv
============================================================
🎯 開始FT Summary生成（簡化版）
📁 處理檔案：YNT8726510993_F1FT_1_072025-07-03 13.58.48.csv
============================================================
🔄 執行前7步驟：YNT8726510993_F1FT_1_072025-07-03 13.58.48.csv
CSV 讀取成功: 18 行 x 1666 欄
執行步驟 1: CSV 結構補全
結構補全: 行7 C欄 空白 → '0.00.01'
結構補全: 行8 C欄 空白 → 'Test_Time'
結構補全: 行10 C欄 空白 → 'none'
結構補全: 行11 C欄 空白 → 'none'
✅ CSV 結構補全完成
執行步驟 2: BIN 號碼分配
BIN 分配: C6 → BIN 5
BIN 分配: D6 → BIN 6
BIN 分配: E6 → BIN 7
BIN 分配: F6 → BIN 8
BIN 分配: G6 → BIN 9
BIN 分配: H6 → BIN 10
BIN 分配: I6 → BIN 11
BIN 分配: J6 → BIN 12
BIN 分配: K6 → BIN 13
BIN 分配: L6 → BIN 14
  ... (省略中間 BIN 分配) ...
BIN 分配: Col16616 → BIN 1663
BIN 分配: Col16626 → BIN 1664
BIN 分配: Col16636 → BIN 1665
BIN 分配: Col16646 → BIN 1666
BIN 分配: Col16656 → BIN 1667
BIN 分配: Col16666 → BIN 1668
✅ BIN 號碼分配完成
執行步驟 3: BIN1 保護機制 (向量化優化版本)
沒有 BIN1 設備，無需 BIN1 保護
執行步驟 4: 全設備上下限檢查與 BIN 分類 (向量化優化版本)
📊 向量化預處理階段...
  資料規模: 6 設備 × 1664 測試項目 = 9,984 次比較
  預處理完成: 0.006 秒
⚡ 向量化測試比較階段...
  向量化比較完成: 0.000 秒
🎯 向量化 BIN 分配階段...
  BIN 分配完成: 0.000 秒
📝 失敗位置記錄階段...
  位置記錄完成: 0.001 秒

🎉 向量化 BIN 分配完成統計:
  ✅ 完成 6 個設備的 BIN 分配
  ⚡ 總處理時間: 0.007 秒
  📊 比較次數: 9,984 次
  🚀 處理速度: 1,515,621 次比較/秒
  🔴 需要標紅色的位置: 3930 個
  📈 性能提升: 向量化優化相比逐一處理
🔍 執行 Site 欄位動態查找
📝 Site 查找記錄已附加到: datalog.txt
✅ 找到 Site 欄位: E欄 (index 4) = 'Site_No'
🎯 生成 Summary 工作表: YNT8726510993_F1FT_1_072025-07-03 13.58.48.csv
📊 BIN 統計計算: 設備資料從第13行開始，BIN 欄位在第2欄
📊 BIN 統計: 總設備 6 個，BIN 分佈 {516: 6}
📊 Site 統計計算: 設備資料從第13行開始，Site 欄位在第5欄，BIN 欄位在第2欄
📊 Site 統計: 1 個 Site，詳細資料 {1: {'total': 6, 'pass': 0, 'fail': 6, 'pass_rate': 0.0, 'bins': {516: 6}}}
📋 BIN Definition 對應: 1662 個 BIN 定義
✅ Summary 生成完成: 2 個 BIN, 1 個 Site
✅ 前7步驟完成
CSV 讀取成功: 18 行 x 1666 欄
📁 生成標準Summary：YNT8726510993_F1FT_1_072025-07-03 13.58.48_summary.csv
📊 統計: Total=6, Pass=0, Fail=6, Yield=0.000%
📊 Metadata: Computer=GAX07, Date=07/03/25 12:47:32
📄 結構: 8行 CSV格式
============================================================
🎉 FT Summary生成完成
📊 處理時間：0.11秒
📁 Summary檔案：/mnt/d/project/python/outlook_summary/doc/YNT8726510993/YNT8726510993_F1FT_1_072025-07-03 13.58.48_summary.csv
============================================================

📊 進度：18/26
⚡ 快速FT處理：YNT8726510993_F1FT_1_072025-07-04 04.02.44.csv
============================================================
🎯 開始FT Summary生成（簡化版）
📁 處理檔案：YNT8726510993_F1FT_1_072025-07-04 04.02.44.csv
============================================================
🔄 執行前7步驟：YNT8726510993_F1FT_1_072025-07-04 04.02.44.csv
CSV 讀取成功: 3486 行 x 1666 欄
執行步驟 1: CSV 結構補全
結構補全: 行7 C欄 空白 → '0.00.01'
結構補全: 行8 C欄 空白 → 'Test_Time'
結構補全: 行10 C欄 空白 → 'none'
結構補全: 行11 C欄 空白 → 'none'
✅ CSV 結構補全完成
執行步驟 2: BIN 號碼分配
BIN 分配: C6 → BIN 5
BIN 分配: D6 → BIN 6
BIN 分配: E6 → BIN 7
BIN 分配: F6 → BIN 8
BIN 分配: G6 → BIN 9
BIN 分配: H6 → BIN 10
BIN 分配: I6 → BIN 11
BIN 分配: J6 → BIN 12
BIN 分配: K6 → BIN 13
BIN 分配: L6 → BIN 14
  ... (省略中間 BIN 分配) ...
BIN 分配: Col16616 → BIN 1663
BIN 分配: Col16626 → BIN 1664
BIN 分配: Col16636 → BIN 1665
BIN 分配: Col16646 → BIN 1666
BIN 分配: Col16656 → BIN 1667
BIN 分配: Col16666 → BIN 1668
✅ BIN 號碼分配完成
執行步驟 3: BIN1 保護機制 (向量化優化版本)
🔍 發現 3222 個 BIN1 設備，識別時間: 0.001 秒
📊 批量資料提取完成: 0.112 秒，3222 設備 × 1664 測試項目
✅ 限值有效性檢查完成: 0.002 秒，1397 個有效測試項目
🛡️ 保護邏輯檢查完成: 1.969 秒，保護 49 個測試項目

🎉 向量化 BIN1 保護完成統計:
  ✅ 檢查 3222 個 BIN1 設備
  ⚡ 總處理時間: 2.084 秒
  📊 檢查次數: 4,501,134 次
  🚀 處理速度: 2,159,747 次檢查/秒
  🛡️ 保護項目: 49 個
  📈 性能提升: 向量化優化相比逐一檢查

📝 BIN1 保護記錄已附加到: datalog.txt
✅ BIN1 保護機制完成，共 98 個位置需要紅色標記
執行步驟 4: 全設備上下限檢查與 BIN 分類 (向量化優化版本)
📊 向量化預處理階段...
  資料規模: 3474 設備 × 1664 測試項目 = 5,780,736 次比較
  預處理完成: 2.491 秒
⚡ 向量化測試比較階段...
  向量化比較完成: 0.196 秒
🎯 向量化 BIN 分配階段...
  BIN 分配完成: 0.002 秒
📝 失敗位置記錄階段...
  位置記錄完成: 0.053 秒

🎉 向量化 BIN 分配完成統計:
  ✅ 完成 3474 個設備的 BIN 分配
  ⚡ 總處理時間: 2.742 秒
  📊 比較次數: 5,780,736 次
  🚀 處理速度: 2,108,195 次比較/秒
  🔴 需要標紅色的位置: 100848 個
  📈 性能提升: 向量化優化相比逐一處理
🔍 執行 Site 欄位動態查找
📝 Site 查找記錄已附加到: datalog.txt
✅ 找到 Site 欄位: E欄 (index 4) = 'Site_No'
🎯 生成 Summary 工作表: YNT8726510993_F1FT_1_072025-07-04 04.02.44.csv
📊 BIN 統計計算: 設備資料從第13行開始，BIN 欄位在第2欄
📊 BIN 統計: 總設備 3474 個，BIN 分佈 {1: 3222, 1312: 40, 379: 20, 660: 6, 107: 1, 654: 18, 30: 1, 956: 19, 1257: 3, 51: 1, 1039: 10, 552: 2, 954: 2, 524: 1, 972: 3, 124: 2, 1414: 4, 376: 1, 724: 5, 683: 7, 970: 40, 964: 1, 579: 5, 1054: 1, 241: 2, 867: 1, 598: 1, 746: 1, 38: 2, 337: 3, 1038: 2, 1053: 1, 143: 4, 491: 1, 937: 1, 269: 1, 686: 9, 718: 3, 409: 1, 511: 1, 66: 1, 76: 1, 1148: 1, 465: 1, 549: 1, 553: 1, 22: 1, 457: 2, 292: 1, 761: 1, 58: 1, 112: 1, 751: 1, 445: 2, 550: 1, 14: 1, 1314: 2, 65: 1, 1220: 1, 684: 1, 1417: 1, 48: 1}
📊 Site 統計計算: 設備資料從第13行開始，Site 欄位在第5欄，BIN 欄位在第2欄
📊 Site 統計: 2 個 Site，詳細資料 {1: {'total': 1741, 'pass': 1604, 'fail': 137, 'pass_rate': 92.13095921883975, 'bins': {1: 1604, 1312: 40, 660: 5, 107: 1, 654: 13, 956: 6, 1257: 3, 1039: 7, 524: 1, 124: 2, 1414: 3, 376: 1, 724: 4, 379: 9, 579: 3, 970: 11, 1054: 1, 241: 1, 867: 1, 598: 1, 746: 1, 337: 2, 1053: 1, 491: 1, 718: 2, 683: 3, 66: 1, 549: 1, 553: 1, 292: 1, 143: 2, 1038: 1, 112: 1, 751: 1, 552: 1, 14: 1, 65: 1, 1220: 1, 972: 1}}, 2: {'total': 1733, 'pass': 1618, 'fail': 115, 'pass_rate': 93.36410848240047, 'bins': {1: 1618, 379: 11, 30: 1, 956: 13, 51: 1, 552: 1, 954: 2, 972: 2, 654: 5, 683: 4, 970: 29, 964: 1, 724: 1, 38: 2, 1039: 3, 1038: 1, 143: 2, 579: 2, 937: 1, 269: 1, 1414: 1, 686: 9, 241: 1, 409: 1, 511: 1, 76: 1, 1148: 1, 465: 1, 337: 1, 22: 1, 457: 2, 761: 1, 58: 1, 718: 1, 445: 2, 550: 1, 660: 1, 1314: 2, 684: 1, 1417: 1, 48: 1}}}
📋 BIN Definition 對應: 1662 個 BIN 定義
✅ Summary 生成完成: 62 個 BIN, 2 個 Site
✅ 前7步驟完成
CSV 讀取成功: 3486 行 x 1666 欄
📁 生成標準Summary：YNT8726510993_F1FT_1_072025-07-04 04.02.44_summary.csv
📊 統計: Total=3474, Pass=3222, Fail=252, Yield=92.746%
📊 Metadata: Computer=GAX07, Date=07/03/25 14:25:51
📄 結構: 68行 CSV格式
============================================================
🎉 FT Summary生成完成
📊 處理時間：7.29秒
📁 Summary檔案：/mnt/d/project/python/outlook_summary/doc/YNT8726510993/YNT8726510993_F1FT_1_072025-07-04 04.02.44_summary.csv
============================================================

📊 進度：19/26
🔍 EQC檔案：YNT8726510993_F1FT_1_07_r12025-07-04 04.02.44.csv
✅ EQC檔案全通過：YNT8726510993_F1FT_1_07_r12025-07-04 04.02.44.csv
✅ EQC全通過記錄：YNT8726510993_F1FT_1_07_r12025-07-04 04.02.44.csv

📊 進度：20/26
⚡ 快速FT處理：YNT8726510993_F1FT_1_072025-07-04 14.02.54.csv
============================================================
🎯 開始FT Summary生成（簡化版）
📁 處理檔案：YNT8726510993_F1FT_1_072025-07-04 14.02.54.csv
============================================================
🔄 執行前7步驟：YNT8726510993_F1FT_1_072025-07-04 14.02.54.csv
CSV 讀取成功: 2058 行 x 1666 欄
執行步驟 1: CSV 結構補全
結構補全: 行7 C欄 空白 → '0.00.01'
結構補全: 行8 C欄 空白 → 'Test_Time'
結構補全: 行10 C欄 空白 → 'none'
結構補全: 行11 C欄 空白 → 'none'
✅ CSV 結構補全完成
執行步驟 2: BIN 號碼分配
BIN 分配: C6 → BIN 5
BIN 分配: D6 → BIN 6
BIN 分配: E6 → BIN 7
BIN 分配: F6 → BIN 8
BIN 分配: G6 → BIN 9
BIN 分配: H6 → BIN 10
BIN 分配: I6 → BIN 11
BIN 分配: J6 → BIN 12
BIN 分配: K6 → BIN 13
BIN 分配: L6 → BIN 14
  ... (省略中間 BIN 分配) ...
BIN 分配: Col16616 → BIN 1663
BIN 分配: Col16626 → BIN 1664
BIN 分配: Col16636 → BIN 1665
BIN 分配: Col16646 → BIN 1666
BIN 分配: Col16656 → BIN 1667
BIN 分配: Col16666 → BIN 1668
✅ BIN 號碼分配完成
執行步驟 3: BIN1 保護機制 (向量化優化版本)
🔍 發現 1857 個 BIN1 設備，識別時間: 0.001 秒
📊 批量資料提取完成: 0.066 秒，1857 設備 × 1664 測試項目
✅ 限值有效性檢查完成: 0.002 秒，1397 個有效測試項目
🛡️ 保護邏輯檢查完成: 1.120 秒，保護 49 個測試項目

🎉 向量化 BIN1 保護完成統計:
  ✅ 檢查 1857 個 BIN1 設備
  ⚡ 總處理時間: 1.189 秒
  📊 檢查次數: 2,594,229 次
  🚀 處理速度: 2,181,801 次檢查/秒
  🛡️ 保護項目: 49 個
  📈 性能提升: 向量化優化相比逐一檢查

📝 BIN1 保護記錄已附加到: datalog.txt
✅ BIN1 保護機制完成，共 98 個位置需要紅色標記
執行步驟 4: 全設備上下限檢查與 BIN 分類 (向量化優化版本)
📊 向量化預處理階段...
  資料規模: 2046 設備 × 1664 測試項目 = 3,404,544 次比較
  預處理完成: 1.203 秒
⚡ 向量化測試比較階段...
  向量化比較完成: 0.062 秒
🎯 向量化 BIN 分配階段...
  BIN 分配完成: 0.001 秒
📝 失敗位置記錄階段...
  位置記錄完成: 0.023 秒

🎉 向量化 BIN 分配完成統計:
  ✅ 完成 2046 個設備的 BIN 分配
  ⚡ 總處理時間: 1.289 秒
  📊 比較次數: 3,404,544 次
  🚀 處理速度: 2,641,618 次比較/秒
  🔴 需要標紅色的位置: 72925 個
  📈 性能提升: 向量化優化相比逐一處理
🔍 執行 Site 欄位動態查找
📝 Site 查找記錄已附加到: datalog.txt
✅ 找到 Site 欄位: E欄 (index 4) = 'Site_No'
🎯 生成 Summary 工作表: YNT8726510993_F1FT_1_072025-07-04 14.02.54.csv
📊 BIN 統計計算: 設備資料從第13行開始，BIN 欄位在第2欄
📊 BIN 統計: 總設備 2046 個，BIN 分佈 {1: 1857, 1312: 22, 686: 20, 579: 2, 337: 3, 751: 1, 349: 1, 1314: 6, 654: 14, 867: 1, 746: 5, 1039: 5, 956: 26, 457: 1, 269: 1, 964: 5, 487: 1, 970: 23, 660: 3, 51: 1, 1258: 3, 724: 3, 445: 1, 453: 1, 433: 1, 552: 1, 379: 3, 241: 1, 1053: 1, 367: 1, 206: 1, 143: 1, 683: 2, 124: 1, 718: 2, 361: 1, 1038: 1, 421: 1, 495: 1, 951: 5, 1257: 1, 465: 1, 954: 1, 553: 10, 42: 1, 62: 1, 18: 1}
📊 Site 統計計算: 設備資料從第13行開始，Site 欄位在第5欄，BIN 欄位在第2欄
📊 Site 統計: 2 個 Site，詳細資料 {1: {'total': 937, 'pass': 850, 'fail': 87, 'pass_rate': 90.71504802561367, 'bins': {1: 850, 1312: 22, 579: 2, 349: 1, 654: 5, 746: 3, 1039: 3, 269: 1, 964: 1, 487: 1, 956: 10, 660: 1, 445: 1, 453: 1, 433: 1, 1258: 2, 379: 2, 337: 1, 241: 1, 1053: 1, 724: 1, 683: 1, 124: 1, 718: 1, 1314: 2, 951: 2, 970: 7, 465: 1, 954: 1, 553: 10}}, 2: {'total': 1109, 'pass': 1007, 'fail': 102, 'pass_rate': 90.80252479711451, 'bins': {1: 1007, 686: 20, 337: 2, 751: 1, 1314: 4, 867: 1, 654: 9, 956: 16, 457: 1, 970: 16, 964: 4, 51: 1, 1258: 1, 724: 2, 746: 2, 552: 1, 367: 1, 206: 1, 143: 1, 361: 1, 1038: 1, 421: 1, 495: 1, 683: 1, 1257: 1, 660: 2, 718: 1, 1039: 2, 951: 3, 42: 1, 379: 1, 62: 1, 18: 1}}}
📋 BIN Definition 對應: 1662 個 BIN 定義
✅ Summary 生成完成: 47 個 BIN, 2 個 Site
✅ 前7步驟完成
CSV 讀取成功: 2058 行 x 1666 欄
📁 生成標準Summary：YNT8726510993_F1FT_1_072025-07-04 14.02.54_summary.csv
📊 統計: Total=2046, Pass=1857, Fail=189, Yield=90.762%
📊 Metadata: Computer=GAX07, Date=07/04/25 04:10:57
📄 結構: 53行 CSV格式
============================================================
🎉 FT Summary生成完成
📊 處理時間：3.89秒
📁 Summary檔案：/mnt/d/project/python/outlook_summary/doc/YNT8726510993/YNT8726510993_F1FT_1_072025-07-04 14.02.54_summary.csv
============================================================

📊 進度：21/26
🔍 EQC檔案：YNT8726510993_F1FT_1_07_r12025-07-04 14.02.54.csv
✅ EQC檔案全通過：YNT8726510993_F1FT_1_07_r12025-07-04 14.02.54.csv
✅ EQC全通過記錄：YNT8726510993_F1FT_1_07_r12025-07-04 14.02.54.csv

📊 進度：22/26
⚡ 快速FT處理：YNT8726510993_F1R1_1_07.csv
============================================================
🎯 開始FT Summary生成（簡化版）
📁 處理檔案：YNT8726510993_F1R1_1_07.csv
============================================================
🔄 執行前7步驟：YNT8726510993_F1R1_1_07.csv
CSV 讀取成功: 2520 行 x 1666 欄
執行步驟 1: CSV 結構補全
結構補全: 行7 C欄 空白 → '0.00.01'
結構補全: 行8 C欄 空白 → 'Test_Time'
結構補全: 行10 C欄 空白 → 'none'
結構補全: 行11 C欄 空白 → 'none'
✅ CSV 結構補全完成
執行步驟 2: BIN 號碼分配
BIN 分配: C6 → BIN 5
BIN 分配: D6 → BIN 6
BIN 分配: E6 → BIN 7
BIN 分配: F6 → BIN 8
BIN 分配: G6 → BIN 9
BIN 分配: H6 → BIN 10
BIN 分配: I6 → BIN 11
BIN 分配: J6 → BIN 12
BIN 分配: K6 → BIN 13
BIN 分配: L6 → BIN 14
  ... (省略中間 BIN 分配) ...
BIN 分配: Col16616 → BIN 1663
BIN 分配: Col16626 → BIN 1664
BIN 分配: Col16636 → BIN 1665
BIN 分配: Col16646 → BIN 1666
BIN 分配: Col16656 → BIN 1667
BIN 分配: Col16666 → BIN 1668
✅ BIN 號碼分配完成
執行步驟 3: BIN1 保護機制 (向量化優化版本)
🔍 發現 743 個 BIN1 設備，識別時間: 0.006 秒
📊 批量資料提取完成: 0.049 秒，743 設備 × 1664 測試項目
✅ 限值有效性檢查完成: 0.006 秒，1397 個有效測試項目
🛡️ 保護邏輯檢查完成: 1.313 秒，保護 49 個測試項目

🎉 向量化 BIN1 保護完成統計:
  ✅ 檢查 743 個 BIN1 設備
  ⚡ 總處理時間: 1.373 秒
  📊 檢查次數: 1,037,971 次
  🚀 處理速度: 755,740 次檢查/秒
  🛡️ 保護項目: 49 個
  📈 性能提升: 向量化優化相比逐一檢查

📝 BIN1 保護記錄已附加到: datalog.txt
✅ BIN1 保護機制完成，共 98 個位置需要紅色標記
執行步驟 4: 全設備上下限檢查與 BIN 分類 (向量化優化版本)
📊 向量化預處理階段...
  資料規模: 2508 設備 × 1664 測試項目 = 4,173,312 次比較
  預處理完成: 4.955 秒
⚡ 向量化測試比較階段...
  向量化比較完成: 0.429 秒
🎯 向量化 BIN 分配階段...
  BIN 分配完成: 0.043 秒
📝 失敗位置記錄階段...
  位置記錄完成: 2.221 秒

🎉 向量化 BIN 分配完成統計:
  ✅ 完成 2508 個設備的 BIN 分配
  ⚡ 總處理時間: 7.649 秒
  📊 比較次數: 4,173,312 次
  🚀 處理速度: 545,631 次比較/秒
  🔴 需要標紅色的位置: 726612 個
  📈 性能提升: 向量化優化相比逐一處理
🔍 執行 Site 欄位動態查找
📝 Site 查找記錄已附加到: datalog.txt
✅ 找到 Site 欄位: E欄 (index 4) = 'Site_No'
🎯 生成 Summary 工作表: YNT8726510993_F1R1_1_07.csv
📊 BIN 統計計算: 設備資料從第13行開始，BIN 欄位在第2欄
📊 BIN 統計: 總設備 2508 個，BIN 分佈 {1258: 14, 1039: 62, 147: 1, 1257: 42, 951: 17, 511: 6, 337: 20, 422: 2, 686: 1, 1: 743, 1400: 1, 970: 119, 507: 4, 379: 49, 1038: 26, 195: 1, 174: 1, 177: 1, 453: 11, 491: 7, 465: 6, 956: 261, 964: 98, 579: 71, 15: 25, 1078: 1, 38: 15, 48: 6, 24: 1, 172: 1, 745: 5, 1053: 5, 1314: 34, 363: 1, 445: 9, 397: 3, 1303: 2, 78: 1, 247: 1, 1054: 5, 746: 16, 550: 9, 143: 22, 972: 20, 1360: 1, 171: 1, 361: 3, 385: 1, 1317: 1, 457: 9, 1035: 3, 279: 1, 1312: 132, 953: 1, 241: 6, 552: 5, 325: 2, 1348: 1, 1356: 1, 73: 4, 553: 3, 1040: 2, 954: 5, 883: 1, 421: 1, 114: 2, 326: 4, 1359: 2, 245: 1, 563: 1, 403: 1, 549: 1, 580: 1, 475: 1, 67: 1, 270: 1, 1414: 2, 364: 1, 263: 1, 1474: 2, 753: 1, 963: 1, 353: 1, 409: 2, 380: 1, 69: 4, 329: 1, 472: 2, 153: 1, 940: 1, 252: 1, 173: 2, 428: 1, 1260: 1, 1417: 3, 1127: 1, 1288: 1, 302: 1, 1426: 1, 867: 3, 545: 1, 341: 1, 1341: 1, 1259: 3, 254: 1, 194: 2, 654: 248, 478: 2, 449: 4, 1203: 1, 1358: 2, 1430: 1, 49: 2, 216: 1, 1393: 1, 1349: 1, 70: 1, 373: 2, 572: 1, 433: 1, 438: 1, 469: 1, 499: 1, 273: 1, 950: 1, 152: 1, 1005: 2, 300: 1, 747: 1, 487: 2, 221: 1, 163: 1, 16: 1, 30: 4, 751: 3, 426: 1, 1129: 1, 1205: 1, 660: 74, 1307: 1, 217: 1, 345: 1, 718: 18, 107: 2, 724: 16, 734: 4, 683: 34, 622: 4, 600: 1, 124: 3, 595: 1, 670: 1, 638: 1, 735: 1, 636: 1, 682: 3, 681: 1, 610: 1, 616: 1, 61: 1, 21: 1, 46: 1, 60: 1, 43: 1, 77: 1, 39: 1, 34: 1, 64: 1, 560: 1, 29: 1, 17: 2, 57: 1, 23: 3, 59: 1, 28: 2, 26: 2, 51: 5, 14: 1, 112: 1, 80: 1, 20: 2, 524: 1, 376: 1, 937: 1, 761: 1, 1220: 1, 598: 1, 684: 1, 269: 1, 349: 1, 188: 1, 206: 1, 293: 1, 495: 1, 79: 1, 42: 1, 18: 1, 62: 1, 76: 1, 22: 1, 58: 1, 66: 1, 590: 1}
📊 Site 統計計算: 設備資料從第13行開始，Site 欄位在第5欄，BIN 欄位在第2欄
📊 Site 統計: 2 個 Site，詳細資料 {1: {'total': 1256, 'pass': 303, 'fail': 953, 'pass_rate': 24.12420382165605, 'bins': {1258: 7, 147: 1, 951: 8, 337: 9, 1257: 28, 1: 303, 970: 47, 507: 4, 195: 1, 1039: 29, 453: 6, 465: 3, 956: 140, 964: 53, 579: 39, 15: 11, 38: 10, 48: 3, 172: 1, 1053: 3, 1314: 16, 379: 30, 397: 3, 78: 1, 746: 8, 550: 6, 143: 6, 972: 10, 1360: 1, 171: 1, 361: 3, 385: 1, 1038: 13, 1035: 1, 457: 2, 279: 1, 1312: 132, 1356: 1, 241: 3, 954: 4, 883: 1, 421: 1, 326: 2, 245: 1, 403: 1, 549: 1, 580: 1, 475: 1, 445: 2, 1040: 1, 67: 1, 1414: 1, 263: 1, 1474: 2, 963: 1, 422: 1, 745: 2, 380: 1, 69: 3, 329: 1, 940: 1, 173: 1, 428: 1, 1359: 1, 491: 3, 1260: 1, 1127: 1, 553: 1, 341: 1, 254: 1, 654: 132, 1203: 1, 478: 1, 49: 2, 449: 3, 373: 1, 572: 1, 433: 1, 469: 1, 499: 1, 950: 1, 152: 1, 1005: 2, 747: 1, 16: 1, 1129: 1, 552: 2, 472: 1, 1259: 1, 660: 33, 718: 6, 107: 2, 724: 10, 734: 3, 124: 3, 683: 14, 595: 1, 670: 1, 638: 1, 681: 1, 682: 1, 61: 1, 46: 1, 73: 3, 60: 1, 34: 1, 64: 1, 29: 1, 57: 1, 59: 1, 26: 1, 51: 3, 23: 1, 112: 1, 80: 1, 20: 2, 524: 1, 937: 1, 511: 1, 194: 1, 751: 2, 1417: 1, 114: 1, 598: 1, 867: 1, 349: 1, 487: 1, 30: 2, 206: 1, 79: 1, 18: 1, 66: 1}}, 2: {'total': 1252, 'pass': 440, 'fail': 812, 'pass_rate': 35.14376996805112, 'bins': {1039: 33, 1257: 14, 511: 5, 422: 1, 686: 1, 1400: 1, 970: 72, 1: 440, 379: 19, 1038: 13, 174: 1, 177: 1, 491: 4, 956: 121, 964: 45, 579: 32, 1078: 1, 951: 9, 24: 1, 745: 3, 1258: 7, 1314: 18, 337: 11, 363: 1, 445: 7, 1303: 2, 247: 1, 1054: 5, 143: 16, 972: 10, 1317: 1, 457: 7, 953: 1, 1035: 2, 241: 3, 552: 3, 325: 2, 1348: 1, 73: 1, 1053: 2, 38: 5, 48: 3, 550: 3, 553: 2, 1040: 1, 114: 1, 1359: 1, 563: 1, 326: 2, 15: 14, 270: 1, 364: 1, 753: 1, 353: 1, 409: 2, 472: 1, 153: 1, 252: 1, 173: 1, 465: 3, 453: 5, 1417: 2, 1288: 1, 954: 1, 302: 1, 1426: 1, 867: 2, 545: 1, 1341: 1, 1259: 2, 194: 1, 478: 1, 449: 1, 1358: 2, 1430: 1, 216: 1, 1393: 1, 1349: 1, 70: 1, 438: 1, 746: 8, 273: 1, 300: 1, 373: 1, 487: 1, 221: 1, 163: 1, 30: 2, 751: 1, 426: 1, 1205: 1, 660: 41, 1307: 1, 217: 1, 345: 1, 654: 116, 718: 12, 683: 20, 622: 4, 600: 1, 735: 1, 636: 1, 682: 2, 724: 6, 734: 1, 610: 1, 616: 1, 21: 1, 43: 1, 77: 1, 39: 1, 560: 1, 69: 1, 17: 2, 23: 2, 28: 2, 14: 1, 376: 1, 1414: 1, 26: 1, 761: 1, 51: 2, 1220: 1, 684: 1, 269: 1, 188: 1, 293: 1, 495: 1, 42: 1, 62: 1, 76: 1, 22: 1, 58: 1, 590: 1}}}
📋 BIN Definition 對應: 1662 個 BIN 定義
✅ Summary 生成完成: 203 個 BIN, 2 個 Site
✅ 前7步驟完成
CSV 讀取成功: 2520 行 x 1666 欄
📁 生成標準Summary：YNT8726510993_F1R1_1_07_summary.csv
📊 統計: Total=2508, Pass=743, Fail=1765, Yield=29.625%
📊 Metadata: Computer=GAX07, Date=07/04/25 18:19:45
📄 結構: 209行 CSV格式
============================================================
🎉 FT Summary生成完成
📊 處理時間：14.19秒
📁 Summary檔案：/mnt/d/project/python/outlook_summary/doc/YNT8726510993/YNT8726510993_F1R1_1_07_summary.csv
============================================================

📊 進度：23/26
🔍 EQC檔案：YNT8726510993_F1R1_1_07_r1.csv
✅ EQC檔案全通過：YNT8726510993_F1R1_1_07_r1.csv
✅ EQC全通過記錄：YNT8726510993_F1R1_1_07_r1.csv

📊 進度：24/26
⚡ 快速FT處理：YNT8726510993_F1R2_1_07.csv
============================================================
🎯 開始FT Summary生成（簡化版）
📁 處理檔案：YNT8726510993_F1R2_1_07.csv
============================================================
🔄 執行前7步驟：YNT8726510993_F1R2_1_07.csv
CSV 讀取成功: 1778 行 x 1666 欄
執行步驟 1: CSV 結構補全
結構補全: 行7 C欄 空白 → '0.00.01'
結構補全: 行8 C欄 空白 → 'Test_Time'
結構補全: 行10 C欄 空白 → 'none'
結構補全: 行11 C欄 空白 → 'none'
✅ CSV 結構補全完成
執行步驟 2: BIN 號碼分配
BIN 分配: C6 → BIN 5
BIN 分配: D6 → BIN 6
BIN 分配: E6 → BIN 7
BIN 分配: F6 → BIN 8
BIN 分配: G6 → BIN 9
BIN 分配: H6 → BIN 10
BIN 分配: I6 → BIN 11
BIN 分配: J6 → BIN 12
BIN 分配: K6 → BIN 13
BIN 分配: L6 → BIN 14
  ... (省略中間 BIN 分配) ...
BIN 分配: Col16616 → BIN 1663
BIN 分配: Col16626 → BIN 1664
BIN 分配: Col16636 → BIN 1665
BIN 分配: Col16646 → BIN 1666
BIN 分配: Col16656 → BIN 1667
BIN 分配: Col16666 → BIN 1668
✅ BIN 號碼分配完成
執行步驟 3: BIN1 保護機制 (向量化優化版本)
🔍 發現 169 個 BIN1 設備，識別時間: 0.003 秒
📊 批量資料提取完成: 0.015 秒，169 設備 × 1664 測試項目
✅ 限值有效性檢查完成: 0.008 秒，1397 個有效測試項目
🛡️ 保護邏輯檢查完成: 0.679 秒，保護 49 個測試項目

🎉 向量化 BIN1 保護完成統計:
  ✅ 檢查 169 個 BIN1 設備
  ⚡ 總處理時間: 0.706 秒
  📊 檢查次數: 236,093 次
  🚀 處理速度: 334,601 次檢查/秒
  🛡️ 保護項目: 49 個
  📈 性能提升: 向量化優化相比逐一檢查

📝 BIN1 保護記錄已附加到: datalog.txt
✅ BIN1 保護機制完成，共 98 個位置需要紅色標記
執行步驟 4: 全設備上下限檢查與 BIN 分類 (向量化優化版本)
📊 向量化預處理階段...
  資料規模: 1766 設備 × 1664 測試項目 = 2,938,624 次比較
  預處理完成: 2.819 秒
⚡ 向量化測試比較階段...
  向量化比較完成: 0.069 秒
🎯 向量化 BIN 分配階段...
  BIN 分配完成: 0.007 秒
📝 失敗位置記錄階段...
  位置記錄完成: 0.368 秒

🎉 向量化 BIN 分配完成統計:
  ✅ 完成 1766 個設備的 BIN 分配
  ⚡ 總處理時間: 3.264 秒
  📊 比較次數: 2,938,624 次
  🚀 處理速度: 900,313 次比較/秒
  🔴 需要標紅色的位置: 681329 個
  📈 性能提升: 向量化優化相比逐一處理
🔍 執行 Site 欄位動態查找
📝 Site 查找記錄已附加到: datalog.txt
✅ 找到 Site 欄位: E欄 (index 4) = 'Site_No'
🎯 生成 Summary 工作表: YNT8726510993_F1R2_1_07.csv
📊 BIN 統計計算: 設備資料從第13行開始，BIN 欄位在第2欄
📊 BIN 統計: 總設備 1766 個，BIN 分佈 {956: 209, 1: 169, 954: 5, 1038: 25, 964: 94, 241: 6, 579: 67, 1257: 40, 970: 87, 1039: 59, 1303: 2, 449: 5, 1314: 24, 143: 22, 107: 1, 550: 7, 438: 1, 572: 1, 379: 38, 433: 1, 457: 9, 972: 22, 469: 1, 951: 18, 499: 1, 746: 15, 273: 1, 152: 1, 337: 20, 1005: 2, 950: 1, 300: 1, 1258: 14, 749: 1, 373: 1, 1312: 87, 445: 9, 487: 2, 1035: 3, 453: 11, 221: 1, 361: 3, 163: 1, 1358: 1, 507: 4, 751: 3, 426: 1, 1129: 1, 1205: 1, 552: 5, 511: 6, 472: 2, 1307: 1, 217: 1, 1259: 3, 345: 1, 745: 5, 683: 36, 368: 1, 524: 1, 341: 4, 376: 1, 1054: 4, 1053: 5, 1414: 1, 937: 1, 491: 7, 465: 7, 553: 3, 194: 2, 761: 1, 113: 1, 1220: 1, 688: 1, 1417: 2, 867: 2, 269: 1, 349: 1, 422: 2, 353: 1, 380: 1, 1474: 2, 409: 1, 329: 1, 153: 1, 252: 1, 940: 1, 173: 2, 421: 2, 1359: 2, 1260: 1, 1127: 1, 1288: 1, 326: 4, 1356: 1, 302: 1, 545: 1, 1341: 1, 254: 1, 478: 2, 14: 2, 1430: 1, 1393: 1, 216: 1, 567: 1, 147: 1, 177: 1, 195: 1, 174: 1, 1078: 1, 1274: 1, 363: 1, 247: 1, 397: 4, 1112: 1, 1360: 1, 171: 1, 1298: 1, 1317: 2, 385: 1, 654: 248, 279: 1, 953: 1, 325: 2, 1348: 1, 404: 1, 883: 1, 245: 1, 563: 1, 403: 1, 549: 1, 475: 1, 270: 1, 263: 1, 364: 1, 172: 1, 753: 1, 293: 1, 188: 1, 206: 1, 495: 1, 1385: 1, 1335: 1, 660: 74, 718: 20, 734: 3, 724: 13, 622: 4, 600: 1, 595: 1, 670: 1, 638: 1, 735: 1, 636: 1, 682: 3, 681: 1, 610: 1, 616: 1, 598: 1, 684: 1, 124: 2, 590: 1, 15: 25, 24: 1, 38: 15, 73: 4, 78: 1, 48: 7, 67: 1, 69: 4, 49: 2, 70: 1, 16: 1, 30: 4, 61: 1, 21: 1, 46: 1, 43: 1, 77: 1, 60: 1, 34: 1, 64: 1, 39: 1, 29: 1, 59: 1, 17: 2, 57: 1, 23: 3, 51: 5, 28: 2, 26: 2, 20: 2, 80: 1, 42: 1, 62: 1, 79: 1, 22: 1, 76: 1, 58: 1, 66: 1}
📊 Site 統計計算: 設備資料從第13行開始，Site 欄位在第5欄，BIN 欄位在第2欄
📊 Site 統計: 2 個 Site，詳細資料 {1: {'total': 884, 'pass': 64, 'fail': 820, 'pass_rate': 7.239819004524888, 'bins': {956: 111, 954: 4, 1038: 13, 241: 4, 1257: 22, 964: 45, 579: 38, 970: 36, 1303: 1, 1314: 12, 1: 64, 107: 1, 550: 6, 572: 1, 433: 1, 457: 3, 469: 1, 499: 1, 379: 21, 1039: 27, 749: 1, 1312: 87, 337: 13, 445: 5, 1005: 1, 143: 6, 951: 7, 361: 3, 746: 7, 507: 2, 426: 1, 1129: 1, 552: 2, 472: 1, 972: 11, 1259: 1, 345: 1, 745: 3, 368: 1, 341: 4, 376: 1, 1053: 4, 937: 1, 491: 4, 453: 4, 511: 2, 449: 3, 194: 1, 761: 1, 751: 2, 113: 1, 688: 1, 1417: 1, 867: 1, 349: 1, 1258: 7, 353: 1, 1474: 2, 940: 1, 173: 2, 421: 2, 1359: 1, 1260: 1, 1127: 1, 553: 1, 1356: 1, 465: 4, 147: 1, 195: 1, 1274: 1, 363: 1, 247: 1, 171: 1, 1298: 1, 385: 1, 654: 120, 1035: 1, 279: 1, 953: 1, 397: 3, 883: 1, 326: 1, 245: 1, 403: 1, 549: 1, 1054: 1, 475: 1, 270: 1, 263: 1, 172: 1, 487: 1, 1317: 1, 718: 6, 660: 27, 734: 2, 724: 4, 622: 1, 683: 13, 595: 1, 670: 1, 638: 1, 682: 2, 681: 1, 598: 1, 684: 1, 124: 2, 38: 8, 78: 1, 48: 4, 15: 12, 69: 2, 30: 3, 46: 1, 43: 1, 73: 2, 60: 1, 34: 1, 64: 1, 39: 1, 29: 1, 59: 1, 57: 1, 26: 2, 28: 1, 14: 1, 20: 2, 80: 1, 23: 1, 79: 1, 76: 1, 51: 1, 66: 1}}, 2: {'total': 882, 'pass': 105, 'fail': 777, 'pass_rate': 11.904761904761903, 'bins': {1: 105, 956: 98, 964: 49, 579: 29, 970: 51, 1039: 32, 449: 2, 143: 16, 438: 1, 379: 17, 972: 11, 1038: 12, 951: 11, 1257: 18, 746: 8, 273: 1, 152: 1, 337: 7, 1005: 1, 950: 1, 300: 1, 1258: 7, 373: 1, 487: 1, 1035: 2, 1314: 12, 453: 7, 221: 1, 163: 1, 1358: 1, 751: 1, 1205: 1, 511: 4, 1307: 1, 217: 1, 683: 23, 524: 1, 241: 2, 1054: 3, 1414: 1, 465: 3, 553: 2, 457: 6, 445: 4, 550: 1, 552: 3, 1220: 1, 269: 1, 422: 2, 380: 1, 409: 1, 329: 1, 472: 1, 153: 1, 252: 1, 491: 3, 1288: 1, 326: 3, 745: 2, 302: 1, 867: 1, 545: 1, 1341: 1, 254: 1, 1259: 2, 194: 1, 1417: 1, 478: 2, 14: 1, 1430: 1, 1393: 1, 216: 1, 567: 1, 177: 1, 174: 1, 1078: 1, 1303: 1, 397: 1, 1112: 1, 1360: 1, 1317: 1, 325: 2, 1348: 1, 1053: 1, 507: 2, 404: 1, 1359: 1, 563: 1, 364: 1, 753: 1, 293: 1, 188: 1, 206: 1, 495: 1, 1385: 1, 1335: 1, 654: 128, 660: 47, 718: 14, 600: 1, 724: 9, 622: 3, 735: 1, 636: 1, 734: 1, 682: 1, 610: 1, 616: 1, 954: 1, 590: 1, 15: 13, 24: 1, 73: 2, 38: 7, 67: 1, 48: 3, 49: 2, 70: 1, 16: 1, 61: 1, 21: 1, 30: 1, 77: 1, 69: 2, 17: 2, 23: 2, 51: 4, 28: 1, 42: 1, 62: 1, 22: 1, 58: 1}}}
📋 BIN Definition 對應: 1662 個 BIN 定義
✅ Summary 生成完成: 200 個 BIN, 2 個 Site
✅ 前7步驟完成
CSV 讀取成功: 1778 行 x 1666 欄
📁 生成標準Summary：YNT8726510993_F1R2_1_07_summary.csv
📊 統計: Total=1766, Pass=169, Fail=1597, Yield=9.570%
📊 Metadata: Computer=GAX07, Date=07/05/25 02:03:04
📄 結構: 206行 CSV格式
============================================================
🎉 FT Summary生成完成
📊 處理時間：5.68秒
📁 Summary檔案：/mnt/d/project/python/outlook_summary/doc/YNT8726510993/YNT8726510993_F1R2_1_07_summary.csv
============================================================

📊 進度：25/26
🔍 EQC檔案：YNT8726510993_F1R2_1_07_r1.csv
✅ EQC檔案全通過：YNT8726510993_F1R2_1_07_r1.csv
✅ EQC全通過記錄：YNT8726510993_F1R2_1_07_r1.csv

📊 進度：26/26
🔍 EQC檔案：YNT8726510993_F1GR_1_07_S22025-07-05 15.25.30.csv
📊 EQC檔案有失敗設備：YNT8726510993_F1GR_1_07_S22025-07-05 15.25.30.csv (失敗數量: 1個)
⚡ 快速EQC處理（有失敗）：YNT8726510993_F1GR_1_07_S22025-07-05 15.25.30.csv
============================================================
🎯 開始FT Summary生成（簡化版）
📁 處理檔案：YNT8726510993_F1GR_1_07_S22025-07-05 15.25.30.csv
============================================================
🔄 執行前7步驟：YNT8726510993_F1GR_1_07_S22025-07-05 15.25.30.csv
CSV 讀取成功: 14 行 x 1666 欄
執行步驟 1: CSV 結構補全
結構補全: 行7 C欄 空白 → '0.00.01'
結構補全: 行8 C欄 空白 → 'Test_Time'
結構補全: 行10 C欄 空白 → 'none'
結構補全: 行11 C欄 空白 → 'none'
✅ CSV 結構補全完成
執行步驟 2: BIN 號碼分配
BIN 分配: C6 → BIN 5
BIN 分配: D6 → BIN 6
BIN 分配: E6 → BIN 7
BIN 分配: F6 → BIN 8
BIN 分配: G6 → BIN 9
BIN 分配: H6 → BIN 10
BIN 分配: I6 → BIN 11
BIN 分配: J6 → BIN 12
BIN 分配: K6 → BIN 13
BIN 分配: L6 → BIN 14
  ... (省略中間 BIN 分配) ...
BIN 分配: Col16616 → BIN 1663
BIN 分配: Col16626 → BIN 1664
BIN 分配: Col16636 → BIN 1665
BIN 分配: Col16646 → BIN 1666
BIN 分配: Col16656 → BIN 1667
BIN 分配: Col16666 → BIN 1668
✅ BIN 號碼分配完成
執行步驟 3: BIN1 保護機制 (向量化優化版本)
🔍 發現 1 個 BIN1 設備，識別時間: 0.001 秒
📊 批量資料提取完成: 0.000 秒，1 設備 × 1664 測試項目
✅ 限值有效性檢查完成: 0.002 秒，1371 個有效測試項目
🛡️ 保護邏輯檢查完成: 0.024 秒，保護 48 個測試項目

🎉 向量化 BIN1 保護完成統計:
  ✅ 檢查 1 個 BIN1 設備
  ⚡ 總處理時間: 0.026 秒
  📊 檢查次數: 1,371 次
  🚀 處理速度: 52,299 次檢查/秒
  🛡️ 保護項目: 48 個
  📈 性能提升: 向量化優化相比逐一檢查

📝 BIN1 保護記錄已附加到: datalog.txt
✅ BIN1 保護機制完成，共 96 個位置需要紅色標記
執行步驟 4: 全設備上下限檢查與 BIN 分類 (向量化優化版本)
📊 向量化預處理階段...
  資料規模: 2 設備 × 1664 測試項目 = 3,328 次比較
  預處理完成: 0.003 秒
⚡ 向量化測試比較階段...
  向量化比較完成: 0.000 秒
🎯 向量化 BIN 分配階段...
  BIN 分配完成: 0.000 秒
📝 失敗位置記錄階段...
  位置記錄完成: 0.000 秒

🎉 向量化 BIN 分配完成統計:
  ✅ 完成 2 個設備的 BIN 分配
  ⚡ 總處理時間: 0.004 秒
  📊 比較次數: 3,328 次
  🚀 處理速度: 918,272 次比較/秒
  🔴 需要標紅色的位置: 815 個
  📈 性能提升: 向量化優化相比逐一處理
🔍 執行 Site 欄位動態查找
📝 Site 查找記錄已附加到: datalog.txt
✅ 找到 Site 欄位: E欄 (index 4) = 'Site_No'
🎯 生成 Summary 工作表: YNT8726510993_F1GR_1_07_S22025-07-05 15.25.30.csv
📊 BIN 統計計算: 設備資料從第13行開始，BIN 欄位在第2欄
📊 BIN 統計: 總設備 2 個，BIN 分佈 {123: 1, 1: 1}
📊 Site 統計計算: 設備資料從第13行開始，Site 欄位在第5欄，BIN 欄位在第2欄
📊 Site 統計: 1 個 Site，詳細資料 {2: {'total': 2, 'pass': 1, 'fail': 1, 'pass_rate': 50.0, 'bins': {123: 1, 1: 1}}}
📋 BIN Definition 對應: 1662 個 BIN 定義
✅ Summary 生成完成: 2 個 BIN, 1 個 Site
✅ 前7步驟完成
CSV 讀取成功: 14 行 x 1666 欄
📁 生成標準Summary：YNT8726510993_F1GR_1_07_S22025-07-05 15.25.30_summary.csv
📊 統計: Total=2, Pass=1, Fail=1, Yield=50.000%
📊 Metadata: Computer=GAX07, Date=07/05/25 15:24:04
📄 結構: 8行 CSV格式
============================================================
🎉 FT Summary生成完成
📊 處理時間：0.14秒
📁 Summary檔案：/mnt/d/project/python/outlook_summary/doc/YNT8726510993/YNT8726510993_F1GR_1_07_S22025-07-05 15.25.30_summary.csv
============================================================
初始化 Summary 工作表生成器
初始化 FT_Summary 專用轉換器
初始化核心 4 步驟轉換器 (BIN1 保護: 開啟)
🚀 初始化 FT_Summary_Generator (動態欄位版)
🔄 開始橫向整合Summary檔案 (動態欄位)
📊 分析 YNT8726510993_F1FC_1_07_summary.csv: 9欄 (2個Site)
📊 分析 YNT8726510993_F1FT_1_07_summary.csv: 9欄 (2個Site)
📊 分析 YNT8726510993_F1FT_1_072025-06-30 14.22.59_summary.csv: 9欄 (2個Site)
📊 分析 YNT8726510993_F1FT_1_072025-07-01 04.27.21_summary.csv: 9欄 (2個Site)
📊 分析 YNT8726510993_F1FT_1_072025-07-01 18.30.33_summary.csv: 9欄 (2個Site)
📊 分析 YNT8726510993_F1FT_1_072025-07-02 08.36.39_summary.csv: 9欄 (2個Site)
📊 分析 YNT8726510993_F1FT_1_072025-07-02 22.28.25_summary.csv: 9欄 (2個Site)
📊 分析 YNT8726510993_F1FT_1_072025-07-03 12.34.36_summary.csv: 9欄 (2個Site)
📊 分析 YNT8726510993_F1FT_1_072025-07-03 13.58.48_summary.csv: 7欄 (1個Site)
📊 分析 YNT8726510993_F1FT_1_072025-07-04 04.02.44_summary.csv: 9欄 (2個Site)
📊 分析 YNT8726510993_F1FT_1_072025-07-04 14.02.54_summary.csv: 9欄 (2個Site)
📊 分析 YNT8726510993_F1R1_1_07_summary.csv: 9欄 (2個Site)
📊 分析 YNT8726510993_F1R2_1_07_summary.csv: 9欄 (2個Site)
🗂️ 橫向布局: 總寬度=124欄
📊 TOTAL統計: Total=34506, Pass=28639, Fail=5867, Yield=82.997%
📋 收集到 244 個不同的BIN
📋 BIN排序: ['1', '956', '654', '1312', '970']... (BIN=1優先)
📊 Site總數: {1: 17208, 2: 17298}
📄 生成橫向CSV: 250行 × 124欄
✅ 動態欄位橫向整合完成: FT_SUMMARY.csv
📊 處理檔案: 13個Summary, 總寬度: 124欄
📝 轉換 FT_SUMMARY.csv 為 Excel 格式...
🚀 開始簡化版 FT_SUMMARY 轉換: /mnt/d/project/python/outlook_summary/doc/YNT8726510993/FT_SUMMARY.csv
📁 輸出路徑: /mnt/d/project/python/outlook_summary/doc/YNT8726510993/FT_SUMMARY.xlsx
CSV 讀取成功: 250 行 x 124 欄
📊 讀取成功: 250 行 x 124 欄
🧠 執行智慧轉換...
  🔄 對所有資料套用智慧轉換...
  ✅ 智慧轉換完成: 8,528 / 31,000 個儲存格已轉換
📐 分析 Definition 欄位寬度...
  📍 在第6行找到 Definition 標頭
  📍 找到 14 個 Definition 欄位: [3, 12, 21, 30, 39, 48, 57, 66, 75, 82, 91, 100, 109, 118]
  📏 最大 Definition 字串長度: 20 (最小: 10)
📝 創建 Excel 檔案...
🎨 啟用 BIN 上色功能
  📝 批量寫入資料...
  📐 設定欄位寬度...
    設定欄位 4 (Definition) 寬度: 24
    設定欄位 13 (Definition) 寬度: 24
    設定欄位 22 (Definition) 寬度: 24
    設定欄位 31 (Definition) 寬度: 24
    設定欄位 40 (Definition) 寬度: 24
    設定欄位 49 (Definition) 寬度: 24
    設定欄位 58 (Definition) 寬度: 24
    設定欄位 67 (Definition) 寬度: 24
    設定欄位 76 (Definition) 寬度: 24
    設定欄位 83 (Definition) 寬度: 24
    設定欄位 92 (Definition) 寬度: 24
    設定欄位 101 (Definition) 寬度: 24
    設定欄位 110 (Definition) 寬度: 24
    設定欄位 119 (Definition) 寬度: 24
    設定欄位 1 寬度: 6
    設定欄位 2 寬度: 7
    設定欄位 3 寬度: 9
    設定欄位 5 寬度: 6
    設定欄位 6 寬度: 6
    設定欄位 7 寬度: 6
    設定欄位 8 寬度: 6
    設定欄位 9 寬度: 6
    設定欄位 10 寬度: 6
    設定欄位 11 寬度: 7
    設定欄位 12 寬度: 9
    設定欄位 14 寬度: 6
    設定欄位 15 寬度: 6
    設定欄位 16 寬度: 6
    設定欄位 17 寬度: 6
    設定欄位 18 寬度: 6
    設定欄位 19 寬度: 6
    設定欄位 20 寬度: 7
    設定欄位 21 寬度: 9
    設定欄位 23 寬度: 6
    設定欄位 24 寬度: 6
    設定欄位 25 寬度: 6
    設定欄位 26 寬度: 6
    設定欄位 27 寬度: 6
    設定欄位 28 寬度: 6
    設定欄位 29 寬度: 7
    設定欄位 30 寬度: 9
    設定欄位 32 寬度: 6
    設定欄位 33 寬度: 6
    設定欄位 34 寬度: 6
    設定欄位 35 寬度: 6
    設定欄位 36 寬度: 6
    設定欄位 37 寬度: 6
    設定欄位 38 寬度: 7
    設定欄位 39 寬度: 9
    設定欄位 41 寬度: 6
    設定欄位 42 寬度: 6
    設定欄位 43 寬度: 6
    設定欄位 44 寬度: 6
    設定欄位 45 寬度: 6
    設定欄位 46 寬度: 6
    設定欄位 47 寬度: 7
    設定欄位 48 寬度: 9
    設定欄位 50 寬度: 6
    設定欄位 51 寬度: 6
    設定欄位 52 寬度: 6
    設定欄位 53 寬度: 6
    設定欄位 54 寬度: 6
    設定欄位 55 寬度: 6
    設定欄位 56 寬度: 7
    設定欄位 57 寬度: 9
    設定欄位 59 寬度: 6
    設定欄位 60 寬度: 6
    設定欄位 61 寬度: 6
    設定欄位 62 寬度: 6
    設定欄位 63 寬度: 6
    設定欄位 64 寬度: 6
    設定欄位 65 寬度: 7
    設定欄位 66 寬度: 9
    設定欄位 68 寬度: 6
    設定欄位 69 寬度: 6
    設定欄位 70 寬度: 6
    設定欄位 71 寬度: 6
    設定欄位 72 寬度: 6
    設定欄位 73 寬度: 6
    設定欄位 74 寬度: 6
    設定欄位 75 寬度: 9
    設定欄位 77 寬度: 6
    設定欄位 78 寬度: 6
    設定欄位 79 寬度: 6
    設定欄位 80 寬度: 6
    設定欄位 81 寬度: 7
    設定欄位 82 寬度: 9
    設定欄位 84 寬度: 6
    設定欄位 85 寬度: 6
    設定欄位 86 寬度: 6
    設定欄位 87 寬度: 6
    設定欄位 88 寬度: 6
    設定欄位 89 寬度: 6
    設定欄位 90 寬度: 7
    設定欄位 91 寬度: 9
    設定欄位 93 寬度: 6
    設定欄位 94 寬度: 6
    設定欄位 95 寬度: 6
    設定欄位 96 寬度: 6
    設定欄位 97 寬度: 6
    設定欄位 98 寬度: 6
    設定欄位 99 寬度: 7
    設定欄位 100 寬度: 9
    設定欄位 102 寬度: 6
    設定欄位 103 寬度: 6
    設定欄位 104 寬度: 6
    設定欄位 105 寬度: 6
    設定欄位 106 寬度: 6
    設定欄位 107 寬度: 6
    設定欄位 108 寬度: 6
    設定欄位 109 寬度: 9
    設定欄位 111 寬度: 6
    設定欄位 112 寬度: 6
    設定欄位 113 寬度: 6
    設定欄位 114 寬度: 6
    設定欄位 115 寬度: 6
    設定欄位 116 寬度: 6
    設定欄位 117 寬度: 7
    設定欄位 118 寬度: 9
    設定欄位 120 寬度: 6
    設定欄位 121 寬度: 6
    設定欄位 122 寬度: 6
    設定欄位 123 寬度: 6
    設定欄位 124 寬度: 6
  🎨 應用 BIN 上色...
🎨 FT Summary 上色器已初始化
🎨 開始 FT Summary 上色處理...
  📊 找到 TOTAL: 第1欄 = 348
  📊 找到 TOTAL: 第10欄 = 3480
  📊 找到 TOTAL: 第19欄 = 3480
  📊 找到 TOTAL: 第28欄 = 3480
  📊 找到 TOTAL: 第37欄 = 3480
  📊 找到 TOTAL: 第46欄 = 3478
  📊 找到 TOTAL: 第55欄 = 3480
  📊 找到 TOTAL: 第64欄 = 3480
  📊 找到 TOTAL: 第73欄 = 6
  📊 找到 TOTAL: 第80欄 = 3474
  📊 找到 TOTAL: 第89欄 = 2046
  📊 找到 TOTAL: 第98欄 = 2508
  📊 找到 TOTAL: 第107欄 = 1766
  🚫 跳過 Multiple Files 區段: 第116欄
  🎯 最大 TOTAL 區段: 第10欄，數量 = 3480
  📍 找到 Definition 欄位: 第4欄
  📍 找到 Definition 欄位: 第13欄
  📍 找到 Definition 欄位: 第22欄
  📍 找到 Definition 欄位: 第31欄
  📍 找到 Definition 欄位: 第40欄
  📍 找到 Definition 欄位: 第49欄
  📍 找到 Definition 欄位: 第58欄
  📍 找到 Definition 欄位: 第67欄
  📍 找到 Definition 欄位: 第76欄
  📍 找到 Definition 欄位: 第83欄
  📍 找到 Definition 欄位: 第92欄
  📍 找到 Definition 欄位: 第101欄
  📍 找到 Definition 欄位: 第110欄
  📍 找到 Definition 欄位: 第119欄
  📊 共找到 14 個 Definition 欄位
  🎯 最大 TOTAL 第10欄 → 最近 Definition 第13欄（距離 3 欄）
🎯 目標上色區段：最大 TOTAL 在第10欄，對應 Definition 在第13欄
  🔍 建立基於最大 TOTAL 區段的 BIN 色彩映射...
  📊 找到 TOTAL: 第1欄 = 348
  📊 找到 TOTAL: 第10欄 = 3480
  📊 找到 TOTAL: 第19欄 = 3480
  📊 找到 TOTAL: 第28欄 = 3480
  📊 找到 TOTAL: 第37欄 = 3480
  📊 找到 TOTAL: 第46欄 = 3478
  📊 找到 TOTAL: 第55欄 = 3480
  📊 找到 TOTAL: 第64欄 = 3480
  📊 找到 TOTAL: 第73欄 = 6
  📊 找到 TOTAL: 第80欄 = 3474
  📊 找到 TOTAL: 第89欄 = 2046
  📊 找到 TOTAL: 第98欄 = 2508
  📊 找到 TOTAL: 第107欄 = 1766
  🚫 跳過 Multiple Files 區段: 第116欄
  🎯 最大 TOTAL 區段: 第10欄，數量 = 3480
  🎯 最大 TOTAL 第10欄 → 最近 Definition 第13欄（距離 3 欄）
  🎯 目標 Definition 欄位: 第13欄
    🟢 BIN=1 -> All Pass (綠色)
    🎨 BIN=956 -> OVPth_VCLN1... (#FF69B4) [第1個Fail BIN]
    🎨 BIN=1312 -> DUTY_9P5_delta... (#4682B4) [第2個Fail BIN]
    🎨 BIN=970 -> VBP_PT_VCLN1... (#9370DB) [第3個Fail BIN]
    🎨 BIN=654 -> CKV6_L_OC_FT_100mA... (#DAA520) [第4個Fail BIN]
    🎨 BIN=964 -> OVPth_VCLN2... (#FF6347) [第5個Fail BIN]
    🎨 BIN=1039 -> Vcom1_ISNK... (#32CD32) [第6個Fail BIN]
    🎨 BIN=660 -> CKVB6_L_OC_FT_100mA... (#DEB887) [第7個Fail BIN]
    🎨 BIN=579 -> uvloH_VIN... (#F4A460) [第8個Fail BIN]
    🎨 BIN=379 -> BK1_VoutL... (#708090) [第9個Fail BIN]
    🎨 BIN=1314 -> Iq_vinh_sw... (#20B2AA) [第10個Fail BIN]
    ⚪ BIN=15 -> 不上色 (超過前10個Fail BIN)
    ⚪ BIN=683 -> 不上色 (超過前10個Fail BIN)
    ⚪ BIN=337 -> 不上色 (超過前10個Fail BIN)
    ⚪ BIN=23 -> 不上色 (超過前10個Fail BIN)
    ⚪ BIN=746 -> 不上色 (超過前10個Fail BIN)
    ⚪ BIN=951 -> 不上色 (超過前10個Fail BIN)
    ⚪ BIN=1257 -> 不上色 (超過前10個Fail BIN)
    ⚪ BIN=17 -> 不上色 (超過前10個Fail BIN)
    ⚪ BIN=28 -> 不上色 (超過前10個Fail BIN)
    ⚪ BIN=143 -> 不上色 (超過前10個Fail BIN)
    ⚪ BIN=453 -> 不上色 (超過前10個Fail BIN)
    ⚪ BIN=491 -> 不上色 (超過前10個Fail BIN)
    ⚪ BIN=622 -> 不上色 (超過前10個Fail BIN)
    ⚪ BIN=718 -> 不上色 (超過前10個Fail BIN)
    ⚪ BIN=724 -> 不上色 (超過前10個Fail BIN)
    ⚪ BIN=972 -> 不上色 (超過前10個Fail BIN)
    ⚪ BIN=1038 -> 不上色 (超過前10個Fail BIN)
    ⚪ BIN=1053 -> 不上色 (超過前10個Fail BIN)
    ⚪ BIN=1361 -> 不上色 (超過前10個Fail BIN)
    ⚪ BIN=14 -> 不上色 (超過前10個Fail BIN)
    ⚪ BIN=26 -> 不上色 (超過前10個Fail BIN)
    ⚪ BIN=38 -> 不上色 (超過前10個Fail BIN)
    ⚪ BIN=51 -> 不上色 (超過前10個Fail BIN)
    ⚪ BIN=124 -> 不上色 (超過前10個Fail BIN)
    ⚪ BIN=177 -> 不上色 (超過前10個Fail BIN)
    ⚪ BIN=195 -> 不上色 (超過前10個Fail BIN)
    ⚪ BIN=247 -> 不上色 (超過前10個Fail BIN)
    ⚪ BIN=292 -> 不上色 (超過前10個Fail BIN)
    ⚪ BIN=363 -> 不上色 (超過前10個Fail BIN)
    ⚪ BIN=397 -> 不上色 (超過前10個Fail BIN)
    ⚪ BIN=421 -> 不上色 (超過前10個Fail BIN)
    ⚪ BIN=445 -> 不上色 (超過前10個Fail BIN)
    ⚪ BIN=465 -> 不上色 (超過前10個Fail BIN)
    ⚪ BIN=487 -> 不上色 (超過前10個Fail BIN)
    ⚪ BIN=507 -> 不上色 (超過前10個Fail BIN)
    ⚪ BIN=511 -> 不上色 (超過前10個Fail BIN)
    ⚪ BIN=549 -> 不上色 (超過前10個Fail BIN)
    ⚪ BIN=550 -> 不上色 (超過前10個Fail BIN)
    ⚪ BIN=686 -> 不上色 (超過前10個Fail BIN)
    ⚪ BIN=745 -> 不上色 (超過前10個Fail BIN)
    ⚪ BIN=954 -> 不上色 (超過前10個Fail BIN)
    ⚪ BIN=963 -> 不上色 (超過前10個Fail BIN)
    ⚪ BIN=1054 -> 不上色 (超過前10個Fail BIN)
    ⚪ BIN=1078 -> 不上色 (超過前10個Fail BIN)
    ⚪ BIN=1115 -> 不上色 (超過前10個Fail BIN)
    ⚪ BIN=1258 -> 不上色 (超過前10個Fail BIN)
    ⚪ BIN=1274 -> 不上色 (超過前10個Fail BIN)
    ⚪ BIN=1301 -> 不上色 (超過前10個Fail BIN)
    ⚪ BIN=1303 -> 不上色 (超過前10個Fail BIN)
    ⚪ BIN=1412 -> 不上色 (超過前10個Fail BIN)
    ⚪ BIN=1417 -> 不上色 (超過前10個Fail BIN)
  📋 BIN 色彩映射建立完成，共 11 個 BIN (1個All Pass + 10個Fail BIN)
  🖌️ 開始對 Definition 欄位應用色彩...
  📍 找到 Definition 欄位: 第4欄
  📍 找到 Definition 欄位: 第13欄
  📍 找到 Definition 欄位: 第22欄
  📍 找到 Definition 欄位: 第31欄
  📍 找到 Definition 欄位: 第40欄
  📍 找到 Definition 欄位: 第49欄
  📍 找到 Definition 欄位: 第58欄
  📍 找到 Definition 欄位: 第67欄
  📍 找到 Definition 欄位: 第76欄
  📍 找到 Definition 欄位: 第83欄
  📍 找到 Definition 欄位: 第92欄
  📍 找到 Definition 欄位: 第101欄
  📍 找到 Definition 欄位: 第110欄
  📍 找到 Definition 欄位: 第119欄
  📊 共找到 14 個 Definition 欄位
    📍 Definition 欄位: [4, 13, 22, 31, 40, 49, 58, 67, 76, 83, 92, 101, 110, 119]
    🟢 第7行，第4欄: All Pass -> 綠色
    🟢 第7行，第13欄: All Pass -> 綠色
    🟢 第7行，第22欄: All Pass -> 綠色
    🟢 第7行，第31欄: All Pass -> 綠色
    🟢 第7行，第40欄: All Pass -> 綠色
    🟢 第7行，第49欄: All Pass -> 綠色
    🟢 第7行，第58欄: All Pass -> 綠色
    🟢 第7行，第67欄: All Pass -> 綠色
    🟢 第7行，第76欄: All Pass -> 綠色
    🟢 第7行，第83欄: All Pass -> 綠色
    🟢 第7行，第92欄: All Pass -> 綠色
    🟢 第7行，第101欄: All Pass -> 綠色
    🟢 第7行，第110欄: All Pass -> 綠色
    🟢 第7行，第119欄: All Pass -> 綠色
    🎨 第8行，第4欄: CKV6_L_OC_FT_100mA -> #DAA520
    🎨 第8行，第13欄: OVPth_VCLN1 -> #FF69B4
    🎨 第8行，第22欄: OVPth_VCLN1 -> #FF69B4
    🎨 第8行，第31欄: OVPth_VCLN1 -> #FF69B4
    🎨 第8行，第40欄: OVPth_VCLN1 -> #FF69B4
    🎨 第8行，第49欄: VBP_PT_VCLN1 -> #9370DB
    🎨 第8行，第58欄: OVPth_VCLN1 -> #FF69B4
    🎨 第8行，第67欄: OVPth_VCLN1 -> #FF69B4
    🎨 第8行，第83欄: VBP_PT_VCLN1 -> #9370DB
    🎨 第8行，第92欄: OVPth_VCLN1 -> #FF69B4
    🎨 第8行，第101欄: OVPth_VCLN1 -> #FF69B4
    🎨 第8行，第110欄: CKV6_L_OC_FT_100mA -> #DAA520
    🎨 第8行，第119欄: OVPth_VCLN1 -> #FF69B4
    🎨 第9行，第13欄: DUTY_9P5_delta -> #4682B4
    🎨 第9行，第22欄: DUTY_9P5_delta -> #4682B4
    🎨 第9行，第31欄: DUTY_9P5_delta -> #4682B4
    🎨 第9行，第40欄: DUTY_9P5_delta -> #4682B4
    🎨 第9行，第49欄: OVPth_VCLN1 -> #FF69B4
    🎨 第9行，第58欄: CKV6_L_OC_FT_100mA -> #DAA520
    🎨 第9行，第67欄: DUTY_9P5_delta -> #4682B4
    🎨 第9行，第83欄: DUTY_9P5_delta -> #4682B4
    🎨 第9行，第92欄: VBP_PT_VCLN1 -> #9370DB
    🎨 第9行，第101欄: CKV6_L_OC_FT_100mA -> #DAA520
    🎨 第9行，第110欄: OVPth_VCLN1 -> #FF69B4
    🎨 第9行，第119欄: CKV6_L_OC_FT_100mA -> #DAA520
    🎨 第10行，第13欄: VBP_PT_VCLN1 -> #9370DB
    🎨 第10行，第22欄: VBP_PT_VCLN1 -> #9370DB
    🎨 第10行，第31欄: CKV6_L_OC_FT_100mA -> #DAA520
    🎨 第10行，第40欄: VBP_PT_VCLN1 -> #9370DB
    🎨 第10行，第49欄: DUTY_9P5_delta -> #4682B4
    🎨 第10行，第58欄: DUTY_9P5_delta -> #4682B4
    🎨 第10行，第67欄: CKV6_L_OC_FT_100mA -> #DAA520
    🎨 第10行，第83欄: BK1_VoutL -> #708090
    🎨 第10行，第92欄: DUTY_9P5_delta -> #4682B4
    🎨 第10行，第101欄: DUTY_9P5_delta -> #4682B4
    🎨 第10行，第110欄: OVPth_VCLN2 -> #FF6347
    🎨 第10行，第119欄: DUTY_9P5_delta -> #4682B4
    🎨 第11行，第13欄: CKV6_L_OC_FT_100mA -> #DAA520
    🎨 第11行，第22欄: CKV6_L_OC_FT_100mA -> #DAA520
    🎨 第11行，第31欄: VBP_PT_VCLN1 -> #9370DB
    🎨 第11行，第40欄: CKV6_L_OC_FT_100mA -> #DAA520
    🎨 第11行，第49欄: CKV6_L_OC_FT_100mA -> #DAA520
    🎨 第11行，第58欄: VBP_PT_VCLN1 -> #9370DB
    🎨 第11行，第67欄: OVPth_VCLN2 -> #FF6347
    🎨 第11行，第83欄: OVPth_VCLN1 -> #FF69B4
    🎨 第11行，第101欄: VBP_PT_VCLN1 -> #9370DB
    🎨 第11行，第110欄: VBP_PT_VCLN1 -> #9370DB
    🎨 第11行，第119欄: VBP_PT_VCLN1 -> #9370DB
    🎨 第12行，第4欄: BK1_VoutL -> #708090
    🎨 第12行，第13欄: OVPth_VCLN2 -> #FF6347
    🎨 第12行，第22欄: OVPth_VCLN2 -> #FF6347
    🎨 第12行，第31欄: uvloH_VIN -> #F4A460
    🎨 第12行，第40欄: CKVB6_L_OC_FT_100mA -> #DEB887
    🎨 第12行，第49欄: Iq_vinh_sw -> #20B2AA
    🎨 第12行，第58欄: CKVB6_L_OC_FT_100mA -> #DEB887
    🎨 第12行，第67欄: uvloH_VIN -> #F4A460
    🎨 第12行，第83欄: CKV6_L_OC_FT_100mA -> #DAA520
    🎨 第12行，第92欄: CKV6_L_OC_FT_100mA -> #DAA520
    🎨 第12行，第101欄: OVPth_VCLN2 -> #FF6347
    🎨 第12行，第110欄: DUTY_9P5_delta -> #4682B4
    🎨 第12行，第119欄: OVPth_VCLN2 -> #FF6347
    🎨 第13行，第13欄: Vcom1_ISNK -> #32CD32
    🎨 第13行，第22欄: Iq_vinh_sw -> #20B2AA
    🎨 第13行，第31欄: OVPth_VCLN2 -> #FF6347
    🎨 第13行，第40欄: Iq_vinh_sw -> #20B2AA
    🎨 第13行，第49欄: OVPth_VCLN2 -> #FF6347
    🎨 第13行，第58欄: BK1_VoutL -> #708090
    🎨 第13行，第67欄: CKVB6_L_OC_FT_100mA -> #DEB887
    🎨 第13行，第83欄: Vcom1_ISNK -> #32CD32
    🎨 第13行，第101欄: CKVB6_L_OC_FT_100mA -> #DEB887
    🎨 第13行，第110欄: CKVB6_L_OC_FT_100mA -> #DEB887
    🎨 第13行，第119欄: CKVB6_L_OC_FT_100mA -> #DEB887
    🎨 第14行，第13欄: CKVB6_L_OC_FT_100mA -> #DEB887
    🎨 第14行，第22欄: uvloH_VIN -> #F4A460
    🎨 第14行，第31欄: Vcom1_ISNK -> #32CD32
    🎨 第14行，第40欄: OVPth_VCLN2 -> #FF6347
    🎨 第14行，第49欄: uvloH_VIN -> #F4A460
    🎨 第14行，第58欄: uvloH_VIN -> #F4A460
    🎨 第14行，第92欄: Iq_vinh_sw -> #20B2AA
    🎨 第14行，第101欄: uvloH_VIN -> #F4A460
    🎨 第14行，第110欄: uvloH_VIN -> #F4A460
    🎨 第14行，第119欄: uvloH_VIN -> #F4A460
    🎨 第15行，第13欄: uvloH_VIN -> #F4A460
    🎨 第15行，第22欄: CKVB6_L_OC_FT_100mA -> #DEB887
    🎨 第15行，第31欄: CKVB6_L_OC_FT_100mA -> #DEB887
    🎨 第15行，第40欄: Vcom1_ISNK -> #32CD32
    🎨 第15行，第58欄: OVPth_VCLN2 -> #FF6347
    🎨 第15行，第67欄: VBP_PT_VCLN1 -> #9370DB
    🎨 第15行，第101欄: Vcom1_ISNK -> #32CD32
    🎨 第15行，第110欄: Vcom1_ISNK -> #32CD32
    🎨 第15行，第119欄: Vcom1_ISNK -> #32CD32
    🎨 第16行，第13欄: BK1_VoutL -> #708090
    🎨 第16行，第22欄: Vcom1_ISNK -> #32CD32
    🎨 第16行，第31欄: BK1_VoutL -> #708090
    🎨 第16行，第40欄: uvloH_VIN -> #F4A460
    🎨 第16行，第49欄: BK1_VoutL -> #708090
    🎨 第16行，第67欄: Iq_vinh_sw -> #20B2AA
    🎨 第16行，第83欄: CKVB6_L_OC_FT_100mA -> #DEB887
    🎨 第16行，第101欄: BK1_VoutL -> #708090
    🎨 第16行，第119欄: BK1_VoutL -> #708090
    🎨 第17行，第13欄: Iq_vinh_sw -> #20B2AA
    🎨 第17行，第49欄: Vcom1_ISNK -> #32CD32
    🎨 第17行，第67欄: Vcom1_ISNK -> #32CD32
    🎨 第17行，第83欄: uvloH_VIN -> #F4A460
    🎨 第17行，第92欄: OVPth_VCLN2 -> #FF6347
    🎨 第17行，第110欄: BK1_VoutL -> #708090
    🎨 第17行，第119欄: Iq_vinh_sw -> #20B2AA
    🎨 第18行，第31欄: Iq_vinh_sw -> #20B2AA
    🎨 第18行，第92欄: Vcom1_ISNK -> #32CD32
    🎨 第19行，第4欄: OVPth_VCLN1 -> #FF69B4
    🎨 第19行，第49欄: CKVB6_L_OC_FT_100mA -> #DEB887
    🎨 第19行，第101欄: Iq_vinh_sw -> #20B2AA
    🎨 第20行，第4欄: Vcom1_ISNK -> #32CD32
    🎨 第20行，第22欄: BK1_VoutL -> #708090
    🎨 第20行，第58欄: Iq_vinh_sw -> #20B2AA
    🎨 第20行，第92欄: BK1_VoutL -> #708090
    🎨 第21行，第40欄: BK1_VoutL -> #708090
    🎨 第21行，第92欄: CKVB6_L_OC_FT_100mA -> #DEB887
    🎨 第21行，第110欄: Iq_vinh_sw -> #20B2AA
    🎨 第22行，第4欄: DUTY_9P5_delta -> #4682B42025-07-09 09:08:47.825 | INFO     | src.presentation.api.ft_eqc_api:process_ft_summary:780 - FT Summary 處理成功：/mnt/d/project/python/outlook_summary/doc/YNT8726510993/FT_SUMMARY.xlsx

    🎨 第24行，第92欄: uvloH_VIN -> #F4A460
    🎨 第33行，第83欄: Iq_vinh_sw -> #20B2AA
    🎨 第62行，第58欄: Vcom1_ISNK -> #32CD32
    🎨 第63行，第83欄: OVPth_VCLN2 -> #FF6347
    ✅ 共為 138 個 Definition 儲存格應用了色彩
✅ FT Summary 上色完成，共處理 11 個 BIN
  ✅ 帶色彩的 Excel 檔案創建完成
✅ FT_SUMMARY Excel 轉換完成: /mnt/d/project/python/outlook_summary/doc/YNT8726510993/FT_SUMMARY.xlsx
📁 檔案大小: 47,973 bytes
✅ FT_SUMMARY Excel 生成完成：FT_SUMMARY.xlsx
🎯 FT橫向整併完成: FT_SUMMARY.xlsx
初始化 Summary 工作表生成器
初始化 FT_Summary 專用轉換器
初始化核心 4 步驟轉換器 (BIN1 保護: 開啟)
🚀 初始化 FT_Summary_Generator (動態欄位版)
🔄 開始橫向整合Summary檔案 (動態欄位)
📊 分析 YNT8726510993_F1GR_1_07_S22025-07-05 15.25.30_summary.csv: 7欄 (1個Site)
🗂️ 橫向布局: 總寬度=14欄
📊 TOTAL統計: Total=2, Pass=1, Fail=1, Yield=50.000%
📋 收集到 2 個不同的BIN
📋 BIN排序: ['1', '123'] (BIN=1優先)
📊 Site總數: {1: 2}
📄 生成橫向CSV: 8行 × 14欄
✅ 動態欄位橫向整合完成: EQC_SUMMARY.csv
📊 處理檔案: 1個Summary, 總寬度: 14欄
📝 轉換 FT_SUMMARY.csv 為 Excel 格式...
🚀 開始簡化版 FT_SUMMARY 轉換: /mnt/d/project/python/outlook_summary/doc/YNT8726510993/EQC_SUMMARY.csv
📁 輸出路徑: /mnt/d/project/python/outlook_summary/doc/YNT8726510993/EQC_SUMMARY.xlsx
CSV 讀取成功: 8 行 x 14 欄
📊 讀取成功: 8 行 x 14 欄
🧠 執行智慧轉換...
  🔄 對所有資料套用智慧轉換...
  ✅ 智慧轉換完成: 28 / 112 個儲存格已轉換
📐 分析 Definition 欄位寬度...
  📍 在第6行找到 Definition 標頭
  📍 找到 2 個 Definition 欄位: [3, 10]
  📏 最大 Definition 字串長度: 14 (最小: 10)
📝 創建 Excel 檔案...
🎨 啟用 BIN 上色功能
  📝 批量寫入資料...
  📐 設定欄位寬度...
    設定欄位 4 (Definition) 寬度: 18
    設定欄位 11 (Definition) 寬度: 18
    設定欄位 1 寬度: 6
    設定欄位 2 寬度: 7
    設定欄位 3 寬度: 9
    設定欄位 5 寬度: 6
    設定欄位 6 寬度: 6
    設定欄位 7 寬度: 6
    設定欄位 8 寬度: 6
    設定欄位 9 寬度: 7
    設定欄位 10 寬度: 9
    設定欄位 12 寬度: 6
    設定欄位 13 寬度: 6
    設定欄位 14 寬度: 6
  🎨 應用 BIN 上色...
🎨 FT Summary 上色器已初始化
🎨 開始 FT Summary 上色處理...
  📊 找到 TOTAL: 第1欄 = 2
  🚫 跳過 Multiple Files 區段: 第8欄
  🎯 最大 TOTAL 區段: 第1欄，數量 = 2
  📍 找到 Definition 欄位: 第4欄
  📍 找到 Definition 欄位: 第11欄
  📊 共找到 2 個 Definition 欄位
  🎯 最大 TOTAL 第1欄 → 最近 Definition 第4欄（距離 3 欄）
🎯 目標上色區段：最大 TOTAL 在第1欄，對應 Definition 在第4欄
  🔍 建立基於最大 TOTAL 區段的 BIN 色彩映射...
  📊 找到 TOTAL: 第1欄 = 2
  🚫 跳過 Multiple Files 區段: 第8欄
  🎯 最大 TOTAL 區段: 第1欄，數量 = 2
  🎯 最大 TOTAL 第1欄 → 最近 Definition 第4欄（距離 3 欄）
  🎯 目標 Definition 欄位: 第4欄
    🟢 BIN=1 -> All Pass (綠色)
    🎨 BIN=123 -> xvi_CKVB5_53FR... (#FF69B4) [第1個Fail BIN]
  📋 BIN 色彩映射建立完成，共 2 個 BIN (1個All Pass + 1個Fail BIN)
  🖌️ 開始對 Definition 欄位應用色彩...
  📍 找到 Definition 欄位: 第4欄
  📍 找到 Definition 欄位: 第11欄
  📊 共找到 2 個 Definition 欄位
    📍 Definition 欄位: [4, 11]
    🟢 第7行，第4欄: All Pass -> 綠色
    🟢 第7行，第11欄: All Pass -> 綠色
    🎨 第8行，第4欄: xvi_CKVB5_53FR -> #FF69B4
    🎨 第8行，第11欄: xvi_CKVB5_53FR -> #FF69B4
    ✅ 共為 4 個 Definition 儲存格應用了色彩
✅ FT Summary 上色完成，共處理 2 個 BIN
  ✅ 帶色彩的 Excel 檔案創建完成
✅ FT_SUMMARY Excel 轉換完成: /mnt/d/project/python/outlook_summary/doc/YNT8726510993/EQC_SUMMARY.xlsx
📁 檔案大小: 5,974 bytes
✅ FT_SUMMARY Excel 生成完成：EQC_SUMMARY.xlsx
🎯 EQC橫向整併完成: EQC_SUMMARY.xlsx
📄 FT Excel檔案已生成: FT_SUMMARY.xlsx
📄 EQC Excel檔案已生成: EQC_SUMMARY.xlsx

=== 反假測試檢查：記錄執行後狀態 ===
⏰ 結束時間：2025-07-09 09:08:47
📊 處理結果：成功14個，跳過12個，失敗0個
🎯 FT Summary檔案：13個
🔍 EQC Summary檔案：1個
✅ EQC全通過檔案：12個
⏱️ 總處理時間：101.83秒
📊 FT最終整併：FT_SUMMARY.xlsx + .xlsx
📊 EQC最終整併：EQC_SUMMARY.xlsx + .xlsx
INFO:     127.0.0.1:61204 - "POST /api/process_ft_summary HTTP/1.1" 200 OK
INFO:     Shutting down
INFO:     Waiting for application shutdown.
2025-07-09 09:26:53.158 | INFO     | src.presentation.api.ft_eqc_api:shutdown_event:949 - 🛑 FT-EQC API 服務正在關閉...
2025-07-09 09:26:53.159 | INFO     | src.presentation.api.services.file_cleanup_scheduler:stop_scheduler:135 - ⏹️ 檔案清理調度器已停止
2025-07-09 09:26:53.159 | INFO     | src.presentation.api.ft_eqc_api:shutdown_event:956 - ✅ 背景清理調度器已優雅關閉
2025-07-09 09:26:53.159 | INFO     | src.presentation.api.ft_eqc_api:shutdown_event:962 - ✅ 服務關閉完成
INFO:     Application shutdown complete.
INFO:     Finished server process [11171]
