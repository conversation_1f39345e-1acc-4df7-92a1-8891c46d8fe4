#!/usr/bin/env python3
"""
詳細分析 temp 目錄無法產生 EQCTOTALDATA.csv 的原因
捕獲完整 LOG 並逐步分析
"""

import os
import sys
import io
from contextlib import redirect_stdout, redirect_stderr

# 添加路徑
sys.path.append('src')

def capture_eqc_processing_logs():
    """捕獲 EQC 處理的完整日誌"""
    print("🔍 捕獲 EQC 處理完整日誌...")
    
    temp_path = "/mnt/d/project/python/outlook_summary/temp/extracted/extracted_27a0d31eb8cc411db95d7db419ade28d/20250523"
    
    try:
        from src.infrastructure.adapters.excel.eqc.eqc_bin1_final_processor import EQCBin1FinalProcessor
        
        processor = EQCBin1FinalProcessor()
        
        # 捕獲標準輸出和錯誤輸出
        stdout_capture = io.StringIO()
        stderr_capture = io.StringIO()
        
        print(f"   📁 處理路徑: {temp_path}")
        print(f"   🚀 開始執行 process_complete_eqc_integration...")
        print()
        
        with redirect_stdout(stdout_capture), redirect_stderr(stderr_capture):
            try:
                result = processor.process_complete_eqc_integration(temp_path)
            except Exception as e:
                print(f"處理過程中發生異常: {e}", file=stderr_capture)
                result = None
        
        # 獲取捕獲的輸出
        stdout_output = stdout_capture.getvalue()
        stderr_output = stderr_capture.getvalue()
        
        print("=" * 80)
        print("📋 完整處理日誌:")
        print("=" * 80)
        print(stdout_output)
        
        if stderr_output:
            print("=" * 80)
            print("❌ 錯誤輸出:")
            print("=" * 80)
            print(stderr_output)
        
        print("=" * 80)
        print("📊 處理結果分析:")
        print("=" * 80)
        
        if result and len(result) == 2:
            total_file, raw_file = result
            if total_file and raw_file:
                print(f"✅ 處理成功:")
                print(f"   📄 生成: {os.path.basename(total_file)}")
                print(f"   📄 生成: {os.path.basename(raw_file)}")
                
                # 檢查檔案
                if os.path.exists(total_file):
                    size = os.path.getsize(total_file)
                    print(f"   📊 檔案大小: {size} bytes")
                    
                    # 檢查檔案內容
                    with open(total_file, 'r', encoding='utf-8') as f:
                        lines = f.readlines()
                        print(f"   📄 檔案行數: {len(lines)}")
                        if len(lines) > 0:
                            print(f"   📝 第一行: {lines[0].strip()}")
                
                return True
            else:
                print(f"❌ 處理返回 None 值:")
                print(f"   total_file: {total_file}")
                print(f"   raw_file: {raw_file}")
                return False
        else:
            print(f"❌ 處理失敗或返回格式錯誤:")
            print(f"   result: {result}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 捕獲日誌失敗: {e}")
        import traceback
        print(f"🐛 錯誤詳情: {traceback.format_exc()}")
        return False

def analyze_csv_discovery():
    """分析 CSV 檔案發現階段"""
    print("\n🔍 分析 CSV 檔案發現階段...")
    
    temp_path = "/mnt/d/project/python/outlook_summary/temp/extracted/extracted_27a0d31eb8cc411db95d7db419ade28d/20250523"
    
    try:
        from src.infrastructure.adapters.excel.ft_eqc_grouping_processor import CSVFileDiscovery
        
        discovery = CSVFileDiscovery()
        
        print("   🔄 執行檔案發現...")
        csv_files = discovery.find_all_csv_files(temp_path)
        
        print(f"   📊 找到 {len(csv_files)} 個 CSV 檔案")
        
        if csv_files:
            print("   📄 檔案列表:")
            for i, csv_file in enumerate(csv_files, 1):
                rel_path = os.path.relpath(csv_file, temp_path)
                print(f"      {i:2d}. {rel_path}")
        
        # 測試分類
        print("\n   🔄 執行檔案分類...")
        eqc_files = discovery.classify_eqc_files(csv_files)
        ft_files = discovery.classify_ft_files(csv_files)
        
        print(f"   📊 EQC 檔案: {len(eqc_files)} 個")
        print(f"   📊 FT 檔案: {len(ft_files)} 個")
        
        if eqc_files:
            print("   📄 EQC 檔案:")
            for i, eqc_file in enumerate(eqc_files, 1):
                print(f"      {i}. {os.path.basename(eqc_file)}")
        
        if ft_files:
            print("   📄 FT 檔案:")
            for i, ft_file in enumerate(ft_files, 1):
                print(f"      {i}. {os.path.basename(ft_file)}")
        
        return len(csv_files) > 0
        
    except Exception as e:
        print(f"   ❌ CSV 檔案發現分析失敗: {e}")
        return False

def analyze_eqc_bin1_search():
    """分析 EQC BIN=1 搜尋階段"""
    print("\n🔍 分析 EQC BIN=1 搜尋階段...")
    
    temp_path = "/mnt/d/project/python/outlook_summary/temp/extracted/extracted_27a0d31eb8cc411db95d7db419ade28d/20250523"
    
    try:
        from src.infrastructure.adapters.excel.ft_eqc_grouping_processor import CSVFileDiscovery
        from src.infrastructure.adapters.excel.eqc.eqc_bin1_final_processor import EQCBin1FinalProcessor
        
        discovery = CSVFileDiscovery()
        processor = EQCBin1FinalProcessor()
        
        # 獲取 EQC 檔案
        csv_files = discovery.find_all_csv_files(temp_path)
        eqc_files = discovery.classify_eqc_files(csv_files)
        
        print(f"   📊 待檢查的 EQC 檔案: {len(eqc_files)} 個")
        
        # 檢查每個 EQC 檔案
        valid_eqc_files = []
        for i, eqc_file in enumerate(eqc_files, 1):
            filename = os.path.basename(eqc_file)
            is_valid = processor.check_eqc_csv_file(eqc_file)
            print(f"   {i:2d}. {filename}")
            print(f"       檢查結果: {'✅ 有效' if is_valid else '❌ 無效'}")
            
            if is_valid:
                valid_eqc_files.append(eqc_file)
                
                # 檢查是否包含 BIN=1 資料
                try:
                    with open(eqc_file, 'r', encoding='utf-8') as f:
                        lines = f.readlines()
                    
                    bin1_found = False
                    data_lines = 0
                    for line_num, line in enumerate(lines[12:], 13):  # 從第13行開始
                        if not line.strip():
                            break
                        data_lines += 1
                        elements = line.split(',')
                        if len(elements) > 1:
                            try:
                                if int(elements[1]) == 1:
                                    bin1_found = True
                                    print(f"       🎯 BIN=1 找到於第 {line_num} 行")
                                    break
                            except (ValueError, IndexError):
                                continue
                    
                    print(f"       📊 資料行數: {data_lines}")
                    if not bin1_found:
                        print(f"       ❌ 未找到 BIN=1 資料")
                
                except Exception as e:
                    print(f"       ❌ 檔案讀取失敗: {e}")
        
        print(f"\n   📊 有效 EQC 檔案總數: {len(valid_eqc_files)}")
        
        # 嘗試找 BIN=1 資料
        print("\n   🔄 執行 find_online_eqc_bin1_datalog...")
        bin1_content = processor.find_online_eqc_bin1_datalog(valid_eqc_files)
        
        if bin1_content:
            print(f"   ✅ 找到 BIN=1 資料，長度: {len(bin1_content)} 字元")
            lines = bin1_content.split('\n')
            print(f"   📄 內容行數: {len(lines)}")
            if len(lines) > 0:
                print(f"   📝 第一行: {lines[0]}")
        else:
            print(f"   ❌ 未找到 BIN=1 資料")
        
        return bool(bin1_content)
        
    except Exception as e:
        print(f"   ❌ EQC BIN=1 搜尋分析失敗: {e}")
        import traceback
        print(f"   🐛 錯誤詳情: {traceback.format_exc()}")
        return False

def main():
    """主要分析流程"""
    print("🎯 詳細分析 temp 目錄 EQCTOTALDATA.csv 生成失敗原因")
    print("=" * 80)
    
    analyses = [
        ("CSV 檔案發現分析", analyze_csv_discovery),
        ("EQC BIN=1 搜尋分析", analyze_eqc_bin1_search),
        ("完整處理日誌捕獲", capture_eqc_processing_logs)
    ]
    
    results = []
    for analysis_name, analysis_func in analyses:
        print(f"\n{'='*20} {analysis_name} {'='*20}")
        try:
            result = analysis_func()
            results.append((analysis_name, result))
        except Exception as e:
            print(f"❌ {analysis_name} 失敗: {e}")
            results.append((analysis_name, False))
    
    # 總結
    print("\n" + "=" * 80)
    print("🎯 分析結果總結:")
    print("=" * 80)
    for analysis_name, result in results:
        status = "✅ 正常" if result else "❌ 異常"
        print(f"   {status} {analysis_name}")

if __name__ == "__main__":
    main()