"""
暫存檔案管理模組

提供暫存目錄自動創建、檔案清理、權限檢查等功能。
"""

import os
import shutil
import uuid
import logging
from pathlib import Path
from datetime import datetime, timedelta
from typing import Optional, List
from .upload_config import UploadConfig, upload_config


logger = logging.getLogger(__name__)


class TempFileManager:
    """暫存檔案管理器"""
    
    def __init__(self, config: Optional[UploadConfig] = None):
        """
        初始化暫存檔案管理器
        
        Args:
            config: 上傳配置，若為 None 則使用預設配置
        """
        self.config = config or upload_config
        self._ensure_directories_exist()
    
    def _ensure_directories_exist(self) -> None:
        """確保暫存目錄存在，不存在則自動創建"""
        directories = [
            self.config.upload_temp_dir,
            self.config.extract_temp_dir
        ]
        
        for directory in directories:
            try:
                directory.mkdir(parents=True, exist_ok=True)
                logger.info(f"暫存目錄已確保存在: {directory}")
                
                # 檢查目錄權限
                if not os.access(directory, os.W_OK):
                    logger.warning(f"暫存目錄無寫入權限: {directory}")
                    
            except Exception as e:
                logger.error(f"無法創建暫存目錄 {directory}: {e}")
                raise
    
    def create_unique_upload_path(self, original_filename: str) -> Path:
        """
        創建唯一的上傳檔案路徑
        
        Args:
            original_filename: 原始檔案名稱
            
        Returns:
            Path: 唯一的檔案路徑
        """
        # 生成唯一的檔案名稱
        file_extension = Path(original_filename).suffix
        unique_name = f"{uuid.uuid4().hex}{file_extension}"
        
        return self.config.upload_temp_dir / unique_name
    
    def create_unique_extract_dir(self) -> Path:
        """
        創建唯一的解壓縮目錄
        
        Returns:
            Path: 唯一的解壓縮目錄路徑
        """
        unique_dir = f"extracted_{uuid.uuid4().hex}"
        extract_path = self.config.extract_temp_dir / unique_dir
        
        extract_path.mkdir(parents=True, exist_ok=True)
        logger.info(f"已創建解壓縮目錄: {extract_path}")
        
        return extract_path
    
    def cleanup_old_files(self) -> None:
        """清理過期的暫存檔案"""
        cutoff_time = datetime.now() - timedelta(hours=self.config.auto_cleanup_hours)
        
        directories_to_clean = [
            self.config.upload_temp_dir,
            self.config.extract_temp_dir
        ]
        
        for directory in directories_to_clean:
            if not directory.exists():
                continue
                
            try:
                self._cleanup_directory(directory, cutoff_time)
            except Exception as e:
                logger.error(f"清理目錄時發生錯誤 {directory}: {e}")
    
    def _cleanup_directory(self, directory: Path, cutoff_time: datetime) -> None:
        """
        清理指定目錄中的過期檔案
        
        Args:
            directory: 要清理的目錄
            cutoff_time: 刪除時間閾值
        """
        removed_count = 0
        
        for item in directory.iterdir():
            try:
                # 取得檔案修改時間
                file_mtime = datetime.fromtimestamp(item.stat().st_mtime)
                
                if file_mtime < cutoff_time:
                    if item.is_file():
                        item.unlink()
                        removed_count += 1
                        logger.debug(f"已刪除過期檔案: {item}")
                    elif item.is_dir():
                        shutil.rmtree(item)
                        removed_count += 1
                        logger.debug(f"已刪除過期目錄: {item}")
                        
            except Exception as e:
                logger.warning(f"無法刪除檔案 {item}: {e}")
        
        if removed_count > 0:
            logger.info(f"已清理 {directory} 中的 {removed_count} 個過期項目")
    
    def remove_file_or_dir(self, path: Path) -> bool:
        """
        移除指定的檔案或目錄（包含重試機制）
        
        Args:
            path: 要移除的路徑
            
        Returns:
            bool: 移除是否成功
        """
        return self.remove_file_or_dir_with_retry(path, max_retries=3, delay_seconds=1)
    
    def remove_file_or_dir_with_retry(self, path: Path, max_retries: int = 3, delay_seconds: float = 1.0) -> bool:
        """
        移除指定的檔案或目錄（強化版本，包含延遲和重試機制）
        
        Args:
            path: 要移除的路徑
            max_retries: 最大重試次數
            delay_seconds: 每次重試之間的延遲秒數
            
        Returns:
            bool: 移除是否成功
        """
        import time
        
        for attempt in range(max_retries + 1):
            try:
                if not path.exists():
                    logger.debug(f"路徑已不存在: {path}")
                    return True
                
                if path.is_file():
                    path.unlink()
                    logger.debug(f"已刪除檔案: {path}")
                    return True
                elif path.is_dir():
                    shutil.rmtree(path)
                    logger.debug(f"已刪除目錄: {path}")
                    return True
                else:
                    logger.warning(f"未知的路徑類型: {path}")
                    return False
                    
            except PermissionError as e:
                if attempt < max_retries:
                    logger.warning(f"權限錯誤，等待 {delay_seconds} 秒後重試 (第 {attempt + 1}/{max_retries} 次): {e}")
                    time.sleep(delay_seconds)
                    continue
                else:
                    logger.error(f"權限錯誤，重試 {max_retries} 次後仍失敗: {e}")
                    return False
            
            except OSError as e:
                if attempt < max_retries and ("directory not empty" in str(e).lower() or 
                                            "being used by another process" in str(e).lower()):
                    logger.warning(f"檔案被占用，等待 {delay_seconds} 秒後重試 (第 {attempt + 1}/{max_retries} 次): {e}")
                    time.sleep(delay_seconds)
                    continue
                else:
                    logger.error(f"系統錯誤，無法刪除 {path}: {e}")
                    return False
            
            except Exception as e:
                logger.error(f"未預期錯誤，無法刪除 {path}: {e}")
                return False
        
        logger.error(f"重試 {max_retries} 次後仍無法刪除: {path}")
        return False
    
    def get_directory_size(self, directory: Path) -> int:
        """
        計算目錄總大小（位元組）
        
        Args:
            directory: 目錄路徑
            
        Returns:
            int: 目錄大小（位元組）
        """
        total_size = 0
        
        try:
            for item in directory.rglob('*'):
                if item.is_file():
                    total_size += item.stat().st_size
        except Exception as e:
            logger.error(f"計算目錄大小時發生錯誤 {directory}: {e}")
            
        return total_size
    
    def list_temp_files(self) -> List[dict]:
        """
        列出所有暫存檔案的資訊
        
        Returns:
            List[dict]: 暫存檔案資訊列表
        """
        files_info = []
        
        directories = [
            ("上傳目錄", self.config.upload_temp_dir),
            ("解壓縮目錄", self.config.extract_temp_dir)
        ]
        
        for dir_type, directory in directories:
            if not directory.exists():
                continue
                
            try:
                for item in directory.iterdir():
                    files_info.append({
                        'type': dir_type,
                        'path': str(item),
                        'name': item.name,
                        'size': item.stat().st_size if item.is_file() else self.get_directory_size(item),
                        'modified': datetime.fromtimestamp(item.stat().st_mtime).isoformat(),
                        'is_directory': item.is_dir()
                    })
            except Exception as e:
                logger.error(f"列出檔案時發生錯誤 {directory}: {e}")
        
        return files_info
    
    def check_disk_space(self, required_space_mb: int = 100) -> bool:
        """
        檢查暫存目錄是否有足夠的磁碟空間
        
        Args:
            required_space_mb: 需要的空間（MB）
            
        Returns:
            bool: 是否有足夠空間
        """
        try:
            stat = shutil.disk_usage(self.config.upload_temp_dir)
            free_space_mb = stat.free / (1024 * 1024)
            
            if free_space_mb < required_space_mb:
                logger.warning(f"磁碟空間不足: 可用 {free_space_mb:.1f}MB，需要 {required_space_mb}MB")
                return False
                
            return True
            
        except Exception as e:
            logger.error(f"檢查磁碟空間時發生錯誤: {e}")
            return False