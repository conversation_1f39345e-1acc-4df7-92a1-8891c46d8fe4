#!/bin/bash
"""
整合API服務啟動腳本
功能替換原則：獨立啟動模組，不影響現有功能
使用方法：./start_integrated_apis.sh
"""

echo "🚀 整合API服務啟動器"
echo "功能替換原則：同時啟動主API(8010)和網路瀏覽器API(8009)"
echo "="*60

# 檢查虛擬環境
if [ ! -d "venv" ]; then
    echo "❌ 未找到虛擬環境，請先執行: python -m venv venv"
    exit 1
fi

# 啟動虛擬環境
echo "🔧 啟動虛擬環境..."
source venv/bin/activate

# 檢查 Python 版本
echo "🐍 Python 版本: $(python --version)"
echo "📍 Python 路徑: $(which python)"

# 檢查必要套件
echo "📦 檢查必要套件..."
python -c "import fastapi, uvicorn, loguru; print('✅ 所有必要套件已安裝')" || {
    echo "❌ 缺少必要套件，請執行: pip install -r requirements.txt"
    exit 1
}

# 啟動整合服務
echo "🚀 啟動整合API服務..."
echo "主API服務: http://localhost:8010"
echo "網路瀏覽器API: http://localhost:8009"
echo ""
echo "按 Ctrl+C 停止所有服務"
echo "="*60

# 執行整合器
python api_integration.py