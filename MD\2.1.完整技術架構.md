# 包含資料庫與統計分析的完整架構設計

## 📋 目錄

1. [架構概覽](#架構概覽)
2. [資料庫設計](#資料庫設計)
3. [統計分析功能](#統計分析功能)
4. [新增組件說明](#新增組件說明)
5. [技術棧更新](#技術棧更新)
6. [開發指南](#開發指南)

## 架構概覽

### 🏗️ 完整的六角架構 + 資料層

```
outlook-email-processor/
├── src/
│   ├── domain/                    # 核心領域
│   │   ├── entities/             # 領域實體
│   │   │   ├── email.py          # Email 實體
│   │   │   ├── vendor.py         # Vendor 實體
│   │   │   ├── processing_result.py
│   │   │   ├── statistics.py     # 統計實體 ✨
│   │   │   └── report.py         # 報表實體
│   │   ├── value_objects/        # 值物件
│   │   │   ├── mo_number.py      # MO 編號
│   │   │   ├── lot_number.py     # LOT 編號
│   │   │   └── yield_value.py    # 良率值
│   │   └── services/             # 領域服務
│   │       ├── parser_service.py
│   │       ├── statistics_service.py  # 統計服務 ✨
│   │       └── notification_service.py # 通知服務 ✨
│   │
│   ├── application/              # 應用層
│   │   ├── use_cases/            # 使用案例
│   │   │   ├── process_email.py
│   │   │   ├── generate_report.py
│   │   │   ├── analyze_statistics.py  # 統計分析 ✨
│   │   │   ├── export_data.py    # 資料匯出 ✨
│   │   │   ├── monitor_system.py # 系統監控 ✨
│   │   │   └── manage_vendors.py # 廠商管理 ✨
│   │   └── interfaces/           # 通訊埠定義
│   │       ├── email_reader.py
│   │       ├── file_processor.py
│   │       ├── database_port.py   # 資料庫介面 ✨
│   │       ├── analytics_port.py  # 分析介面 ✨
│   │       ├── notification_port.py # 通知介面 ✨
│   │       └── cache_port.py     # 快取介面 ✨
│   │
│   ├── infrastructure/           # 基礎設施層
│   │   ├── adapters/             # 適配器實作
│   │   │   ├── outlook/          # Outlook 整合
│   │   │   │   ├── outlook_client.py
│   │   │   │   └── email_monitor.py
│   │   │   ├── filesystem/       # 檔案系統
│   │   │   │   ├── file_manager.py
│   │   │   │   └── network_storage.py
│   │   │   ├── excel/            # Excel 處理
│   │   │   │   ├── excel_generator.py
│   │   │   │   └── chart_creator.py
│   │   │   ├── database/         # 資料庫適配器 ✨
│   │   │   │   ├── postgresql_adapter.py
│   │   │   │   ├── sqlite_adapter.py
│   │   │   │   ├── repositories/
│   │   │   │   │   ├── email_repository.py
│   │   │   │   │   ├── statistics_repository.py
│   │   │   │   │   └── vendor_repository.py
│   │   │   │   └── migrations/
│   │   │   ├── analytics/        # 分析適配器 ✨
│   │   │   │   ├── pandas_analytics.py
│   │   │   │   ├── plotly_charts.py
│   │   │   │   └── time_series_analyzer.py
│   │   │   ├── cache/            # 快取適配器 ✨
│   │   │   │   ├── redis_cache.py
│   │   │   │   └── memory_cache.py
│   │   │   └── notification/     # 通知適配器 ✨
│   │   │       ├── email_notifier.py
│   │   │       ├── teams_notifier.py
│   │   │       └── line_notifier.py
│   │   ├── parsers/              # 廠商解析器
│   │   │   ├── base.py
│   │   │   ├── gtk.py
│   │   │   ├── etd.py
│   │   │   └── factory.py
│   │   └── config/               # 配置管理
│   │       ├── settings.py
│   │       ├── database.yaml
│   │       └── vendors.yaml
│   │
│   └── presentation/             # 展示層
│       ├── api/                  # REST API
│       │   ├── email_routes.py
│       │   ├── statistics_routes.py  # 統計 API ✨
│       │   ├── dashboard_routes.py   # 儀表板 API ✨
│       │   ├── vendor_routes.py      # 廠商管理 API ✨
│       │   └── export_routes.py      # 匯出 API ✨
│       ├── cli/                  # 命令列介面
│       │   ├── email_commands.py
│       │   ├── stats_commands.py     # 統計命令 ✨
│       │   └── maintenance_commands.py # 維護命令 ✨
│       └── web/                  # Web UI
│           ├── templates/
│           │   ├── dashboard.html    # 統計儀表板 ✨
│           │   ├── reports.html      # 報表頁面 ✨
│           │   ├── vendors.html      # 廠商管理 ✨
│           │   └── monitoring.html   # 系統監控 ✨
│           └── static/
│               ├── js/
│               │   ├── dashboard.js  # 儀表板 JS ✨
│               │   └── charts.js     # 圖表 JS ✨
│               └── css/
│
├── database/                     # 資料庫相關 ✨
│   ├── migrations/              # 資料庫遷移
│   │   ├── 001_create_emails.sql
│   │   ├── 002_create_parsing_results.sql
│   │   ├── 003_create_statistics.sql
│   │   └── 004_create_performance_metrics.sql
│   ├── seeds/                   # 測試資料
│   │   ├── sample_emails.sql
│   │   └── sample_vendors.sql
│   ├── views/                   # 資料庫視圖
│   │   ├── vendor_summary.sql
│   │   └── daily_statistics.sql
│   └── schema.sql               # 完整資料庫結構
│
├── analytics/                   # 分析相關 ✨
│   ├── notebooks/               # Jupyter 分析筆記本
│   │   ├── vendor_analysis.ipynb
│   │   ├── yield_trends.ipynb
│   │   └── performance_monitoring.ipynb
│   ├── reports/                 # 自動生成報表
│   │   ├── templates/
│   │   └── generated/
│   ├── dashboards/              # 儀表板設定
│   │   ├── real_time_dashboard.json
│   │   └── executive_summary.json
│   └── scripts/                 # 分析腳本
│       ├── daily_report.py
│       └── anomaly_detection.py
│
├── monitoring/                  # 監控相關 ✨
│   ├── prometheus/              # Prometheus 設定
│   ├── grafana/                 # Grafana 儀表板
│   └── alerts/                  # 警示規則
│
└── docker/                     # Docker 相關
    ├── Dockerfile
    ├── docker-compose.yml       # 包含資料庫服務
    └── init-db/                 # 資料庫初始化腳本
```

## 資料庫設計

### 🗄️ 核心資料表結構

```sql
-- 郵件記錄表
CREATE TABLE emails (
    id SERIAL PRIMARY KEY,
    received_date TIMESTAMP NOT NULL,
    vendor VARCHAR(50) NOT NULL,
    sender_email VARCHAR(255),
    recipient_email VARCHAR(255),
    subject TEXT,
    body TEXT,
    processed_at TIMESTAMP,
    status VARCHAR(20) DEFAULT 'pending',
    error_message TEXT,
    retry_count INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- 廠商資訊表
CREATE TABLE vendors (
    id SERIAL PRIMARY KEY,
    code VARCHAR(20) UNIQUE NOT NULL,
    name VARCHAR(100) NOT NULL,
    email_patterns TEXT[],
    parser_config JSONB,
    active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- 解析結果表
CREATE TABLE parsing_results (
    id SERIAL PRIMARY KEY,
    email_id INTEGER REFERENCES emails(id),
    vendor_id INTEGER REFERENCES vendors(id),
    product VARCHAR(100),
    mo_number VARCHAR(100),
    lot_number VARCHAR(100),
    yield_value DECIMAL(5,2),
    in_qty INTEGER,
    processing_status VARCHAR(50),
    parsing_duration INTERVAL,
    confidence_score DECIMAL(3,2),
    extracted_data JSONB,
    created_at TIMESTAMP DEFAULT NOW()
);

-- 檔案處理記錄表
CREATE TABLE file_processing (
    id SERIAL PRIMARY KEY,
    email_id INTEGER REFERENCES emails(id),
    file_name VARCHAR(255),
    file_type VARCHAR(50),
    file_size INTEGER,
    processing_duration INTERVAL,
    summary_generated BOOLEAN DEFAULT FALSE,
    network_path TEXT,
    checksum VARCHAR(64),
    metadata JSONB,
    created_at TIMESTAMP DEFAULT NOW()
);

-- 每日統計表
CREATE TABLE daily_statistics (
    id SERIAL PRIMARY KEY,
    date DATE NOT NULL,
    vendor_id INTEGER REFERENCES vendors(id),
    total_emails INTEGER DEFAULT 0,
    processed_emails INTEGER DEFAULT 0,
    failed_emails INTEGER DEFAULT 0,
    avg_yield DECIMAL(5,2),
    total_qty INTEGER DEFAULT 0,
    avg_processing_time INTERVAL,
    created_at TIMESTAMP DEFAULT NOW(),
    UNIQUE(date, vendor_id)
);

-- 效能指標表
CREATE TABLE performance_metrics (
    id SERIAL PRIMARY KEY,
    metric_name VARCHAR(100) NOT NULL,
    metric_value DECIMAL(10,2),
    unit VARCHAR(20),
    tags JSONB,
    timestamp TIMESTAMP DEFAULT NOW()
);

-- 警示記錄表
CREATE TABLE alerts (
    id SERIAL PRIMARY KEY,
    alert_type VARCHAR(50) NOT NULL,
    severity VARCHAR(20) NOT NULL,
    title VARCHAR(255) NOT NULL,
    message TEXT,
    source_type VARCHAR(50),
    source_id INTEGER,
    acknowledged BOOLEAN DEFAULT FALSE,
    acknowledged_by VARCHAR(100),
    acknowledged_at TIMESTAMP,
    resolved BOOLEAN DEFAULT FALSE,
    resolved_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT NOW()
);

-- 系統設定表
CREATE TABLE system_settings (
    id SERIAL PRIMARY KEY,
    setting_key VARCHAR(100) UNIQUE NOT NULL,
    setting_value TEXT,
    setting_type VARCHAR(20) DEFAULT 'string',
    description TEXT,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);
```

### 📊 資料庫視圖

```sql
-- 廠商摘要視圖
CREATE VIEW vendor_summary AS
SELECT 
    v.code,
    v.name,
    COUNT(e.id) as total_emails,
    COUNT(CASE WHEN e.status = 'completed' THEN 1 END) as processed_emails,
    COUNT(CASE WHEN e.status = 'failed' THEN 1 END) as failed_emails,
    AVG(pr.yield_value) as avg_yield,
    SUM(pr.in_qty) as total_qty,
    AVG(EXTRACT(EPOCH FROM pr.parsing_duration)) as avg_processing_seconds
FROM vendors v
LEFT JOIN emails e ON e.vendor = v.code
LEFT JOIN parsing_results pr ON pr.email_id = e.id
GROUP BY v.id, v.code, v.name;

-- 每日效能視圖
CREATE VIEW daily_performance AS
SELECT 
    DATE(e.received_date) as date,
    e.vendor,
    COUNT(*) as total_emails,
    AVG(pr.yield_value) as avg_yield,
    AVG(EXTRACT(EPOCH FROM pr.parsing_duration)) as avg_processing_time,
    COUNT(CASE WHEN e.status = 'failed' THEN 1 END) as failed_count
FROM emails e
LEFT JOIN parsing_results pr ON pr.email_id = e.id
WHERE e.received_date >= CURRENT_DATE - INTERVAL '30 days'
GROUP BY DATE(e.received_date), e.vendor
ORDER BY date DESC, e.vendor;
```

## 統計分析功能

### 📈 核心分析類別

```python
from dataclasses import dataclass
from datetime import date, datetime, timedelta
from typing import List, Dict, Optional
import pandas as pd
import numpy as np

@dataclass
class VendorStatistics:
    vendor_code: str
    vendor_name: str
    total_emails: int
    processed_emails: int
    failed_emails: int
    success_rate: float
    avg_yield: float
    total_quantity: int
    avg_processing_time: float

@dataclass
class YieldTrend:
    date: date
    vendor_code: str
    yield_value: float
    quantity: int
    trend_direction: str  # 'up', 'down', 'stable'

@dataclass
class SystemHealth:
    timestamp: datetime
    cpu_usage: float
    memory_usage: float
    disk_usage: float
    active_connections: int
    queue_size: int
    status: str  # 'healthy', 'warning', 'critical'

class StatisticsAnalyzer:
    """統計分析核心類別"""
    
    def __init__(self, db_port: DatabasePort, cache_port: CachePort):
        self.db = db_port
        self.cache = cache_port
    
    def get_vendor_performance(self, 
                             start_date: date, 
                             end_date: date) -> List[VendorStatistics]:
        """取得廠商效能統計"""
        cache_key = f"vendor_performance_{start_date}_{end_date}"
        
        # 檢查快取
        cached_result = self.cache.get(cache_key)
        if cached_result:
            return cached_result
        
        # 從資料庫查詢
        query = """
        SELECT 
            v.code,
            v.name,
            COUNT(e.id) as total_emails,
            COUNT(CASE WHEN e.status = 'completed' THEN 1 END) as processed,
            COUNT(CASE WHEN e.status = 'failed' THEN 1 END) as failed,
            AVG(pr.yield_value) as avg_yield,
            SUM(pr.in_qty) as total_qty,
            AVG(EXTRACT(EPOCH FROM pr.parsing_duration)) as avg_time
        FROM vendors v
        LEFT JOIN emails e ON e.vendor = v.code 
            AND e.received_date BETWEEN %s AND %s
        LEFT JOIN parsing_results pr ON pr.email_id = e.id
        GROUP BY v.id, v.code, v.name
        """
        
        results = self.db.query(query, [start_date, end_date])
        
        vendor_stats = []
        for row in results:
            stats = VendorStatistics(
                vendor_code=row['code'],
                vendor_name=row['name'],
                total_emails=row['total_emails'] or 0,
                processed_emails=row['processed'] or 0,
                failed_emails=row['failed'] or 0,
                success_rate=self._calculate_success_rate(
                    row['processed'], row['total_emails']
                ),
                avg_yield=float(row['avg_yield'] or 0),
                total_quantity=row['total_qty'] or 0,
                avg_processing_time=float(row['avg_time'] or 0)
            )
            vendor_stats.append(stats)
        
        # 快取結果 (1小時)
        self.cache.set(cache_key, vendor_stats, ttl=3600)
        
        return vendor_stats
    
    def analyze_yield_trends(self, 
                           vendor_code: str, 
                           days: int = 30) -> List[YieldTrend]:
        """分析良率趨勢"""
        end_date = date.today()
        start_date = end_date - timedelta(days=days)
        
        query = """
        SELECT 
            DATE(e.received_date) as date,
            AVG(pr.yield_value) as avg_yield,
            SUM(pr.in_qty) as total_qty
        FROM emails e
        JOIN parsing_results pr ON pr.email_id = e.id
        WHERE e.vendor = %s 
            AND e.received_date BETWEEN %s AND %s
            AND pr.yield_value IS NOT NULL
        GROUP BY DATE(e.received_date)
        ORDER BY date
        """
        
        results = self.db.query(query, [vendor_code, start_date, end_date])
        
        trends = []
        previous_yield = None
        
        for row in results:
            current_yield = float(row['avg_yield'])
            
            # 計算趨勢方向
            if previous_yield is None:
                trend_direction = 'stable'
            elif current_yield > previous_yield * 1.02:  # 2% 以上增長
                trend_direction = 'up'
            elif current_yield < previous_yield * 0.98:  # 2% 以上下降
                trend_direction = 'down'
            else:
                trend_direction = 'stable'
            
            trend = YieldTrend(
                date=row['date'],
                vendor_code=vendor_code,
                yield_value=current_yield,
                quantity=row['total_qty'] or 0,
                trend_direction=trend_direction
            )
            trends.append(trend)
            previous_yield = current_yield
        
        return trends
    
    def detect_anomalies(self, vendor_code: str, metric: str = 'yield') -> List[dict]:
        """異常檢測"""
        # 使用 Z-score 方法檢測異常值
        query = """
        SELECT 
            e.id,
            e.received_date,
            pr.yield_value,
            pr.in_qty
        FROM emails e
        JOIN parsing_results pr ON pr.email_id = e.id
        WHERE e.vendor = %s 
            AND e.received_date >= %s
            AND pr.yield_value IS NOT NULL
        ORDER BY e.received_date
        """
        
        thirty_days_ago = date.today() - timedelta(days=30)
        results = self.db.query(query, [vendor_code, thirty_days_ago])
        
        if not results:
            return []
        
        # 轉換為 pandas DataFrame 進行分析
        df = pd.DataFrame(results)
        
        if metric == 'yield':
            values = df['yield_value'].astype(float)
        else:
            values = df['in_qty'].astype(float)
        
        # 計算 Z-score
        mean_val = values.mean()
        std_val = values.std()
        z_scores = np.abs((values - mean_val) / std_val)
        
        # 找出異常值 (Z-score > 2)
        anomalies = []
        threshold = 2.0
        
        for idx, z_score in enumerate(z_scores):
            if z_score > threshold:
                anomaly = {
                    'email_id': results[idx]['id'],
                    'date': results[idx]['received_date'],
                    'value': float(values.iloc[idx]),
                    'z_score': float(z_score),
                    'severity': 'high' if z_score > 3 else 'medium'
                }
                anomalies.append(anomaly)
        
        return anomalies
    
    def generate_executive_summary(self, days: int = 7) -> dict:
        """生成主管摘要報告"""
        end_date = date.today()
        start_date = end_date - timedelta(days=days)
        
        # 整體統計
        total_emails = self.db.count_emails(start_date, end_date)
        processed_emails = self.db.count_processed_emails(start_date, end_date)
        
        # 廠商表現
        vendor_stats = self.get_vendor_performance(start_date, end_date)
        
        # 系統效能
        avg_processing_time = self.db.get_avg_processing_time(start_date, end_date)
        
        # 異常警示
        alerts = self.db.get_active_alerts()
        
        return {
            'period': f"{start_date} 至 {end_date}",
            'total_emails': total_emails,
            'processed_emails': processed_emails,
            'success_rate': (processed_emails / total_emails * 100) if total_emails > 0 else 0,
            'avg_processing_time_seconds': avg_processing_time,
            'vendor_count': len(vendor_stats),
            'top_performing_vendor': max(vendor_stats, key=lambda x: x.success_rate) if vendor_stats else None,
            'active_alerts': len(alerts),
            'critical_alerts': len([a for a in alerts if a['severity'] == 'critical']),
            'system_health': self._get_current_system_health()
        }
    
    def _calculate_success_rate(self, processed: int, total: int) -> float:
        """計算成功率"""
        if total == 0:
            return 0.0
        return (processed / total) * 100
    
    def _get_current_system_health(self) -> SystemHealth:
        """取得目前系統健康狀態"""
        # 這裡可以整合系統監控工具
        import psutil
        
        return SystemHealth(
            timestamp=datetime.now(),
            cpu_usage=psutil.cpu_percent(),
            memory_usage=psutil.virtual_memory().percent,
            disk_usage=psutil.disk_usage('/').percent,
            active_connections=len(psutil.net_connections()),
            queue_size=self.db.get_queue_size(),
            status=self._determine_health_status()
        )
    
    def _determine_health_status(self) -> str:
        """判斷系統健康狀態"""
        health = self._get_current_system_health()
        
        if (health.cpu_usage > 90 or 
            health.memory_usage > 90 or 
            health.disk_usage > 95):
            return 'critical'
        elif (health.cpu_usage > 70 or 
              health.memory_usage > 80 or 
              health.disk_usage > 85):
            return 'warning'
        else:
            return 'healthy'
```

## 新增組件說明

### 🔌 資料庫通訊埠介面

```python
from abc import ABC, abstractmethod
from typing import List, Dict, Any, Optional
from datetime import date, datetime

class DatabasePort(ABC):
    """資料庫操作介面"""
    
    @abstractmethod
    def save_email(self, email: Email) -> int:
        """儲存郵件記錄"""
        pass
    
    @abstractmethod
    def save_parsing_result(self, result: ParseResult) -> int:
        """儲存解析結果"""
        pass
    
    @abstractmethod
    def get_vendor_statistics(self, 
                            start_date: date, 
                            end_date: date) -> List[Dict[str, Any]]:
        """取得廠商統計資料"""
        pass
    
    @abstractmethod
    def get_daily_statistics(self, vendor: str, days: int) -> List[Dict[str, Any]]:
        """取得每日統計資料"""
        pass
    
    @abstractmethod
    def create_alert(self, alert: Alert) -> int:
        """建立警示"""
        pass
    
    @abstractmethod
    def get_system_metrics(self) -> Dict[str, float]:
        """取得系統指標"""
        pass

class AnalyticsPort(ABC):
    """分析功能介面"""
    
    @abstractmethod
    def analyze_trends(self, data: List[Dict]) -> TrendAnalysis:
        """趨勢分析"""
        pass
    
    @abstractmethod
    def detect_anomalies(self, data: List[float]) -> List[Anomaly]:
        """異常檢測"""
        pass
    
    @abstractmethod
    def generate_forecast(self, historical_data: List[Dict]) -> Forecast:
        """生成預測"""
        pass
    
    @abstractmethod
    def create_visualization(self, data: List[Dict], chart_type: str) -> Chart:
        """建立視覺化圖表"""
        pass

class CachePort(ABC):
    """快取操作介面"""
    
    @abstractmethod
    def get(self, key: str) -> Optional[Any]:
        """取得快取值"""
        pass
    
    @abstractmethod
    def set(self, key: str, value: Any, ttl: int = 3600) -> bool:
        """設定快取值"""
        pass
    
    @abstractmethod
    def delete(self, key: str) -> bool:
        """刪除快取值"""
        pass
    
    @abstractmethod
    def clear_pattern(self, pattern: str) -> int:
        """清除符合模式的快取"""
        pass
```

### 📊 儀表板 API 路由

```python
from fastapi import APIRouter, Depends, HTTPException
from datetime import date, timedelta
from typing import List, Optional

router = APIRouter(prefix="/api/dashboard", tags=["儀表板"])

@router.get("/summary")
async def get_dashboard_summary(
    days: int = 7,
    analytics: StatisticsAnalyzer = Depends(get_analytics_service)
) -> Dict[str, Any]:
    """取得儀表板摘要資訊"""
    try:
        summary = analytics.generate_executive_summary(days)
        return {
            "success": True,
            "data": summary
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/vendors/performance")
async def get_vendor_performance(
    start_date: Optional[date] = None,
    end_date: Optional[date] = None,
    analytics: StatisticsAnalyzer = Depends(get_analytics_service)
) -> Dict[str, Any]:
    """取得廠商效能資料"""
    if not start_date:
        start_date = date.today() - timedelta(days=30)
    if not end_date:
        end_date = date.today()
    
    try:
        performance = analytics.get_vendor_performance(start_date, end_date)
        return {
            "success": True,
            "data": [stat.__dict__ for stat in performance]
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/trends/{vendor_code}")
async def get_yield_trends(
    vendor_code: str,
    days: int = 30,
    analytics: StatisticsAnalyzer = Depends(get_analytics_service)
) -> Dict[str, Any]:
    """取得良率趨勢資料"""
    try:
        trends = analytics.analyze_yield_trends(vendor_code, days)
        return {
            "success": True,
            "data": [trend.__dict__ for trend in trends]
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/anomalies/{vendor_code}")
async def get_anomalies(
    vendor_code: str,
    metric: str = "yield",
    analytics: StatisticsAnalyzer = Depends(get_analytics_service)
) -> Dict[str, Any]:
    """取得異常檢測結果"""
    try:
        anomalies = analytics.detect_anomalies(vendor_code, metric)
        return {
            "success": True,
            "data": anomalies
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/alerts")
async def get_active_alerts(
    severity: Optional[str] = None,
    db: DatabasePort = Depends(get_database_service)
) -> Dict[str, Any]:
    """取得作用中的警示"""
    try:
        alerts = db.get_active_alerts(severity)
        return {
            "success": True,
            "data": alerts
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/alerts/{alert_id}/acknowledge")
async def acknowledge_alert(
    alert_id: int,
    user_id: str,
    db: DatabasePort = Depends(get_database_service)
) -> Dict[str, Any]:
    """確認警示"""
    try:
        success = db.acknowledge_alert(alert_id, user_id)
        return {
            "success": success,
            "message": "警示已確認" if success else "確認失敗"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
```

## 技術棧更新

### 📦 新增的相依套件

```toml
# pyproject.toml 更新
[tool.poetry.dependencies]
python = "^3.9"

# 原有套件
pydantic = "^2.0"
pandas = "^2.0"
openpyxl = "^3.0"
loguru = "^0.7"
fastapi = "^0.104"
uvicorn = "^0.24"

# 資料庫相關 ✨
psycopg2-binary = "^2.9"      # PostgreSQL 驅動
sqlalchemy = "^2.0"           # ORM
alembic = "^1.12"             # 資料庫遷移
sqlite = "^3.40"              # SQLite 支援

# 分析與視覺化 ✨
numpy = "^1.24"               # 數值計算
scipy = "^1.11"               # 科學計算
matplotlib = "^3.7"           # 基礎繪圖
plotly = "^5.17"              # 互動式圖表
seaborn = "^0.12"             # 統計圖表

# 快取與監控 ✨
redis = "^5.0"                # Redis 快取
prometheus-client = "^0.19"   # Prometheus 指標
psutil = "^5.9"               # 系統監控

# 資料處理 ✨
jupyter = "^1.0"              # Jupyter Notebook
ipython = "^8.17"             # 互動式 Python
xlsxwriter = "^3.1"           # Excel 進階功能

# 測試與開發工具 ✨
pytest-asyncio = "^0.21"      # 異步測試
factory-boy = "^3.3"          # 測試資料工廠
freezegun = "^1.2"            # 時間測試
```

### 🐳 Docker Compose 更新

```yaml
# docker-compose.yml
version: '3.8'

services:
  app:
    build: .
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=****************************************/outlook_summary
      - REDIS_URL=redis://redis:6379/0
    depends_on:
      - postgres
      - redis
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs

  postgres:
    image: postgres:15
    environment:
      POSTGRES_DB: outlook_summary
      POSTGRES_USER: user
      POSTGRES_PASSWORD: password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/init-db:/docker-entrypoint-initdb.d

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data

  prometheus:
    image: prom/prometheus:latest
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus:/etc/prometheus
      - prometheus_data:/prometheus

  grafana:
    image: grafana/grafana:latest
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana:/etc/grafana/provisioning

volumes:
  postgres_data:
  redis_data:
  prometheus_data:
  grafana_data:
```

## 開發指南

### 🚀 快速開始

```bash
# 1. 克隆專案
git clone <repository_url>
cd outlook-email-processor

# 2. 建立虛擬環境
python -m venv venv
source venv/bin/activate  # Windows: venv\Scripts\activate

# 3. 安裝相依套件
pip install -r requirements-dev.txt

# 4. 設定環境變數
cp .env.example .env
# 編輯 .env 檔案設定資料庫連線等

# 5. 啟動資料庫服務
docker-compose up -d postgres redis

# 6. 執行資料庫遷移
alembic upgrade head

# 7. 載入測試資料
python scripts/load_sample_data.py

# 8. 啟動應用程式
uvicorn src.main:app --reload --host 0.0.0.0 --port 8000

# 9. 存取儀表板
open http://localhost:8000/dashboard
```

### 📈 資料庫遷移

```bash
# 建立新的遷移檔案
alembic revision --autogenerate -m "描述變更內容"

# 執行遷移
alembic upgrade head

# 回滾遷移
alembic downgrade -1

# 檢視遷移歷史
alembic history
```

### 🧪 測試資料庫功能

```bash
# 單元測試
pytest tests/unit/test_database.py -v

# 整合測試
pytest tests/integration/test_analytics.py -v

# 效能測試
pytest tests/performance/test_database_performance.py -v

# 端對端測試（包含資料庫）
pytest tests/e2e/test_full_workflow_with_db.py -v
```

### 📊 監控與分析

```bash
# 檢視系統指標
curl http://localhost:8000/metrics

# 執行分析腳本
python analytics/scripts/daily_report.py

# 啟動 Jupyter Notebook
jupyter notebook analytics/notebooks/

# 檢視 Grafana 儀表板
open http://localhost:3000
```

這個完整的架構設計涵蓋了資料持久化、統計分析、即時監控、和可視化等功能，為系統提供了強大的資料管理和分析能力。