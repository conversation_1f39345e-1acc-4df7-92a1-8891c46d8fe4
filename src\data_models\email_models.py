"""
郵件數據模型
TASK_004: 建立郵件數據模型
使用 Pydantic 建立強型別的數據模型，支援郵件解析、廠商識別、任務管理
"""

import re
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Any, Optional, Union, ClassVar
from enum import Enum

from pydantic import BaseModel, Field, field_validator, model_validator


class ProcessingStatus(Enum):
    """處理狀態枚舉"""
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"


class EmailAttachment(BaseModel):
    """郵件附件模型"""
    
    filename: str = Field(..., min_length=1, description="附件檔名")
    content_type: str = Field(..., description="MIME 類型")
    size_bytes: int = Field(..., gt=0, description="檔案大小 (位元組)")
    file_path: Path = Field(..., description="檔案路徑")
    is_processed: bool = Field(default=False, description="是否已處理")
    checksum: Optional[str] = Field(default=None, description="檔案校驗和")
    
    # 允許的副檔名
    ALLOWED_EXTENSIONS: ClassVar[set] = {
        ".csv", ".xlsx", ".xls", ".zip", ".txt", ".dat",
        ".pdf", ".doc", ".docx", ".ppt", ".pptx", ".msg",
        ".xml", ".json", ".log", ".png", ".jpg", ".jpeg", ".gif", ".bmp"
    }
    
    # 檔案大小限制 (100MB)
    MAX_FILE_SIZE: ClassVar[int] = 100 * 1024 * 1024
    
    @field_validator('size_bytes')
    @classmethod
    def validate_file_size(cls, v):
        """驗證檔案大小"""
        if v > cls.MAX_FILE_SIZE:
            raise ValueError(f"檔案大小不能超過 {cls.MAX_FILE_SIZE / (1024*1024):.0f}MB")
        return v
    
    @field_validator('filename')
    @classmethod
    def validate_filename(cls, v):
        """驗證檔名和副檔名"""
        if not v.strip():
            raise ValueError("檔名不能為空")
        
        # 檢查副檔名
        file_ext = Path(v).suffix.lower()
        if file_ext not in cls.ALLOWED_EXTENSIONS:
            raise ValueError(
                f"不允許的檔案類型: {file_ext}. "
                f"允許的類型: {', '.join(cls.ALLOWED_EXTENSIONS)}"
            )
        
        return v
    
    class Config:
        """Pydantic 配置"""
        arbitrary_types_allowed = True


class EmailData(BaseModel):
    """郵件數據模型"""
    
    message_id: str = Field(..., description="郵件 ID")
    subject: str = Field(..., min_length=1, description="郵件主旨")
    sender: str = Field(..., description="寄件者")
    recipient: Optional[str] = Field(default=None, description="收件者")
    received_time: datetime = Field(..., description="接收時間")
    body: Optional[str] = Field(default=None, description="郵件內容")
    attachments: List[EmailAttachment] = Field(default_factory=list, description="附件列表")
    headers: Dict[str, str] = Field(default_factory=dict, description="郵件標頭")
    raw_data: Dict[str, Any] = Field(default_factory=dict, description="原始數據")
    
    @field_validator('subject')
    @classmethod
    def validate_subject(cls, v):
        """驗證郵件主旨"""
        if not v.strip():
            raise ValueError("郵件主旨不能為空")
        return v.strip()
    
    @field_validator('sender')
    @classmethod
    def validate_sender(cls, v):
        """驗證寄件者 email 格式 (支援中文字元)"""
        # 簡化的 email 驗證，支援中文字元
        if "@" not in v or v.count("@") != 1:
            raise ValueError(f"無效的 email 格式: {v}")
        
        local, domain = v.split("@")
        if not local or not domain:
            raise ValueError(f"無效的 email 格式: {v}")
        
        # 檢查域名部分包含點號
        if "." not in domain:
            raise ValueError(f"無效的 email 格式: {v}")
        
        return v
    
    def add_attachment(self, attachment: EmailAttachment) -> None:
        """新增附件"""
        self.attachments.append(attachment)
    
    def get_attachment_count(self) -> int:
        """取得附件數量"""
        return len(self.attachments)
    
    def get_total_attachment_size(self) -> int:
        """取得所有附件總大小"""
        return sum(att.size_bytes for att in self.attachments)


class VendorIdentificationResult(BaseModel):
    """廠商識別結果模型"""
    
    vendor_code: Optional[str] = Field(default=None, description="廠商代碼")
    vendor_name: Optional[str] = Field(default=None, description="廠商名稱") 
    confidence_score: float = Field(..., ge=0.0, le=1.0, description="信心分數 (0-1)")
    matching_patterns: List[str] = Field(default_factory=list, description="匹配的模式")
    is_identified: bool = Field(default=False, description="是否成功識別")
    identification_method: Optional[str] = Field(default=None, description="識別方法")
    
    @field_validator('confidence_score')
    @classmethod
    def validate_confidence_score(cls, v):
        """驗證信心分數範圍"""
        if not (0.0 <= v <= 1.0):
            raise ValueError("信心分數必須在 0.0 到 1.0 之間")
        return v
    
    @model_validator(mode='after')
    def validate_identification_consistency(self):
        """驗證識別結果一致性"""
        if self.is_identified:
            if not self.vendor_code:
                raise ValueError("識別成功時必須提供廠商代碼")
        return self


class EmailParsingResult(BaseModel):
    """郵件解析結果模型"""
    
    is_success: bool = Field(..., description="是否解析成功")
    vendor_code: Optional[str] = Field(default=None, description="廠商代碼")
    mo_number: Optional[str] = Field(default=None, description="MO 編號")
    lot_number: Optional[str] = Field(default=None, description="批次編號")
    test_type: Optional[str] = Field(default=None, description="測試類型")
    extracted_data: Dict[str, Any] = Field(default_factory=dict, description="解析出的數據")
    error_message: Optional[str] = Field(default=None, description="錯誤訊息")
    validation_errors: List[str] = Field(default_factory=list, description="驗證錯誤")
    parsing_method: Optional[str] = Field(default=None, description="解析方法")
    
    # MO 編號格式模式 (例如: F123456, G234567)
    MO_NUMBER_PATTERN: ClassVar[str] = r'^[A-Z]\d{6}$'
    
    @field_validator('mo_number')
    @classmethod
    def validate_mo_number(cls, v):
        """驗證 MO 編號格式"""
        if v is not None:
            if not re.match(cls.MO_NUMBER_PATTERN, v):
                raise ValueError(f"MO 編號格式不正確: {v}. 正確格式: 字母+6位數字 (例如: F123456)")
        return v
    
    @model_validator(mode='after')
    def validate_parsing_consistency(self):
        """驗證解析結果一致性"""
        if self.is_success:
            if not self.vendor_code:
                raise ValueError("解析成功時必須提供廠商代碼")
        else:
            if not self.error_message:
                raise ValueError("解析失敗時必須提供錯誤訊息")
        return self
    
    def add_validation_error(self, error: str) -> None:
        """新增驗證錯誤"""
        self.validation_errors.append(error)
    
    def has_validation_errors(self) -> bool:
        """檢查是否有驗證錯誤"""
        return len(self.validation_errors) > 0


class EmailMetadata(BaseModel):
    """郵件元數據模型"""
    
    message_size_bytes: Optional[int] = Field(default=None, ge=0, description="郵件大小")
    attachment_count: Optional[int] = Field(default=None, ge=0, description="附件數量")
    processing_priority: int = Field(default=5, ge=1, le=10, description="處理優先級 (1-10)")
    source_folder: Optional[str] = Field(default=None, description="來源資料夾")
    backup_location: Optional[Path] = Field(default=None, description="備份位置")
    tags: List[str] = Field(default_factory=list, description="標籤")
    custom_fields: Dict[str, Any] = Field(default_factory=dict, description="自訂欄位")
    
    @field_validator('processing_priority')
    @classmethod
    def validate_priority(cls, v):
        """驗證處理優先級"""
        if not (1 <= v <= 10):
            raise ValueError("處理優先級必須在 1 到 10 之間")
        return v
    
    class Config:
        """Pydantic 配置"""
        arbitrary_types_allowed = True


class TaskData(BaseModel):
    """任務數據模型"""
    
    task_id: str = Field(..., description="任務 ID")
    email_id: str = Field(..., description="關聯的郵件 ID")
    vendor_code: str = Field(..., description="廠商代碼")
    mo_number: Optional[str] = Field(default=None, description="MO 編號")
    lot_number: Optional[str] = Field(default=None, description="批次編號")
    status: ProcessingStatus = Field(default=ProcessingStatus.PENDING, description="處理狀態")
    created_time: datetime = Field(..., description="建立時間")
    started_time: Optional[datetime] = Field(default=None, description="開始時間")
    completed_time: Optional[datetime] = Field(default=None, description="完成時間")
    error_message: Optional[str] = Field(default=None, description="錯誤訊息")
    result_data: Dict[str, Any] = Field(default_factory=dict, description="處理結果")
    retry_count: int = Field(default=0, ge=0, description="重試次數")
    max_retries: int = Field(default=3, ge=0, description="最大重試次數")
    
    def is_completed(self) -> bool:
        """檢查是否已完成"""
        return self.status == ProcessingStatus.COMPLETED
    
    def is_failed(self) -> bool:
        """檢查是否失敗"""
        return self.status == ProcessingStatus.FAILED
    
    def can_retry(self) -> bool:
        """檢查是否可以重試"""
        return self.retry_count < self.max_retries and self.is_failed()
    
    def mark_started(self) -> None:
        """標記為開始處理"""
        self.status = ProcessingStatus.PROCESSING
        self.started_time = datetime.now()
    
    def mark_completed(self, result_data: Optional[Dict[str, Any]] = None) -> None:
        """標記為完成"""
        self.status = ProcessingStatus.COMPLETED
        self.completed_time = datetime.now()
        if result_data:
            self.result_data.update(result_data)
    
    def mark_failed(self, error_message: str) -> None:
        """標記為失敗"""
        self.status = ProcessingStatus.FAILED
        self.error_message = error_message
        self.completed_time = datetime.now()


class FileProcessingInfo(BaseModel):
    """檔案處理資訊模型"""
    
    source_file: Path = Field(..., description="來源檔案路徑")
    processed_file: Optional[Path] = Field(default=None, description="處理後檔案路徑")
    file_type: str = Field(..., description="檔案類型")
    file_size_bytes: int = Field(..., gt=0, description="檔案大小")
    processing_steps: List[str] = Field(..., min_length=1, description="處理步驟")
    started_time: Optional[datetime] = Field(default=None, description="處理開始時間")
    completed_time: Optional[datetime] = Field(default=None, description="處理完成時間")
    is_success: bool = Field(default=False, description="是否處理成功")
    error_message: Optional[str] = Field(default=None, description="錯誤訊息")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="檔案元數據")
    
    @field_validator('processing_steps')
    @classmethod
    def validate_processing_steps(cls, v):
        """驗證處理步驟"""
        if not v:
            raise ValueError("必須至少包含一個處理步驟")
        return v
    
    def add_processing_step(self, step: str) -> None:
        """新增處理步驟"""
        self.processing_steps.append(step)
    
    def mark_started(self) -> None:
        """標記處理開始"""
        self.started_time = datetime.now()
    
    def mark_completed(self, processed_file: Optional[Path] = None) -> None:
        """標記處理完成"""
        self.is_success = True
        self.completed_time = datetime.now()
        if processed_file:
            self.processed_file = processed_file
    
    def mark_failed(self, error_message: str) -> None:
        """標記處理失敗"""
        self.is_success = False
        self.error_message = error_message
        self.completed_time = datetime.now()
    
    class Config:
        """Pydantic 配置"""
        arbitrary_types_allowed = True


class EmailProcessingContext(BaseModel):
    """郵件處理上下文模型"""
    
    email_data: EmailData = Field(..., description="郵件數據")
    vendor_identification: Optional[VendorIdentificationResult] = Field(
        default=None, description="廠商識別結果"
    )
    parsing_result: Optional[EmailParsingResult] = Field(
        default=None, description="解析結果"
    )
    task_data: Optional[TaskData] = Field(default=None, description="任務數據")
    file_processing_info: List[FileProcessingInfo] = Field(
        default_factory=list, description="檔案處理資訊"
    )
    metadata: EmailMetadata = Field(default_factory=EmailMetadata, description="郵件元數據")
    processing_start_time: datetime = Field(..., description="處理開始時間")
    processing_end_time: Optional[datetime] = Field(default=None, description="處理結束時間")
    context_data: Dict[str, Any] = Field(default_factory=dict, description="上下文數據")
    
    def is_vendor_identified(self) -> bool:
        """檢查是否已識別廠商"""
        return (
            self.vendor_identification is not None and 
            self.vendor_identification.is_identified
        )
    
    def is_parsing_successful(self) -> bool:
        """檢查是否解析成功"""
        return (
            self.parsing_result is not None and 
            self.parsing_result.is_success
        )
    
    def get_vendor_code(self) -> Optional[str]:
        """取得廠商代碼"""
        if self.vendor_identification:
            return self.vendor_identification.vendor_code
        elif self.parsing_result:
            return self.parsing_result.vendor_code
        return None
    
    def add_file_processing_info(self, info: FileProcessingInfo) -> None:
        """新增檔案處理資訊"""
        self.file_processing_info.append(info)
    
    def mark_processing_completed(self) -> None:
        """標記處理完成"""
        self.processing_end_time = datetime.now()
    
    def get_processing_duration(self) -> Optional[float]:
        """取得處理時長 (秒)"""
        if self.processing_end_time:
            return (self.processing_end_time - self.processing_start_time).total_seconds()
        return None
    
    def to_summary_dict(self) -> Dict[str, Any]:
        """轉換為摘要字典"""
        return {
            "email_id": self.email_data.message_id,
            "subject": self.email_data.subject,
            "sender": self.email_data.sender,
            "vendor_code": self.get_vendor_code(),
            "vendor_identified": self.is_vendor_identified(),
            "parsing_successful": self.is_parsing_successful(),
            "attachment_count": self.email_data.get_attachment_count(),
            "processing_duration": self.get_processing_duration(),
            "start_time": self.processing_start_time.isoformat(),
            "end_time": self.processing_end_time.isoformat() if self.processing_end_time else None
        }