#!/usr/bin/env python3
"""
EQC 詳細日誌記錄模組
記錄 FT、ONLINE EQC、EQC RT 檔案的配對和統計資訊
遵循 CLAUDE.md 功能替換原則，從主處理器中獨立出來
"""

import os
from datetime import datetime
from typing import List, Optional


class EQCDebugLogger:
    """
    EQCTOTALDATA 生成過程的詳細日誌記錄器
    記錄 FT、ONLINE EQC、EQC RT 檔案的配對和統計資訊
    """
    
    def __init__(self, log_dir: str, enabled: bool = True):
        """
        初始化日誌記錄器
        
        Args:
            log_dir: 日誌目錄
            enabled: 是否啟用日誌記錄
        """
        self.enabled = enabled and os.getenv("EQC_DETAILED_LOGS", "true").lower() == "true"
        self.log_dir = log_dir
        self.log_entries = []
        
        if self.enabled:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            self.log_filename = f"EQCTOTALDATA_DEBUG_{timestamp}.log"
            self.log_path = os.path.join(log_dir, self.log_filename)
            self._init_log()
    
    def _init_log(self) -> None:
        """初始化日誌檔案標頭"""
        if not self.enabled:
            return
        
        header = [
            "=" * 80,
            "EQCTOTALDATA 生成過程詳細日誌",
            "=" * 80,
            f"處理時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
            f"處理目錄: {self.log_dir}",
            "=" * 80,
            ""
        ]
        self.log_entries.extend(header)
    
    def log_section(self, title: str) -> None:
        """
        記錄段落標題
        
        Args:
            title: 段落標題
        """
        if not self.enabled:
            return
        
        self.log_entries.extend([
            "",
            f"📋 {title}",
            "-" * 60
        ])
    
    def log_file_scan(self, file_type: str, files: List[str]) -> None:
        """
        記錄檔案掃描結果
        
        Args:
            file_type: 檔案類型名稱
            files: 檔案路徑列表
        """
        if not self.enabled:
            return
        
        self.log_entries.append(f"📁 {file_type} 檔案掃描結果: {len(files)} 個檔案")
        for file_path in files:
            filename = os.path.basename(file_path)
            self.log_entries.append(f"   - {filename}")
    
    def log_file_timestamp(self, file_path: str, timestamp: Optional[int], readable_time: str) -> None:
        """
        記錄檔案時間戳提取結果
        
        Args:
            file_path: 檔案路徑
            timestamp: Unix時間戳
            readable_time: 可讀時間字串
        """
        if not self.enabled:
            return
        
        filename = os.path.basename(file_path)
        if timestamp:
            self.log_entries.append(f"   ⏰ {filename}: {readable_time} (timestamp: {timestamp})")
        else:
            self.log_entries.append(f"   ❌ {filename}: 時間戳提取失敗")
    
    def log_pairing_result(self, ft_file: str, eqc_file: str, match_method: str = "時間匹配") -> None:
        """
        記錄配對結果
        
        Args:
            ft_file: FT檔案路徑
            eqc_file: EQC檔案路徑
            match_method: 配對方法
        """
        if not self.enabled:
            return
        
        ft_name = os.path.basename(ft_file) if ft_file else "無"
        eqc_name = os.path.basename(eqc_file) if eqc_file else "無"
        self.log_entries.append(f"   🔗 配對成功 ({match_method}): {ft_name} ↔ {eqc_name}")
    
    def log_statistics(self, file_type: str, file_path: str, pass_count: int, fail_count: int, total_count: int) -> None:
        """
        記錄檔案統計資訊
        
        Args:
            file_type: 檔案類型
            file_path: 檔案路徑
            pass_count: 通過數量
            fail_count: 失敗數量
            total_count: 總數量
        """
        if not self.enabled:
            return
        
        filename = os.path.basename(file_path)
        self.log_entries.append(f"   📊 {filename}: PASS={pass_count}, FAIL={fail_count}, TOTAL={total_count}")
    
    def log_unmatched_files(self, file_type: str, files: List[str], processor_instance=None) -> None:
        """
        記錄未配對檔案，包含內部時間戳資訊
        
        Args:
            file_type: 檔案類型
            files: 未配對檔案列表
            processor_instance: 處理器實例，用於提取時間戳
        """
        if not self.enabled:
            return

        if files:
            self.log_entries.append(f"📂 未配對的 {file_type} 檔案: {len(files)} 個")
            for file_path in files:
                filename = os.path.basename(file_path)

                # 嘗試提取內部時間戳
                timestamp_info = ""
                if processor_instance and hasattr(processor_instance, '_extract_internal_timestamp'):
                    try:
                        timestamp = processor_instance._extract_internal_timestamp(file_path)
                        if timestamp and timestamp > 0:
                            readable_time = datetime.fromtimestamp(timestamp).strftime("%Y-%m-%d %H:%M:%S")
                            timestamp_info = f" | 內部時間: {readable_time}"
                        else:
                            timestamp_info = " | 內部時間: 無法提取"
                    except Exception:
                        timestamp_info = " | 內部時間: 提取失敗"

                self.log_entries.append(f"   - {filename}{timestamp_info}")
    
    def log_summary(self, online_eqc_fail: int, eqc_rt_pass: int, total_pairs: int) -> None:
        """
        記錄處理總結
        
        Args:
            online_eqc_fail: Online EQC FAIL總數
            eqc_rt_pass: EQC RT PASS總數
            total_pairs: 成功配對數量
        """
        if not self.enabled:
            return
        
        self.log_entries.extend([
            "",
            "📋 處理總結",
            "-" * 60,
            f"🔴 Online EQC FAIL 總數: {online_eqc_fail}",
            f"🟢 EQC RT PASS 總數: {eqc_rt_pass}",
            f"🔗 成功配對數量: {total_pairs}",
            "=" * 80
        ])
    
    def save_log(self) -> None:
        """保存日誌到檔案"""
        if not self.enabled or not self.log_entries:
            return
        
        try:
            with open(self.log_path, 'w', encoding='utf-8') as f:
                f.write('\n'.join(self.log_entries))
            print(f"📋 詳細日誌已保存: {os.path.basename(self.log_path)}")
        except Exception as e:
            print(f"❌ 保存日誌失敗: {e}")
    
    def get_log_path(self) -> Optional[str]:
        """
        獲取日誌檔案路徑
        
        Returns:
            str: 日誌檔案路徑，未啟用則返回None
        """
        return self.log_path if self.enabled else None