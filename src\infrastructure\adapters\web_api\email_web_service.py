"""
郵件 Web API 服務
提供 Flask 路由和 API 端點
"""

from flask import Flask, Blueprint, request, jsonify, render_template
from typing import Dict, Any, List, Optional
from datetime import datetime
import traceback

from src.infrastructure.adapters.database.email_database import EmailDatabase
from src.infrastructure.logging.logger_manager import LoggerManager


class EmailWebService:
    """
    郵件 Web API 服務類
    提供郵件相關的 RESTful API 服務
    """
    
    def __init__(self, database: EmailDatabase = None):
        """
        初始化郵件 Web 服務
        
        Args:
            database: 郵件資料庫實例
        """
        self.logger = LoggerManager().get_logger("EmailWebService")
        self.database = database or EmailDatabase()
        
        self.logger.info("郵件 Web API 服務已初始化")
    
    def get_emails_api(self) -> Dict[str, Any]:
        """
        取得郵件列表 API
        
        Returns:
            API 回應字典
        """
        try:
            # 取得查詢參數
            sender = request.args.get('sender')
            limit = int(request.args.get('limit', 50))
            offset = int(request.args.get('offset', 0))
            order_by = request.args.get('order_by', 'received_time')
            order_desc = request.args.get('order_desc', 'true').lower() == 'true'
            
            # 查詢郵件
            emails = self.database.get_emails(
                sender=sender,
                limit=limit,
                offset=offset,
                order_by=order_by,
                order_desc=order_desc
            )
            
            # 取得總數
            total_count = self.database.get_email_count(sender=sender)
            
            return {
                'success': True,
                'data': {
                    'emails': emails,
                    'total_count': total_count,
                    'has_more': (offset + limit) < total_count
                },
                'message': f'成功取得 {len(emails)} 封郵件'
            }
            
        except Exception as e:
            self.logger.error(f"取得郵件列表失敗: {e}")
            return {
                'success': False,
                'error': str(e),
                'message': '取得郵件列表失敗'
            }
    
    def get_email_detail_api(self, email_id: int) -> Dict[str, Any]:
        """
        取得單一郵件詳情 API
        
        Args:
            email_id: 郵件 ID
            
        Returns:
            API 回應字典
        """
        try:
            email = self.database.get_email_by_id(email_id)
            
            if not email:
                return {
                    'success': False,
                    'error': 'Email not found',
                    'message': f'找不到郵件 ID: {email_id}'
                }
            
            # 標記為已讀
            self.database.mark_email_as_read(email_id)
            
            return {
                'success': True,
                'data': email,
                'message': '成功取得郵件詳情'
            }
            
        except Exception as e:
            self.logger.error(f"取得郵件詳情失敗: {e}")
            return {
                'success': False,
                'error': str(e),
                'message': '取得郵件詳情失敗'
            }
    
    def get_senders_api(self) -> Dict[str, Any]:
        """
        取得寄件者列表 API
        
        Returns:
            API 回應字典
        """
        try:
            senders = self.database.get_senders()
            
            return {
                'success': True,
                'data': {
                    'senders': senders,
                    'total_count': len(senders)
                },
                'message': f'成功取得 {len(senders)} 位寄件者'
            }
            
        except Exception as e:
            self.logger.error(f"取得寄件者列表失敗: {e}")
            return {
                'success': False,
                'error': str(e),
                'message': '取得寄件者列表失敗'
            }
    
    def search_emails_api(self) -> Dict[str, Any]:
        """
        搜尋郵件 API
        
        Returns:
            API 回應字典
        """
        try:
            # 取得搜尋參數
            search_term = request.args.get('q', '').strip()
            search_fields = request.args.get('fields', 'subject,body,sender').split(',')
            sender = request.args.get('sender')
            limit = int(request.args.get('limit', 50))
            
            if not search_term:
                return {
                    'success': False,
                    'error': 'Missing search term',
                    'message': '請提供搜尋關鍵字'
                }
            
            # 執行搜尋
            results = self.database.search_emails(
                search_term=search_term,
                search_fields=search_fields,
                sender=sender,
                limit=limit
            )
            
            return {
                'success': True,
                'data': {
                    'emails': results,
                    'search_term': search_term,
                    'total_count': len(results)
                },
                'message': f'找到 {len(results)} 封相關郵件'
            }
            
        except Exception as e:
            self.logger.error(f"搜尋郵件失敗: {e}")
            return {
                'success': False,
                'error': str(e),
                'message': '搜尋郵件失敗'
            }
    
    def delete_email_api(self, email_id: int) -> Dict[str, Any]:
        """
        刪除郵件 API
        
        Args:
            email_id: 郵件 ID
            
        Returns:
            API 回應字典
        """
        try:
            success = self.database.delete_email(email_id)
            
            if success:
                return {
                    'success': True,
                    'message': f'成功刪除郵件 ID: {email_id}'
                }
            else:
                return {
                    'success': False,
                    'error': 'Email not found',
                    'message': f'找不到郵件 ID: {email_id}'
                }
                
        except Exception as e:
            self.logger.error(f"刪除郵件失敗: {e}")
            return {
                'success': False,
                'error': str(e),
                'message': '刪除郵件失敗'
            }
    
    def get_statistics_api(self) -> Dict[str, Any]:
        """
        取得統計資訊 API
        
        Returns:
            API 回應字典
        """
        try:
            stats = self.database.get_statistics()
            
            return {
                'success': True,
                'data': stats,
                'message': '成功取得統計資訊'
            }
            
        except Exception as e:
            self.logger.error(f"取得統計資訊失敗: {e}")
            return {
                'success': False,
                'error': str(e),
                'message': '取得統計資訊失敗'
            }
    
    def sync_emails_api(self) -> Dict[str, Any]:
        """
        同步郵件 API
        
        Returns:
            API 回應字典
        """
        try:
            # 這裡會調用郵件同步服務
            # 暫時返回成功狀態，實際實作會在郵件同步服務中完成
            return {
                'success': True,
                'message': '郵件同步請求已提交',
                'data': {
                    'sync_status': 'requested',
                    'timestamp': datetime.now().isoformat()
                }
            }
            
        except Exception as e:
            self.logger.error(f"同步郵件失敗: {e}")
            return {
                'success': False,
                'error': str(e),
                'message': '同步郵件失敗'
            }


def create_email_api_blueprint(database: EmailDatabase = None) -> Blueprint:
    """
    建立郵件 API Blueprint
    
    Args:
        database: 郵件資料庫實例
        
    Returns:
        Flask Blueprint
    """
    api_bp = Blueprint('email_api', __name__, url_prefix='/api')
    email_service = EmailWebService(database)
    
    @api_bp.route('/emails', methods=['GET'])
    def get_emails():
        """取得郵件列表"""
        result = email_service.get_emails_api()
        return jsonify(result), 200 if result['success'] else 400
    
    @api_bp.route('/emails/<int:email_id>', methods=['GET'])
    def get_email_detail(email_id: int):
        """取得郵件詳情"""
        result = email_service.get_email_detail_api(email_id)
        return jsonify(result), 200 if result['success'] else 404
    
    @api_bp.route('/emails/<int:email_id>', methods=['DELETE'])
    def delete_email(email_id: int):
        """刪除郵件"""
        result = email_service.delete_email_api(email_id)
        return jsonify(result), 200 if result['success'] else 404
    
    @api_bp.route('/senders', methods=['GET'])
    def get_senders():
        """取得寄件者列表"""
        result = email_service.get_senders_api()
        return jsonify(result), 200 if result['success'] else 400
    
    @api_bp.route('/search', methods=['GET'])
    def search_emails():
        """搜尋郵件"""
        result = email_service.search_emails_api()
        return jsonify(result), 200 if result['success'] else 400
    
    @api_bp.route('/statistics', methods=['GET'])
    def get_statistics():
        """取得統計資訊"""
        result = email_service.get_statistics_api()
        return jsonify(result), 200 if result['success'] else 400
    
    @api_bp.route('/sync', methods=['POST'])
    def sync_emails():
        """同步郵件"""
        result = email_service.sync_emails_api()
        return jsonify(result), 200 if result['success'] else 400
    
    @api_bp.errorhandler(Exception)
    def handle_api_error(error):
        """API 錯誤處理"""
        logger = LoggerManager().get_logger("EmailAPI")
        logger.error(f"API 錯誤: {error}")
        logger.error(traceback.format_exc())
        
        return jsonify({
            'success': False,
            'error': 'Internal server error',
            'message': '伺服器內部錯誤'
        }), 500
    
    return api_bp


def create_email_web_blueprint(database: EmailDatabase = None) -> Blueprint:
    """
    建立郵件 Web 頁面 Blueprint
    
    Args:
        database: 郵件資料庫實例
        
    Returns:
        Flask Blueprint
    """
    web_bp = Blueprint('email_web', __name__)
    email_service = EmailWebService(database)
    
    @web_bp.route('/')
    def inbox():
        """郵件收件夾主頁面"""
        try:
            # 取得基本統計資訊
            stats = email_service.get_statistics_api()
            senders = email_service.get_senders_api()
            
            return render_template('email_inbox.html', 
                                 statistics=stats.get('data', {}),
                                 senders=senders.get('data', {}).get('senders', []))
        except Exception as e:
            logger = LoggerManager().get_logger("EmailWeb")
            logger.error(f"載入收件夾頁面失敗: {e}")
            return f"載入頁面失敗: {e}", 500
    
    @web_bp.route('/email/<int:email_id>')
    def email_detail(email_id: int):
        """郵件詳情頁面"""
        try:
            email = email_service.get_email_detail_api(email_id)
            
            if not email['success']:
                return f"找不到郵件: {email_id}", 404
            
            return render_template('email_detail.html', 
                                 email=email['data'])
        except Exception as e:
            logger = LoggerManager().get_logger("EmailWeb")
            logger.error(f"載入郵件詳情頁面失敗: {e}")
            return f"載入頁面失敗: {e}", 500
    
    return web_bp