#!/usr/bin/env python3
"""
EQC 統計計算模組
處理 Online EQC FAIL 和 EQC RT PASS 的統計計算
遵循 CLAUDE.md 功能替換原則，從主處理器中獨立出來
"""

import os
from datetime import datetime
from typing import Tuple, TYPE_CHECKING

if TYPE_CHECKING:
    from ..utils.timestamp_extractor import TimestampExtractor
    from ..monitoring.debug_logger import EQCDebugLogger


class EQCStatisticsCalculator:
    """
    EQC 統計數據計算器
    負責計算 Online EQC FAIL 和 EQC RT PASS 統計
    """
    
    def __init__(self, timestamp_extractor: 'TimestampExtractor', debug_logger: 'EQCDebugLogger', data_start_row: int = 12):
        """
        初始化統計計算器
        
        Args:
            timestamp_extractor: 時間戳提取器
            debug_logger: 調試日誌記錄器
            data_start_row: 資料起始行號
        """
        self.timestamp_extractor = timestamp_extractor
        self.debug_logger = debug_logger
        self.data_start_row = data_start_row
    
    def calculate_statistics_from_pairs(self, grouping_result) -> Tuple[int, int]:
        """
        直接基於配對結果計算統計數據
        Online EQC FAIL = 配對檔案中的失敗數量
        EQC RT PASS = 未配對檔案中的通過數量
        
        Args:
            grouping_result: FT-EQC配對結果物件
            
        Returns:
            Tuple[int, int]: (online_eqc_fail_count, eqc_rt_pass_count)
        """
        online_eqc_fail_count = 0
        eqc_rt_pass_count = 0
        
        # 統計 Online EQC FAIL 數量（配對檔案中的失敗）
        print("🔍 計算 Online EQC FAIL 統計:")
        self.debug_logger.log_section("Online EQC FAIL 統計 (配對檔案)")
        
        for ft_file, eqc_file in grouping_result.matched_pairs:
            filename = os.path.basename(eqc_file)
            try:
                with open(eqc_file, 'r', encoding='utf-8') as f:
                    lines = f.readlines()
                
                file_fail_count, file_pass_count = self._analyze_eqc_file_bins(lines)
                online_eqc_fail_count += file_fail_count
                total_count = file_pass_count + file_fail_count
                
                # 記錄到詳細日誌
                self.debug_logger.log_statistics("ONLINE EQC", eqc_file, file_pass_count, file_fail_count, total_count)
                
                if file_fail_count > 0:
                    print(f"   📄 {filename}: {file_fail_count} 個 FAIL")
                
            except Exception as e:
                print(f"   ❌ {filename}: 讀取失敗 - {e}")
        
        # 統計 EQC RT PASS 數量（未配對檔案中的通過）
        print("🔍 計算 EQC RT PASS 統計:")
        self.debug_logger.log_section("EQC RT PASS 統計 (未配對檔案)")
        
        for eqc_file in grouping_result.unmatched_eqc:
            filename = os.path.basename(eqc_file)
            try:
                with open(eqc_file, 'r', encoding='utf-8') as f:
                    lines = f.readlines()
                
                file_fail_count, file_pass_count = self._analyze_eqc_file_bins(lines)
                eqc_rt_pass_count += file_pass_count
                total_count = file_pass_count + file_fail_count
                
                # 記錄到詳細日誌
                self.debug_logger.log_statistics("EQC RT", eqc_file, file_pass_count, file_fail_count, total_count)
                
                timestamp = self.timestamp_extractor.extract_internal_timestamp(eqc_file)
                readable_time = self.timestamp_extractor.format_timestamp_readable(timestamp)
                print(f"   📄 {filename}: {file_pass_count} 個 PASS ({readable_time})")
                
            except Exception as e:
                print(f"   ❌ {filename}: 讀取失敗 - {e}")
        
        print(f"\n📊 統計結果:")
        print(f"   🔴 Online EQC FAIL: {online_eqc_fail_count} 個")
        print(f"   🟢 EQC RT PASS: {eqc_rt_pass_count} 個")
        
        return online_eqc_fail_count, eqc_rt_pass_count
    
    def _analyze_eqc_file_bins(self, lines: list) -> Tuple[int, int]:
        """
        分析EQC檔案中的BIN統計
        
        Args:
            lines: 檔案行內容列表
            
        Returns:
            Tuple[int, int]: (fail_count, pass_count)
        """
        file_pass_count = 0
        file_fail_count = 0
        
        # 從第13行開始檢查 (索引12)
        for i in range(self.data_start_row, len(lines)):
            line = lines[i].strip()
            if len(line) < 1:
                break
            
            elements = line.split(',')
            if len(elements) > 1:  # 確保有第2欄
                try:
                    # 檢查 BIN# (第2欄) - 恢復舊版本正確邏輯
                    bin_value = int(elements[1])
                    if bin_value != 1:
                        file_fail_count += 1
                    else:
                        file_pass_count += 1
                except (ValueError, IndexError):
                    continue
        
        return file_fail_count, file_pass_count
    
    def fill_eqc_bin1_statistics(self, content_lines: list, 
                                online_eqc_fail_count: int, eqc_rt_pass_count: int) -> list:
        """
        在 EQC BIN=1 內容中填入統計資料
        對應 3.2 文檔的統計填入功能
        
        Args:
            content_lines: 內容行列表
            online_eqc_fail_count: Online EQC FAIL數量
            eqc_rt_pass_count: EQC RT PASS數量
            
        Returns:
            list: 填入統計後的內容行
        """
        lines = content_lines.copy()
        
        # 確保有足夠行數，但不添加額外空行
        while len(lines) < 10:
            lines.append('')
        
        # 第9行處理 (索引8) - 只替換A欄和B欄，C欄之後保持原樣
        line9_elements = lines[8].strip().split(',') if len(lines) > 8 and lines[8].strip() else []
        # 確保有足夠的欄位
        while len(line9_elements) < 2:
            line9_elements.append("")
        
        line9_elements[0] = "OnlineEQC_Fail:"  # A9
        line9_elements[1] = str(online_eqc_fail_count)                 # B9
        # C欄之後保持原樣
        lines[8] = ','.join(line9_elements)
        
        # 第10行處理 (索引9) - 只替換A欄和B欄，C欄之後保持原樣
        line10_elements = lines[9].strip().split(',') if len(lines) > 9 and lines[9].strip() else []
        # 確保有足夠的欄位
        while len(line10_elements) < 2:
            line10_elements.append("")
        
        line10_elements[0] = "EQC_RT_FINAL_PASS:"  # A10
        line10_elements[1] = str(eqc_rt_pass_count)                    # B10
        # C欄之後保持原樣
        lines[9] = ','.join(line10_elements)
        
        return lines