# CLAUDE.md
# 檔案限制：≤500行，超過則立即精簡

## Project Overview

## Development Commands

### 🔴 虛擬環境啟動（強制第一步）
```bash
source venv/bin/activate     # Linux/Mac
venv\Scripts\activate        # Windows
which python                 # 確認虛擬環境
```

### Testing
```bash
pytest tests/ -v
python tools/run_pytest_rag.py --offline
```

## AI Programming Core Rules

**註明：所有回復都必須使用繁體中文**

### 0. Anti-Fake Testing Principle (Zero Tolerance)

#### 強制真實驗證
```bash
# 每次測試前後都必須執行
echo "=== 測試前狀態 ===" && ls -la target_files/ && date
# 執行功能
echo "=== 測試後狀態 ===" && ls -la target_files/ && date
# 必須看到檔案時間戳實際改變
```

#### 禁止行為（零容忍）
- ❌ **使用假資料測試**
- ❌ **假設API成功=功能成功**  
- ❌ **不等待真實處理時間就下結論**
- ❌ **用time.sleep()模擬真實處理**
- ❌ **急於展示結果而跳過實際驗證**

#### 強制檢查
- ✅ 檔案時間戳必須實際改變
- ✅ 檔案內容必須實際更新
- ✅ 進程必須實際執行完成
- ✅ 等待時間必須≥30秒（真實處理）
- ✅ 檢查進程狀態：`ps aux | grep process_name`

### 1. Function Replacement Principle (No Backward Compatibility)

#### 強制清理舊版本
```bash
# 必須執行清理檢查
grep -r "function_v1\|function_old\|function_backup" src/
grep -r "_v[0-9]\|_legacy" src/
# 發現任何舊版本必須立即刪除
```

#### 替換流程
1. 實現新功能
2. 測試新功能通過
3. **立即刪除舊版本** - 不保留任何功能重複的程式碼
4. 搜索確認無殘留：`grep -r "old_function_name"`

### 2. File Size Limit Enforcement

#### 500行強制限制
```bash
# 檔案開頭加入檢查
wc -l CLAUDE.md
if [ $(wc -l < CLAUDE.md) -gt 500 ]; then
    echo "錯誤：檔案超過500行限制"
    exit 1
fi
```

#### 超過限制處理
- 立即刪除冗長說明
- 移除重複內容
- 保留核心規則和指令
- 重新檢查確認≤500行

### 3. Selective TDD + Mandatory Backend Testing

#### 後端程式碼強制要求
- **先寫測試** → 測試失敗 → 寫程式碼 → 測試通過 → **執行程式測試驗證**
- **嚴禁修改測試讓程式通過**
- **必須執行實際程式測試**（不能只依賴單元測試）

#### 程式測試驗證（後端強制）
```bash
# API測試
curl -X POST 
# 功能測試  
python test_actual_
# 錯誤測試
python test_error_handling.py
```

### 4. Mandatory Checklist (Execute Every Time)

#### 反假測試檢查
- [ ] 記錄執行前檔案狀態：`ls -la && date`
- [ ] 執行功能並等待真實時間（≥30秒）
- [ ] 記錄執行後檔案狀態：`ls -la && date`
- [ ] 確認時間戳確實改變
- [ ] 檢查檔案內容確實更新
- [ ] 驗證進程確實執行完成

#### 功能清理檢查
- [ ] 搜索舊版本：`grep -r "_v[0-9]\|_old\|_backup"`
- [ ] 刪除所有舊版本程式碼
- [ ] 確保只有一個版本存在
- [ ] 測試新版本功能正常

#### 檔案大小檢查
- [ ] 檢查檔案行數：`wc -l *.md *.py`
- [ ] 如果超過500行，立即精簡
- [ ] 保留核心規則，刪除冗長說明
- [ ] 重新檢查確認≤500行

#### 後端測試檢查
- [ ] 後端程式碼是否先寫了測試？
- [ ] 測試是否先失敗了？
- [ ] 是否執行了程式測試驗證？
- [ ] API端點是否實際測試過？
- [ ] 是否使用繁體中文回應？

### 5. Prohibition List (Zero Tolerance)

#### 反假測試禁令
- ❌ **使用假資料進行測試**（零容忍禁止）
- ❌ **假裝API成功等於功能成功**（零容忍禁止）
- ❌ **不等待真實處理時間就下結論**（零容忍禁止）
- ❌ **使用模擬結果代替真實驗證**（零容忍禁止）
- ❌ **急於展示成功而跳過實際驗證**（零容忍禁止）

#### 功能替換禁令
- ❌ **保留功能重複的舊版本函式**（強制禁止）
- ❌ **建立功能相同但名稱不同的函式**（強制禁止）
- ❌ **為了向下相容而保留廢棄功能**（強制禁止）

#### 測試誠實性禁令
- ❌ **修改測試邏輯讓程式通過**（強制禁止）
- ❌ **降低測試標準或跳過邊界測試**（強制禁止）
- ❌ **後端程式碼只做單元測試不做程式測試**（強制禁止）
- ❌ **API端點未實際測試就交付**（強制禁止）

#### 其他禁令
- ❌ **使用英文或簡體中文回應**
- ❌ **提交未經程式測試驗證的後端程式碼**
- ❌ **檔案超過500行而不精簡**

## Violation Auto-Detection Script

建立自動檢測腳本防止違規：

```bash
#!/bin/bash
# anti_fake_check.sh - 反假測試檢查腳本

echo "🔍 檢查假測試跡象..."

# 檢查模擬代碼
if grep -r "time.sleep\|mock\|fake\|simulate" *.py; then
    echo "❌ 發現模擬代碼，可能是假測試"
    exit 1
fi

# 檢查功能重複
if grep -r "_v[0-9]\|_old\|_backup\|_legacy" src/; then
    echo "❌ 發現舊版本函式未清理"
    exit 1
fi

# 檢查檔案大小
for file in *.md *.py; do
    lines=$(wc -l < "$file")
    if [ "$lines" -gt 500 ]; then
        echo "❌ $file 超過500行限制 ($lines 行)"
        exit 1
    fi
done

echo "✅ 所有檢查通過"
```

## Core Configuration

### Environment Variables
- `AI_PROVIDER`: 'ollama' or 'grok'
- `EMBEDDING_MODEL`: sentence-transformers/all-MiniLM-L6-v2
- `EXPECTED_VECTOR_DIMENSION`: 384

### Test Commands


## API Endpoints

### Core Endpoints


### Config Management


## File Management Rules

### 500-Line Limit
- 所有檔案必須≤500行
- 超過立即精簡或拆分
- 定期檢查：`wc -l *.md *.py`

### NOW Work Tracking
- Current program: `now/current.md`
- Auto-archive: `now/archive/now0.md` → `now1.md` → `now2.md`
- Track active development and line count
- Trigger new tracking when exceeding 500-line limit

### Cleanup Rules
- 測試檔案用完即刪：`rm test_*.py simple_*.py`
- 備份檔案不保留：`rm *.backup *.bak`
- 日誌檔案定期清理：`rm *.log`

---
**檔案行數限制：≤500行**
**最後更新：遵循功能替換原則，所有舊版本已清理**
**註明：所有AI回復都必須使用繁體中文**