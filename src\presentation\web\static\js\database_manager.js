/**
 * 資料庫管理前端邏輯
 */

class DatabaseManager {
    constructor() {
        this.currentTable = null;
        this.dataTable = null;
        this.init();
    }

    init() {
        this.bindEvents();
        this.loadDatabaseInfo();
    }

    bindEvents() {
        // 表格選擇
        $('#table-select').on('change', (e) => {
            const tableName = e.target.value;
            if (tableName) {
                this.loadTableData(tableName);
            } else {
                this.clearTableView();
            }
        });

        // 重新整理按鈕
        $('#refresh-btn').on('click', () => {
            if (this.currentTable) {
                this.loadTableData(this.currentTable);
            }
        });

        // 匯出 CSV 按鈕
        $('#export-csv-btn').on('click', () => {
            if (this.currentTable) {
                this.exportTableToCsv(this.currentTable);
            }
        });

        // 執行查詢按鈕
        $('#execute-query-btn').on('click', () => {
            this.executeQuery();
        });
    }

    async loadDatabaseInfo() {
        try {
            const response = await fetch('/api/database/info');
            const result = await response.json();

            if (result.success) {
                const data = result.data;
                // 顯示資料庫大小
                $('#db-size').text(this.formatBytes(data.db_size));
                
                // 顯示表格記錄數
                for (const [table, count] of Object.entries(data.tables)) {
                    $(`#table-select option[value="${table}"]`).text(`${table} - ${this.getTableDisplayName(table)} (${count} 筆記錄)`);
                }
            } else {
                this.showError('載入資料庫資訊失敗');
            }
        } catch (error) {
            console.error('載入資料庫資訊失敗:', error);
            this.showError('載入資料庫資訊失敗');
        }
    }

    async loadTableData(tableName) {
        this.showLoading();
        this.currentTable = tableName;

        try {
            const response = await fetch(`/api/database/table/${tableName}`);
            const result = await response.json();

            if (result.success) {
                this.renderTable(result.data);
                $('#export-csv-btn').prop('disabled', false);
                $('#table-info').removeClass('hidden');
                $('#table-name').text(`${tableName} - ${this.getTableDisplayName(tableName)}`);
                $('#record-count').text(result.data.total);
                $('#column-count').text(result.data.columns.length);
            } else {
                this.showError('載入表格資料失敗: ' + result.error);
            }
        } catch (error) {
            console.error('載入表格資料失敗:', error);
            this.showError('載入表格資料失敗');
        } finally {
            this.hideLoading();
        }
    }

    renderTable(data) {
        // 清理舊表格
        if (this.dataTable) {
            this.dataTable.destroy();
            $('#data-table').empty();
        }

        // 準備欄位配置
        const columns = data.columns.map(col => ({
            data: col.name,
            title: this.getColumnDisplayName(col.name),
            defaultContent: '',
            render: (value, type, row) => {
                if (value === null || value === undefined) {
                    return '<span class="null-value">NULL</span>';
                }
                // 處理長文本
                if (type === 'display' && typeof value === 'string' && value.length > 50) {
                    return `<span title="${this.escapeHtml(value)}">${this.escapeHtml(value.substring(0, 50))}...</span>`;
                }
                return this.escapeHtml(value);
            }
        }));

        // 添加操作欄
        columns.push({
            data: null,
            title: '操作',
            orderable: false,
            render: (data, type, row) => {
                return `<button class="btn-view-detail" data-row='${JSON.stringify(row)}'>查看</button>`;
            }
        });

        // 初始化 DataTable
        this.dataTable = $('#data-table').DataTable({
            data: data.records,
            columns: columns,
            pageLength: 25,
            lengthMenu: [[10, 25, 50, 100], [10, 25, 50, 100]],
            language: {
                url: '//cdn.datatables.net/plug-ins/1.13.6/i18n/zh-HANT.json'
            },
            scrollX: true,
            autoWidth: false,
            drawCallback: () => {
                // 綁定查看按鈕事件
                $('.btn-view-detail').on('click', (e) => {
                    const row = JSON.parse($(e.target).attr('data-row'));
                    this.showDetailModal(row);
                });
            }
        });
    }

    showDetailModal(row) {
        const modalBody = $('#modal-body');
        modalBody.empty();

        // 生成詳情內容
        const table = $('<table class="detail-table"></table>');
        for (const [key, value] of Object.entries(row)) {
            const displayKey = this.getColumnDisplayName(key);
            const displayValue = value === null ? '<span class="null-value">NULL</span>' : 
                               (typeof value === 'string' && value.length > 100 ? 
                                `<pre>${this.escapeHtml(value)}</pre>` : 
                                this.escapeHtml(value));
            
            table.append(`
                <tr>
                    <td class="detail-key">${displayKey}</td>
                    <td class="detail-value">${displayValue}</td>
                </tr>
            `);
        }
        modalBody.append(table);

        $('#modal-title').text(`${this.currentTable} 記錄詳情`);
        $('#detail-modal').removeClass('hidden');
    }

    async executeQuery() {
        const query = $('#sql-query').val().trim();
        if (!query) {
            this.showError('請輸入查詢語句');
            return;
        }

        this.showLoading();
        $('#query-error').addClass('hidden');

        try {
            const response = await fetch('/api/database/execute', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ query })
            });

            const result = await response.json();

            if (result.success) {
                this.renderTable(result.data);
                $('#table-info').removeClass('hidden');
                $('#table-name').text('查詢結果');
                $('#record-count').text(result.data.total);
                $('#column-count').text(result.data.columns.length);
                $('#export-csv-btn').prop('disabled', true); // 查詢結果不支援匯出
            } else {
                $('#query-error').text(result.error).removeClass('hidden');
            }
        } catch (error) {
            console.error('執行查詢失敗:', error);
            $('#query-error').text('執行查詢失敗').removeClass('hidden');
        } finally {
            this.hideLoading();
        }
    }

    exportTableToCsv(tableName) {
        window.location.href = `/api/database/export/${tableName}`;
    }

    clearTableView() {
        if (this.dataTable) {
            this.dataTable.destroy();
            $('#data-table').empty();
        }
        $('#table-info').addClass('hidden');
        $('#export-csv-btn').prop('disabled', true);
        this.currentTable = null;
    }

    showLoading() {
        $('#loading').removeClass('hidden');
    }

    hideLoading() {
        $('#loading').addClass('hidden');
    }

    showError(message) {
        alert(message); // 可以改為更好看的提示
    }

    formatBytes(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    escapeHtml(text) {
        if (text === null || text === undefined) return '';
        return String(text)
            .replace(/&/g, '&amp;')
            .replace(/</g, '&lt;')
            .replace(/>/g, '&gt;')
            .replace(/"/g, '&quot;')
            .replace(/'/g, '&#039;');
    }

    getTableDisplayName(tableName) {
        const names = {
            'emails': '郵件',
            'senders': '寄件者',
            'attachments': '附件',
            'email_process_status': '處理狀態'
        };
        return names[tableName] || tableName;
    }

    getColumnDisplayName(columnName) {
        const names = {
            'id': 'ID',
            'message_id': '郵件 ID',
            'sender': '寄件者',
            'sender_display_name': '寄件者名稱',
            'subject': '主旨',
            'body': '內容',
            'received_time': '接收時間',
            'created_at': '創建時間',
            'updated_at': '更新時間',
            'is_read': '已讀',
            'is_processed': '已處理',
            'has_attachments': '有附件',
            'attachment_count': '附件數',
            'email_address': '郵件地址',
            'display_name': '顯示名稱',
            'total_emails': '郵件總數',
            'last_email_time': '最後郵件時間',
            'first_email_time': '首封郵件時間',
            'email_id': '郵件 ID',
            'filename': '檔名',
            'content_type': '內容類型',
            'size_bytes': '大小 (位元組)',
            'file_path': '檔案路徑',
            'checksum': '校驗碼',
            'step_name': '步驟名稱',
            'status': '狀態',
            'started_at': '開始時間',
            'completed_at': '完成時間',
            'error_message': '錯誤訊息',
            'output_files': '輸出檔案',
            'progress_percentage': '進度百分比'
        };
        return names[columnName] || columnName;
    }
}

// 全域函數
function closeDetailModal() {
    $('#detail-modal').addClass('hidden');
}

// 初始化
$(document).ready(() => {
    new DatabaseManager();
});