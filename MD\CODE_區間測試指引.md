# CODE 區間設定測試指引

## 🔍 問題分析結果

**根本原因:** 用戶在前端 CODE 區間輸入框中沒有填入任何值，導致所有參數都是 `null`，後端判斷條件不滿足，最終使用了預設的自動檢測區間（298-335 和 1565-1600）。

## 🛠️ 已實施的改進

### 1. 前端 UI 改進
- ✅ 增加了使用說明提示框
- ✅ 更新了輸入框的 placeholder 顯示預設值
- ✅ 改進了欄位計數顯示，清楚顯示當前狀態
- ✅ 增強了參數收集邏輯和詳細日誌

### 2. 參數處理改進
- ✅ 修正了前端空值處理邏輯
- ✅ 增加了詳細的 console.log 輸出
- ✅ 明確顯示是否使用自定義設定

## 🧪 測試步驟

### 測試案例 1: 使用預設區間
1. 開啟 EQC UI 介面
2. **不要填入任何 CODE 區間值**
3. 執行一鍵完成處理
4. 檢查瀏覽器 Console 輸出，應該看到:
   ```
   📊 CODE區間設定收集結果:
      原始輸入值: {mainStart: "(空)", mainEnd: "(空)", ...}
      處理後參數: {main_start: null, main_end: null, ...}
      使用自定義設定: ❌ 否，將使用預設區間 (298-335, 1565-1600)
   🔧 將使用自動檢測的預設 CODE 區間設定
   ```

### 測試案例 2: 使用自定義區間
1. 填入自定義值：
   - 主要 CODE 區間: 300-400
   - 備用 CODE 區間: 1600-1700
2. 執行一鍵完成處理
3. 檢查瀏覽器 Console 輸出，應該看到:
   ```
   📊 CODE區間設定收集結果:
      原始輸入值: {mainStart: "300", mainEnd: "400", ...}
      處理後參數: {main_start: 300, main_end: 400, ...}
      使用自定義設定: ✅ 是
   🎯 將覆蓋預設 CODE 區間設定
   ```

### 測試案例 3: 部分填入（預期使用預設）
1. 只填入主要區間起始值: 300
2. 其他欄位留空
3. 檢查 Console 輸出應該顯示使用預設區間

## 🔍 驗證方法

### 前端驗證
1. 觀察輸入框下方的狀態提示
2. 檢查瀏覽器 Console 日誌
3. 確認 "使用自定義設定" 的狀態

### 後端驗證
1. 檢查後端日誌中的:
   ```
   🎯 收到前端 CODE 區間設定: {...}
   🎯 使用前端自定義 CODE 區間設定  # 或
   🔧 使用自動檢測的 CODE 區間設定
   ```

### 最終結果驗證
1. 檢查生成的 Excel 檔案
2. 確認搜尋區間是否符合設定
3. 檢查處理日誌中的實際使用區間

## 🎯 預期行為

| 輸入狀態 | 前端顯示 | 後端處理 | 實際區間 |
|---------|---------|---------|---------|
| 全部留空 | "使用預設區間" | 自動檢測 | 298-335, 1565-1600 |
| 部分填入 | "請填入完整區間" | 自動檢測 | 298-335, 1565-1600 |
| 完整填入 | "自定義區間: X 個欄位" | 使用自定義 | 用戶設定值 |

## 📝 注意事項

1. **前端已清楚標示預設值**，用戶了解留空的行為
2. **增強了日誌輸出**，便於追蹤問題
3. **保持了向後相容性**，原有功能不受影響
4. **符合 CLAUDE.md 原則**：功能替換、變數重用、極簡程式碼