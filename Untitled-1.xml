<svg aria-roledescription="flowchart-v2" role="graphics-document document" viewBox="0 0 8112.48828125 975.3795776367188" style="max-width: 8112.48828125px;" class="flowchart" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns="http://www.w3.org/2000/svg" width="100%" id="mermaid-b5dea2fa-dc08-4420-b63f-b4b946e2d77f"><style>#mermaid-b5dea2fa-dc08-4420-b63f-b4b946e2d77f{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;fill:#ccc;}#mermaid-b5dea2fa-dc08-4420-b63f-b4b946e2d77f .error-icon{fill:#a44141;}#mermaid-b5dea2fa-dc08-4420-b63f-b4b946e2d77f .error-text{fill:#ddd;stroke:#ddd;}#mermaid-b5dea2fa-dc08-4420-b63f-b4b946e2d77f .edge-thickness-normal{stroke-width:1px;}#mermaid-b5dea2fa-dc08-4420-b63f-b4b946e2d77f .edge-thickness-thick{stroke-width:3.5px;}#mermaid-b5dea2fa-dc08-4420-b63f-b4b946e2d77f .edge-pattern-solid{stroke-dasharray:0;}#mermaid-b5dea2fa-dc08-4420-b63f-b4b946e2d77f .edge-thickness-invisible{stroke-width:0;fill:none;}#mermaid-b5dea2fa-dc08-4420-b63f-b4b946e2d77f .edge-pattern-dashed{stroke-dasharray:3;}#mermaid-b5dea2fa-dc08-4420-b63f-b4b946e2d77f .edge-pattern-dotted{stroke-dasharray:2;}#mermaid-b5dea2fa-dc08-4420-b63f-b4b946e2d77f .marker{fill:lightgrey;stroke:lightgrey;}#mermaid-b5dea2fa-dc08-4420-b63f-b4b946e2d77f .marker.cross{stroke:lightgrey;}#mermaid-b5dea2fa-dc08-4420-b63f-b4b946e2d77f svg{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;}#mermaid-b5dea2fa-dc08-4420-b63f-b4b946e2d77f p{margin:0;}#mermaid-b5dea2fa-dc08-4420-b63f-b4b946e2d77f .label{font-family:"trebuchet ms",verdana,arial,sans-serif;color:#ccc;}#mermaid-b5dea2fa-dc08-4420-b63f-b4b946e2d77f .cluster-label text{fill:#F9FFFE;}#mermaid-b5dea2fa-dc08-4420-b63f-b4b946e2d77f .cluster-label span{color:#F9FFFE;}#mermaid-b5dea2fa-dc08-4420-b63f-b4b946e2d77f .cluster-label span p{background-color:transparent;}#mermaid-b5dea2fa-dc08-4420-b63f-b4b946e2d77f .label text,#mermaid-b5dea2fa-dc08-4420-b63f-b4b946e2d77f span{fill:#ccc;color:#ccc;}#mermaid-b5dea2fa-dc08-4420-b63f-b4b946e2d77f .node rect,#mermaid-b5dea2fa-dc08-4420-b63f-b4b946e2d77f .node circle,#mermaid-b5dea2fa-dc08-4420-b63f-b4b946e2d77f .node ellipse,#mermaid-b5dea2fa-dc08-4420-b63f-b4b946e2d77f .node polygon,#mermaid-b5dea2fa-dc08-4420-b63f-b4b946e2d77f .node path{fill:#1f2020;stroke:#ccc;stroke-width:1px;}#mermaid-b5dea2fa-dc08-4420-b63f-b4b946e2d77f .rough-node .label text,#mermaid-b5dea2fa-dc08-4420-b63f-b4b946e2d77f .node .label text,#mermaid-b5dea2fa-dc08-4420-b63f-b4b946e2d77f .image-shape .label,#mermaid-b5dea2fa-dc08-4420-b63f-b4b946e2d77f .icon-shape .label{text-anchor:middle;}#mermaid-b5dea2fa-dc08-4420-b63f-b4b946e2d77f .node .katex path{fill:#000;stroke:#000;stroke-width:1px;}#mermaid-b5dea2fa-dc08-4420-b63f-b4b946e2d77f .rough-node .label,#mermaid-b5dea2fa-dc08-4420-b63f-b4b946e2d77f .node .label,#mermaid-b5dea2fa-dc08-4420-b63f-b4b946e2d77f .image-shape .label,#mermaid-b5dea2fa-dc08-4420-b63f-b4b946e2d77f .icon-shape .label{text-align:center;}#mermaid-b5dea2fa-dc08-4420-b63f-b4b946e2d77f .node.clickable{cursor:pointer;}#mermaid-b5dea2fa-dc08-4420-b63f-b4b946e2d77f .root .anchor path{fill:lightgrey!important;stroke-width:0;stroke:lightgrey;}#mermaid-b5dea2fa-dc08-4420-b63f-b4b946e2d77f .arrowheadPath{fill:lightgrey;}#mermaid-b5dea2fa-dc08-4420-b63f-b4b946e2d77f .edgePath .path{stroke:lightgrey;stroke-width:2.0px;}#mermaid-b5dea2fa-dc08-4420-b63f-b4b946e2d77f .flowchart-link{stroke:lightgrey;fill:none;}#mermaid-b5dea2fa-dc08-4420-b63f-b4b946e2d77f .edgeLabel{background-color:hsl(0, 0%, 34.4117647059%);text-align:center;}#mermaid-b5dea2fa-dc08-4420-b63f-b4b946e2d77f .edgeLabel p{background-color:hsl(0, 0%, 34.4117647059%);}#mermaid-b5dea2fa-dc08-4420-b63f-b4b946e2d77f .edgeLabel rect{opacity:0.5;background-color:hsl(0, 0%, 34.4117647059%);fill:hsl(0, 0%, 34.4117647059%);}#mermaid-b5dea2fa-dc08-4420-b63f-b4b946e2d77f .labelBkg{background-color:rgba(87.75, 87.75, 87.75, 0.5);}#mermaid-b5dea2fa-dc08-4420-b63f-b4b946e2d77f .cluster rect{fill:hsl(180, 1.5873015873%, 28.3529411765%);stroke:rgba(255, 255, 255, 0.25);stroke-width:1px;}#mermaid-b5dea2fa-dc08-4420-b63f-b4b946e2d77f .cluster text{fill:#F9FFFE;}#mermaid-b5dea2fa-dc08-4420-b63f-b4b946e2d77f .cluster span{color:#F9FFFE;}#mermaid-b5dea2fa-dc08-4420-b63f-b4b946e2d77f div.mermaidTooltip{position:absolute;text-align:center;max-width:200px;padding:2px;font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:12px;background:hsl(20, 1.5873015873%, 12.3529411765%);border:1px solid rgba(255, 255, 255, 0.25);border-radius:2px;pointer-events:none;z-index:100;}#mermaid-b5dea2fa-dc08-4420-b63f-b4b946e2d77f .flowchartTitleText{text-anchor:middle;font-size:18px;fill:#ccc;}#mermaid-b5dea2fa-dc08-4420-b63f-b4b946e2d77f rect.text{fill:none;stroke-width:0;}#mermaid-b5dea2fa-dc08-4420-b63f-b4b946e2d77f .icon-shape,#mermaid-b5dea2fa-dc08-4420-b63f-b4b946e2d77f .image-shape{background-color:hsl(0, 0%, 34.4117647059%);text-align:center;}#mermaid-b5dea2fa-dc08-4420-b63f-b4b946e2d77f .icon-shape p,#mermaid-b5dea2fa-dc08-4420-b63f-b4b946e2d77f .image-shape p{background-color:hsl(0, 0%, 34.4117647059%);padding:2px;}#mermaid-b5dea2fa-dc08-4420-b63f-b4b946e2d77f .icon-shape rect,#mermaid-b5dea2fa-dc08-4420-b63f-b4b946e2d77f .image-shape rect{opacity:0.5;background-color:hsl(0, 0%, 34.4117647059%);fill:hsl(0, 0%, 34.4117647059%);}#mermaid-b5dea2fa-dc08-4420-b63f-b4b946e2d77f :root{--mermaid-font-family:"trebuchet ms",verdana,arial,sans-serif;}#mermaid-b5dea2fa-dc08-4420-b63f-b4b946e2d77f .presentation&gt;*{fill:#e1f5fe!important;}#mermaid-b5dea2fa-dc08-4420-b63f-b4b946e2d77f .presentation span{fill:#e1f5fe!important;}#mermaid-b5dea2fa-dc08-4420-b63f-b4b946e2d77f .application&gt;*{fill:#f3e5f5!important;}#mermaid-b5dea2fa-dc08-4420-b63f-b4b946e2d77f .application span{fill:#f3e5f5!important;}#mermaid-b5dea2fa-dc08-4420-b63f-b4b946e2d77f .domain&gt;*{fill:#e8f5e8!important;}#mermaid-b5dea2fa-dc08-4420-b63f-b4b946e2d77f .domain span{fill:#e8f5e8!important;}#mermaid-b5dea2fa-dc08-4420-b63f-b4b946e2d77f .datamodel&gt;*{fill:#fff3e0!important;}#mermaid-b5dea2fa-dc08-4420-b63f-b4b946e2d77f .datamodel span{fill:#fff3e0!important;}#mermaid-b5dea2fa-dc08-4420-b63f-b4b946e2d77f .infrastructure&gt;*{fill:#fce4ec!important;}#mermaid-b5dea2fa-dc08-4420-b63f-b4b946e2d77f .infrastructure span{fill:#fce4ec!important;}#mermaid-b5dea2fa-dc08-4420-b63f-b4b946e2d77f .services&gt;*{fill:#f1f8e9!important;}#mermaid-b5dea2fa-dc08-4420-b63f-b4b946e2d77f .services span{fill:#f1f8e9!important;}#mermaid-b5dea2fa-dc08-4420-b63f-b4b946e2d77f .external&gt;*{fill:#f5f5f5!important;}#mermaid-b5dea2fa-dc08-4420-b63f-b4b946e2d77f .external span{fill:#f5f5f5!important;}</style><g><marker orient="auto" markerHeight="8" markerWidth="8" markerUnits="userSpaceOnUse" refY="5" refX="5" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-b5dea2fa-dc08-4420-b63f-b4b946e2d77f_flowchart-v2-pointEnd"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 0 L 10 5 L 0 10 z"></path></marker><marker orient="auto" markerHeight="8" markerWidth="8" markerUnits="userSpaceOnUse" refY="5" refX="4.5" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-b5dea2fa-dc08-4420-b63f-b4b946e2d77f_flowchart-v2-pointStart"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 5 L 10 10 L 10 0 z"></path></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="11" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-b5dea2fa-dc08-4420-b63f-b4b946e2d77f_flowchart-v2-circleEnd"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"></circle></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="-1" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-b5dea2fa-dc08-4420-b63f-b4b946e2d77f_flowchart-v2-circleStart"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"></circle></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="12" viewBox="0 0 11 11" class="marker cross flowchart-v2" id="mermaid-b5dea2fa-dc08-4420-b63f-b4b946e2d77f_flowchart-v2-crossEnd"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"></path></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="-1" viewBox="0 0 11 11" class="marker cross flowchart-v2" id="mermaid-b5dea2fa-dc08-4420-b63f-b4b946e2d77f_flowchart-v2-crossStart"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"></path></marker><g class="root"><g class="clusters"><g data-look="classic" id="subGraph16" class="cluster"><rect height="168.37955474853516" width="999.6875" y="645" x="1090.01953125" style=""></rect><g transform="translate(1489.86328125, 645)" class="cluster-label"><foreignObject height="48" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>外部系統 (External Systems)</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph15" class="cluster"><rect height="383" width="228.375" y="212" x="1307.02734375" style=""></rect><g transform="translate(1359.84765625, 212)" class="cluster-label"><foreignObject height="24" width="122.734375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>服務層 (Services)</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph14" class="cluster"><rect height="551.3795547485352" width="5110.40625" y="416" x="2109.70703125" style=""></rect><g transform="translate(4564.91015625, 416)" class="cluster-label"><foreignObject height="48" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>基礎設施層 (Infrastructure Layer)</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph6" class="cluster"><rect height="601.3795547485352" width="898.16015625" y="212" x="171.859375" style=""></rect><g transform="translate(529.369140625, 212)" class="cluster-label"><foreignObject height="24" width="183.140625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>資料模型層 (Data Models)</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph5" class="cluster"><rect height="179" width="864.375" y="416" x="7240.11328125" style=""></rect><g transform="translate(7591.23828125, 416)" class="cluster-label"><foreignObject height="24" width="162.125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>領域層 (Domain Layer)</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph4" class="cluster"><rect height="154" width="4885.26953125" y="212" x="3025.28125" style=""></rect><g transform="translate(5372.994140625, 212)" class="cluster-label"><foreignObject height="24" width="189.84375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>應用層 (Application Layer)</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph1" class="cluster"><rect height="154" width="6241.98046875" y="8" x="8" style=""></rect><g transform="translate(3029.599609375, 8)" class="cluster-label"><foreignObject height="24" width="198.78125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>表現層 (Presentation Layer)</p></span></div></foreignObject></g></g><g data-look="classic" id="配置與日誌" class="cluster"><rect height="501.37955474853516" width="242.40625" y="441" x="2130.953125" style=""></rect><g transform="translate(2212.15625, 441)" class="cluster-label"><foreignObject height="24" width="80"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>配置與日誌</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph12" class="cluster"><rect height="347.37955474853516" width="998.171875" y="441" x="6201.94140625" style=""></rect><g transform="translate(6643.39453125, 441)" class="cluster-label"><foreignObject height="24" width="115.265625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>解析器 (Parsers)</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph11" class="cluster"><rect height="347.37955474853516" width="3788.58203125" y="441" x="2393.359375" style=""></rect><g transform="translate(4223.642578125, 441)" class="cluster-label"><foreignObject height="24" width="128.015625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>適配器 (Adapters)</p></span></div></foreignObject></g></g><g data-look="classic" id="檔案處理" class="cluster"><rect height="104" width="1036.625" y="466" x="3688.53125" style=""></rect><g transform="translate(4174.84375, 466)" class="cluster-label"><foreignObject height="24" width="64"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>檔案處理</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph9" class="cluster"><rect height="104" width="1255.171875" y="466" x="2413.359375" style=""></rect><g transform="translate(3003.5546875, 466)" class="cluster-label"><foreignObject height="24" width="74.78125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Excel 處理</p></span></div></foreignObject></g></g><g data-look="classic" id="郵件服務" class="cluster"><rect height="104" width="1140" y="466" x="5021.94140625" style=""></rect><g transform="translate(5559.94140625, 466)" class="cluster-label"><foreignObject height="24" width="64"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>郵件服務</p></span></div></foreignObject></g></g><g data-look="classic" id="資料庫" class="cluster"><rect height="297.37955474853516" width="256.78515625" y="466" x="4745.15625" style=""></rect><g transform="translate(4849.548828125, 466)" class="cluster-label"><foreignObject height="24" width="48"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>資料庫</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph3" class="cluster"><rect height="104" width="555.390625" y="237" x="3045.28125" style=""></rect><g transform="translate(3246.7265625, 237)" class="cluster-label"><foreignObject height="24" width="152.5"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>介面定義 (Interfaces)</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph2" class="cluster"><rect height="104" width="4093.7734375" y="237" x="3796.77734375" style=""></rect><g transform="translate(5768.3515625, 237)" class="cluster-label"><foreignObject height="24" width="150.625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>使用案例 (Use Cases)</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph0" class="cluster"><rect height="104" width="3125.58203125" y="33" x="28" style=""></rect><g transform="translate(1560.970703125, 33)" class="cluster-label"><foreignObject height="24" width="59.640625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>API 服務</p></span></div></foreignObject></g></g></g><g class="edgePaths"><path marker-end="url(#mermaid-b5dea2fa-dc08-4420-b63f-b4b946e2d77f_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_API_EMAILPROC_0" d="M3836.777,112L3836.777,116.167C3836.777,120.333,3836.777,128.667,3836.777,137C3836.777,145.333,3836.777,153.667,3836.777,162C3836.777,170.333,3836.777,178.667,3836.777,187C3836.777,195.333,3836.777,203.667,3836.777,212C3836.777,220.333,3836.777,228.667,4170.955,241.128C4505.133,253.59,5173.489,270.179,5507.667,278.474L5841.845,286.769"></path><path marker-end="url(#mermaid-b5dea2fa-dc08-4420-b63f-b4b946e2d77f_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_CLI_EMAILPROC_1" d="M4979.887,112L4979.887,116.167C4979.887,120.333,4979.887,128.667,4979.887,137C4979.887,145.333,4979.887,153.667,4979.887,162C4979.887,170.333,4979.887,178.667,4979.887,187C4979.887,195.333,4979.887,203.667,4979.887,212C4979.887,220.333,4979.887,228.667,5123.547,240.682C5267.208,252.697,5554.529,268.394,5698.189,276.242L5841.85,284.09"></path><path marker-end="url(#mermaid-b5dea2fa-dc08-4420-b63f-b4b946e2d77f_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_WEB_EMAILPROC_2" d="M6134.574,112L6134.574,116.167C6134.574,120.333,6134.574,128.667,6134.574,137C6134.574,145.333,6134.574,153.667,6134.574,162C6134.574,170.333,6134.574,178.667,6134.574,187C6134.574,195.333,6134.574,203.667,6134.574,212C6134.574,220.333,6134.574,228.667,6115.723,237.666C6096.872,246.665,6059.17,256.329,6040.319,261.161L6021.468,265.994"></path><path marker-end="url(#mermaid-b5dea2fa-dc08-4420-b63f-b4b946e2d77f_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_FTEQC_EQCPROC2_3" d="M131.859,112L131.859,116.167C131.859,120.333,131.859,128.667,131.859,137C131.859,145.333,131.859,153.667,131.859,162C131.859,170.333,131.859,178.667,131.859,187C131.859,195.333,131.859,203.667,131.859,212C131.859,220.333,131.859,228.667,131.859,241.5C131.859,254.333,131.859,271.667,131.859,289C131.859,306.333,131.859,323.667,131.859,336.5C131.859,349.333,131.859,357.667,131.859,366C131.859,374.333,131.859,382.667,539.92,391C947.98,399.333,1764.102,407.667,2244.122,416C2724.143,424.333,2868.064,432.667,2940.024,441C3011.984,449.333,3011.984,457.667,3011.984,465.333C3011.984,473,3011.984,480,3011.984,483.5L3011.984,487"></path><path marker-end="url(#mermaid-b5dea2fa-dc08-4420-b63f-b4b946e2d77f_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_NETAPI_NETWORK_4" d="M1090.02,112L1090.02,116.167C1090.02,120.333,1090.02,128.667,1090.02,137C1090.02,145.333,1090.02,153.667,1090.02,162C1090.02,170.333,1090.02,178.667,1090.02,187C1090.02,195.333,1090.02,203.667,1090.02,212C1090.02,220.333,1090.02,228.667,1090.02,241.5C1090.02,254.333,1090.02,271.667,1090.02,289C1090.02,306.333,1090.02,323.667,1090.02,336.5C1090.02,349.333,1090.02,357.667,933.66,366C777.299,374.333,464.579,382.667,308.219,391C151.859,399.333,151.859,407.667,151.859,416C151.859,424.333,151.859,432.667,151.859,441C151.859,449.333,151.859,457.667,151.859,470.5C151.859,483.333,151.859,500.667,151.859,518C151.859,535.333,151.859,552.667,151.859,565.5C151.859,578.333,151.859,586.667,151.859,595C151.859,603.333,151.859,611.667,328.316,620C504.772,628.333,857.685,636.667,1034.141,645.532C1210.598,654.397,1210.598,663.793,1210.598,668.491L1210.598,673.19"></path><path marker-end="url(#mermaid-b5dea2fa-dc08-4420-b63f-b4b946e2d77f_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_CLEANUP_FILECLEAN_5" d="M1515.402,112L1515.402,116.167C1515.402,120.333,1515.402,128.667,1515.402,137C1515.402,145.333,1515.402,153.667,1515.402,162C1515.402,170.333,1515.402,178.667,1515.402,187C1515.402,195.333,1515.402,203.667,1515.402,212C1515.402,220.333,1515.402,228.667,1515.402,241.5C1515.402,254.333,1515.402,271.667,1515.402,289C1515.402,306.333,1515.402,323.667,1515.402,336.5C1515.402,349.333,1515.402,357.667,1515.402,366C1515.402,374.333,1515.402,382.667,1515.402,391C1515.402,399.333,1515.402,407.667,1515.402,416C1515.402,424.333,1515.402,432.667,1515.402,441C1515.402,449.333,1515.402,457.667,1507.965,465.693C1500.528,473.719,1485.653,481.438,1478.216,485.298L1470.778,489.158"></path><path marker-end="url(#mermaid-b5dea2fa-dc08-4420-b63f-b4b946e2d77f_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_FILEMGMT_FILEUPLOAD_6" d="M2999.332,112L2999.332,116.167C2999.332,120.333,2999.332,128.667,2999.332,137C2999.332,145.333,2999.332,153.667,2999.332,162C2999.332,170.333,2999.332,178.667,2999.332,187C2999.332,195.333,2999.332,203.667,2999.332,212C2999.332,220.333,2999.332,228.667,2999.332,241.5C2999.332,254.333,2999.332,271.667,2999.332,289C2999.332,306.333,2999.332,323.667,2999.332,336.5C2999.332,349.333,2999.332,357.667,2999.332,366C2999.332,374.333,2999.332,382.667,2999.332,391C2999.332,399.333,2999.332,407.667,3137.811,416C3276.289,424.333,3553.246,432.667,3691.725,441C3830.203,449.333,3830.203,457.667,3830.203,465.333C3830.203,473,3830.203,480,3830.203,483.5L3830.203,487"></path><path marker-end="url(#mermaid-b5dea2fa-dc08-4420-b63f-b4b946e2d77f_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_EMAILPROC_ENTITIES_7" d="M6017.594,291.433L6309.202,299.694C6600.811,307.955,7184.029,324.478,7475.637,336.905C7767.246,349.333,7767.246,357.667,7767.246,366C7767.246,374.333,7767.246,382.667,7767.246,391C7767.246,399.333,7767.246,407.667,7767.246,416C7767.246,424.333,7767.246,432.667,7767.246,441C7767.246,449.333,7767.246,457.667,7767.246,465.333C7767.246,473,7767.246,480,7767.246,483.5L7767.246,487"></path><path marker-end="url(#mermaid-b5dea2fa-dc08-4420-b63f-b4b946e2d77f_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_EMAILPROC_DOMAINSERV_8" d="M6017.594,292.119L6241.878,300.266C6466.163,308.413,6914.732,324.706,7139.016,337.02C7363.301,349.333,7363.301,357.667,7363.301,366C7363.301,374.333,7363.301,382.667,7363.301,391C7363.301,399.333,7363.301,407.667,7363.301,416C7363.301,424.333,7363.301,432.667,7363.301,441C7363.301,449.333,7363.301,457.667,7363.301,465.333C7363.301,473,7363.301,480,7363.301,483.5L7363.301,487"></path><path marker-end="url(#mermaid-b5dea2fa-dc08-4420-b63f-b4b946e2d77f_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_EMAILREADER_EMAILDATA_9" d="M3192.43,316L3192.43,320.167C3192.43,324.333,3192.43,332.667,3192.43,341C3192.43,349.333,3192.43,357.667,2783.604,366C2374.779,374.333,1557.128,382.667,1148.302,391C739.477,399.333,739.477,407.667,739.477,416C739.477,424.333,739.477,432.667,739.477,441C739.477,449.333,739.477,457.667,679.068,469.053C618.66,480.44,497.843,494.879,437.435,502.099L377.026,509.319"></path><path marker-end="url(#mermaid-b5dea2fa-dc08-4420-b63f-b4b946e2d77f_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_TASKQUEUE_TASKDATA_10" d="M3460.125,316L3460.125,320.167C3460.125,324.333,3460.125,332.667,3460.125,341C3460.125,349.333,3460.125,357.667,3045.27,366C2630.415,374.333,1800.706,382.667,1385.851,391C970.996,399.333,970.996,407.667,970.996,416C970.996,424.333,970.996,432.667,970.996,441C970.996,449.333,970.996,457.667,970.996,465.333C970.996,473,970.996,480,970.996,483.5L970.996,487"></path><path marker-end="url(#mermaid-b5dea2fa-dc08-4420-b63f-b4b946e2d77f_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_EMAILPROC_OUTLOOK_11" d="M6017.594,296.637L6100.733,304.031C6183.872,311.425,6350.151,326.212,6433.29,337.773C6516.43,349.333,6516.43,357.667,6516.43,366C6516.43,374.333,6516.43,382.667,6516.43,391C6516.43,399.333,6516.43,407.667,6287.91,416C6059.389,424.333,5602.349,432.667,5373.829,441C5145.309,449.333,5145.309,457.667,5145.309,465.333C5145.309,473,5145.309,480,5145.309,483.5L5145.309,487"></path><path marker-end="url(#mermaid-b5dea2fa-dc08-4420-b63f-b4b946e2d77f_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_EMAILPROC_BASEPARSER_12" d="M6017.594,300.478L6068.121,307.232C6118.648,313.986,6219.703,327.493,6270.23,338.413C6320.758,349.333,6320.758,357.667,6320.758,366C6320.758,374.333,6320.758,382.667,6320.758,391C6320.758,399.333,6320.758,407.667,6418.331,416C6515.904,424.333,6711.049,432.667,6808.622,441C6906.195,449.333,6906.195,457.667,6906.195,465.333C6906.195,473,6906.195,480,6906.195,483.5L6906.195,487"></path><path marker-end="url(#mermaid-b5dea2fa-dc08-4420-b63f-b4b946e2d77f_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_EMAILPROC_EMAILDB_13" d="M5845.844,293.196L5682.769,301.163C5519.694,309.13,5193.544,325.065,5030.469,337.199C4867.395,349.333,4867.395,357.667,4867.395,366C4867.395,374.333,4867.395,382.667,4867.395,391C4867.395,399.333,4867.395,407.667,4867.395,416C4867.395,424.333,4867.395,432.667,4867.395,441C4867.395,449.333,4867.395,457.667,4867.395,465.333C4867.395,473,4867.395,480,4867.395,483.5L4867.395,487"></path><path marker-end="url(#mermaid-b5dea2fa-dc08-4420-b63f-b4b946e2d77f_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_EMAILPROC_LOGGER_14" d="M5845.844,291.238L5527.591,299.532C5209.339,307.825,4572.833,324.413,4254.581,336.873C3936.328,349.333,3936.328,357.667,3936.328,366C3936.328,374.333,3936.328,382.667,3936.328,391C3936.328,399.333,3936.328,407.667,3655.633,416C3374.938,424.333,2813.547,432.667,2532.852,441C2252.156,449.333,2252.156,457.667,2252.156,465.333C2252.156,473,2252.156,480,2252.156,483.5L2252.156,487"></path><path marker-end="url(#mermaid-b5dea2fa-dc08-4420-b63f-b4b946e2d77f_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_BASEPARSER_ETDPARSER_15" d="M6977.047,537.334L6996.998,542.778C7016.949,548.223,7056.852,559.111,7076.803,568.722C7096.754,578.333,7096.754,586.667,7096.754,595C7096.754,603.333,7096.754,611.667,7096.754,620C7096.754,628.333,7096.754,636.667,7096.754,645.532C7096.754,654.397,7096.754,663.793,7096.754,668.491L7096.754,673.19"></path><path marker-end="url(#mermaid-b5dea2fa-dc08-4420-b63f-b4b946e2d77f_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_BASEPARSER_GTKPARSER_16" d="M6907.763,545L6908.005,549.167C6908.247,553.333,6908.731,561.667,6908.973,570C6909.215,578.333,6909.215,586.667,6909.215,595C6909.215,603.333,6909.215,611.667,6909.215,620C6909.215,628.333,6909.215,636.667,6909.215,645.532C6909.215,654.397,6909.215,663.793,6909.215,668.491L6909.215,673.19"></path><path marker-end="url(#mermaid-b5dea2fa-dc08-4420-b63f-b4b946e2d77f_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_BASEPARSER_JCETPARSER_17" d="M6835.344,537.59L6815.808,542.992C6796.272,548.394,6757.201,559.197,6737.665,568.765C6718.129,578.333,6718.129,586.667,6718.129,595C6718.129,603.333,6718.129,611.667,6718.129,620C6718.129,628.333,6718.129,636.667,6718.129,645.532C6718.129,654.397,6718.129,663.793,6718.129,668.491L6718.129,673.19"></path><path marker-end="url(#mermaid-b5dea2fa-dc08-4420-b63f-b4b946e2d77f_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_BASEPARSER_LINGSENPARSER_18" d="M6835.344,527.418L6781.955,534.515C6728.566,541.612,6621.789,555.806,6568.4,567.07C6515.012,578.333,6515.012,586.667,6515.012,595C6515.012,603.333,6515.012,611.667,6515.012,620C6515.012,628.333,6515.012,636.667,6515.012,645.532C6515.012,654.397,6515.012,663.793,6515.012,668.491L6515.012,673.19"></path><path marker-end="url(#mermaid-b5dea2fa-dc08-4420-b63f-b4b946e2d77f_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_BASEPARSER_XAHTPARSER_19" d="M6835.344,524.184L6747.848,531.82C6660.353,539.456,6485.362,554.728,6397.867,566.531C6310.371,578.333,6310.371,586.667,6310.371,595C6310.371,603.333,6310.371,611.667,6310.371,620C6310.371,628.333,6310.371,636.667,6310.371,645.532C6310.371,654.397,6310.371,663.793,6310.371,668.491L6310.371,673.19"></path><path marker-end="url(#mermaid-b5dea2fa-dc08-4420-b63f-b4b946e2d77f_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_EMAILDB_DBMODELS_20" d="M4872.587,545L4873.388,549.167C4874.189,553.333,4875.792,561.667,4876.593,570C4877.395,578.333,4877.395,586.667,4877.395,595C4877.395,603.333,4877.395,611.667,4877.395,620C4877.395,628.333,4877.395,636.667,4877.395,645.532C4877.395,654.397,4877.395,663.793,4877.395,668.491L4877.395,673.19"></path><path marker-end="url(#mermaid-b5dea2fa-dc08-4420-b63f-b4b946e2d77f_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_OUTLOOK_OUTLOOKAPP_21" d="M5196.538,545L5204.444,549.167C5212.35,553.333,5228.161,561.667,5236.067,570C5243.973,578.333,5243.973,586.667,5243.973,595C5243.973,603.333,5243.973,611.667,5243.973,620C5243.973,628.333,5243.973,636.667,4679.798,650.423C4115.624,664.179,2987.274,683.357,2423.1,692.947L1858.925,702.536"></path><path marker-end="url(#mermaid-b5dea2fa-dc08-4420-b63f-b4b946e2d77f_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_POP3_EMAILSERVER_22" d="M5362.637,545L5362.637,549.167C5362.637,553.333,5362.637,561.667,5362.637,570C5362.637,578.333,5362.637,586.667,5362.637,595C5362.637,603.333,5362.637,611.667,5362.637,620C5362.637,628.333,5362.637,636.667,4811.982,650.468C4261.327,664.27,3160.017,683.54,2609.361,693.175L2058.706,702.809"></path><path marker-end="url(#mermaid-b5dea2fa-dc08-4420-b63f-b4b946e2d77f_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_SMTP_EMAILSERVER_23" d="M5567.004,545L5567.004,549.167C5567.004,553.333,5567.004,561.667,5567.004,570C5567.004,578.333,5567.004,586.667,5567.004,595C5567.004,603.333,5567.004,611.667,5567.004,620C5567.004,628.333,5567.004,636.667,4982.288,650.481C4397.571,664.296,3228.139,683.592,2643.423,693.24L2058.706,702.888"></path><path marker-end="url(#mermaid-b5dea2fa-dc08-4420-b63f-b4b946e2d77f_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_CSVCONV_FILESYSTEM_24" d="M2560.852,545L2560.852,549.167C2560.852,553.333,2560.852,561.667,2560.852,570C2560.852,578.333,2560.852,586.667,2560.852,595C2560.852,603.333,2560.852,611.667,2560.852,620C2560.852,628.333,2560.852,636.667,2382.678,650.053C2204.505,663.439,1848.158,681.879,1669.984,691.099L1491.811,700.318"></path><path marker-end="url(#mermaid-b5dea2fa-dc08-4420-b63f-b4b946e2d77f_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_FILEUPLOAD_FILESYSTEM_25" d="M3936.875,533.242L3979.749,539.369C4022.624,545.495,4108.372,557.747,4151.247,568.04C4194.121,578.333,4194.121,586.667,4194.121,595C4194.121,603.333,4194.121,611.667,4194.121,620C4194.121,628.333,4194.121,636.667,3743.737,650.433C3293.353,664.198,2392.584,683.397,1942.2,692.996L1491.815,702.595"></path><path marker-end="url(#mermaid-b5dea2fa-dc08-4420-b63f-b4b946e2d77f_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_CHINESEPATH_FILESYSTEM_26" d="M4578.039,545L4578.039,549.167C4578.039,553.333,4578.039,561.667,4578.039,570C4578.039,578.333,4578.039,586.667,4578.039,595C4578.039,603.333,4578.039,611.667,4578.039,620C4578.039,628.333,4578.039,636.667,4063.669,650.465C3549.298,664.263,2520.557,683.526,2006.186,693.157L1491.816,702.789"></path><path marker-end="url(#mermaid-b5dea2fa-dc08-4420-b63f-b4b946e2d77f_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_EMAILDATA_ATTACHMENT_27" d="M373.055,533.971L398.872,539.976C424.689,545.981,476.323,557.99,502.14,568.162C527.957,578.333,527.957,586.667,527.957,595C527.957,603.333,527.957,611.667,527.957,620C527.957,628.333,527.957,636.667,527.957,645.532C527.957,654.397,527.957,663.793,527.957,668.491L527.957,673.19"></path><path marker-end="url(#mermaid-b5dea2fa-dc08-4420-b63f-b4b946e2d77f_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_EMAILDATA_METADATA_28" d="M301.302,545L300.825,549.167C300.348,553.333,299.395,561.667,298.918,570C298.441,578.333,298.441,586.667,298.441,595C298.441,603.333,298.441,611.667,298.441,620C298.441,628.333,298.441,636.667,298.441,645.532C298.441,654.397,298.441,663.793,298.441,668.491L298.441,673.19"></path><path marker-end="url(#mermaid-b5dea2fa-dc08-4420-b63f-b4b946e2d77f_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_PARSERESULT_VENDORID_29" d="M777.418,545L777.418,549.167C777.418,553.333,777.418,561.667,777.418,570C777.418,578.333,777.418,586.667,777.418,595C777.418,603.333,777.418,611.667,777.418,620C777.418,628.333,777.418,636.667,777.418,645.532C777.418,654.397,777.418,663.793,777.418,668.491L777.418,673.19"></path><path marker-end="url(#mermaid-b5dea2fa-dc08-4420-b63f-b4b946e2d77f_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_CONTEXT_EMAILDATA_30" d="M304.391,316L304.391,320.167C304.391,324.333,304.391,332.667,304.391,341C304.391,349.333,304.391,357.667,304.391,366C304.391,374.333,304.391,382.667,304.391,391C304.391,399.333,304.391,407.667,304.391,416C304.391,424.333,304.391,432.667,304.391,441C304.391,449.333,304.391,457.667,304.391,465.333C304.391,473,304.391,480,304.391,483.5L304.391,487"></path><path marker-end="url(#mermaid-b5dea2fa-dc08-4420-b63f-b4b946e2d77f_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_CONFIG_SETTINGS_31" d="M2252.156,731.19L2252.156,736.555C2252.156,741.92,2252.156,752.65,2252.156,762.181C2252.156,771.713,2252.156,780.046,2252.156,788.38C2252.156,796.713,2252.156,805.046,2252.156,813.38C2252.156,821.713,2252.156,830.046,2252.156,837.713C2252.156,845.38,2252.156,852.38,2252.156,855.88L2252.156,859.38"></path><path marker-end="url(#mermaid-b5dea2fa-dc08-4420-b63f-b4b946e2d77f_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_LOGGER_CONFIG_32" d="M2252.156,545L2252.156,549.167C2252.156,553.333,2252.156,561.667,2252.156,570C2252.156,578.333,2252.156,586.667,2252.156,595C2252.156,603.333,2252.156,611.667,2252.156,620C2252.156,628.333,2252.156,636.667,2252.156,645.532C2252.156,654.397,2252.156,663.793,2252.156,668.491L2252.156,673.19"></path><path marker-end="url(#mermaid-b5dea2fa-dc08-4420-b63f-b4b946e2d77f_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_SCHEDULER_FILECLEAN_33" d="M1415.199,316L1415.199,320.167C1415.199,324.333,1415.199,332.667,1415.199,341C1415.199,349.333,1415.199,357.667,1415.199,366C1415.199,374.333,1415.199,382.667,1415.199,391C1415.199,399.333,1415.199,407.667,1415.199,416C1415.199,424.333,1415.199,432.667,1415.199,441C1415.199,449.333,1415.199,457.667,1415.199,465.333C1415.199,473,1415.199,480,1415.199,483.5L1415.199,487"></path><path marker-end="url(#mermaid-b5dea2fa-dc08-4420-b63f-b4b946e2d77f_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_FILECLEAN_FILESYSTEM_34" d="M1415.199,545L1415.199,549.167C1415.199,553.333,1415.199,561.667,1415.199,570C1415.199,578.333,1415.199,586.667,1415.199,595C1415.199,603.333,1415.199,611.667,1415.199,620C1415.199,628.333,1415.199,636.667,1415.342,645.532C1415.484,654.397,1415.77,663.794,1415.912,668.493L1416.055,673.192"></path><path marker-end="url(#mermaid-b5dea2fa-dc08-4420-b63f-b4b946e2d77f_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_EMAILDB_DATABASE_35" d="M4840.253,545L4836.064,549.167C4831.875,553.333,4823.498,561.667,4819.31,570C4815.121,578.333,4815.121,586.667,4815.121,595C4815.121,603.333,4815.121,611.667,4815.121,620C4815.121,628.333,4815.121,636.667,4282.987,650.563C3750.853,664.46,2686.584,683.92,2154.45,693.651L1622.316,703.381"></path><path marker-end="url(#mermaid-b5dea2fa-dc08-4420-b63f-b4b946e2d77f_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_OUTLOOK_OUTLOOKAPP_36" d="M5123.665,545L5120.325,549.167C5116.985,553.333,5110.305,561.667,5106.965,570C5103.625,578.333,5103.625,586.667,5103.625,595C5103.625,603.333,5103.625,611.667,5103.625,620C5103.625,628.333,5103.625,636.667,4562.842,650.411C4022.058,664.155,2940.492,683.311,2399.708,692.889L1858.925,702.466"></path><path marker-end="url(#mermaid-b5dea2fa-dc08-4420-b63f-b4b946e2d77f_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_FILEUPLOAD_NETWORK_37" d="M3803.808,545L3799.734,549.167C3795.661,553.333,3787.514,561.667,3783.441,570C3779.367,578.333,3779.367,586.667,3779.367,595C3779.367,603.333,3779.367,611.667,3779.367,620C3779.367,628.333,3779.367,636.667,3366.168,650.354C2952.97,664.042,2126.572,683.084,1713.373,692.605L1300.175,702.126"></path></g><g class="edgeLabels"><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g></g><g class="nodes"><g transform="translate(3836.77734375, 85)" id="flowchart-API-131" class="node default presentation"><rect height="54" width="145.796875" y="-27" x="-72.8984375" style="fill:#e1f5fe !important" class="basic label-container"></rect><g transform="translate(-42.8984375, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="85.796875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>API Services</p></span></div></foreignObject></g></g><g transform="translate(4979.88671875, 85)" id="flowchart-CLI-132" class="node default presentation"><rect height="54" width="152.40625" y="-27" x="-76.203125" style="fill:#e1f5fe !important" class="basic label-container"></rect><g transform="translate(-46.203125, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="92.40625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>CLI Interface</p></span></div></foreignObject></g></g><g transform="translate(6134.57421875, 85)" id="flowchart-WEB-133" class="node default presentation"><rect height="54" width="160.8125" y="-27" x="-80.40625" style="fill:#e1f5fe !important" class="basic label-container"></rect><g transform="translate(-50.40625, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="100.8125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Web Interface</p></span></div></foreignObject></g></g><g transform="translate(131.859375, 85)" id="flowchart-FTEQC-134" class="node default presentation"><rect height="54" width="137.71875" y="-27" x="-68.859375" style="fill:#e1f5fe !important" class="basic label-container"></rect><g transform="translate(-38.859375, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="77.71875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>FT-EQC API</p></span></div></foreignObject></g></g><g transform="translate(1090.01953125, 85)" id="flowchart-NETAPI-135" class="node default presentation"><rect height="54" width="208.828125" y="-27" x="-104.4140625" style="fill:#e1f5fe !important" class="basic label-container"></rect><g transform="translate(-74.4140625, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="148.828125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Network Browser API</p></span></div></foreignObject></g></g><g transform="translate(1304.48828125, 85)" id="flowchart-APIUTILS-136" class="node default presentation"><rect height="54" width="120.109375" y="-27" x="-60.0546875" style="fill:#e1f5fe !important" class="basic label-container"></rect><g transform="translate(-30.0546875, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="60.109375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>API Utils</p></span></div></foreignObject></g></g><g transform="translate(1515.40234375, 85)" id="flowchart-CLEANUP-137" class="node default presentation"><rect height="54" width="174.328125" y="-27" x="-87.1640625" style="fill:#e1f5fe !important" class="basic label-container"></rect><g transform="translate(-57.1640625, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="114.328125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Cleanup Service</p></span></div></foreignObject></g></g><g transform="translate(2059.578125, 85)" id="flowchart-EQCPROC-138" class="node default presentation"><rect height="54" width="224.203125" y="-27" x="-112.1015625" style="fill:#e1f5fe !important" class="basic label-container"></rect><g transform="translate(-82.1015625, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="164.203125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>EQC Processing Service</p></span></div></foreignObject></g></g><g transform="translate(2999.33203125, 85)" id="flowchart-FILEMGMT-139" class="node default presentation"><rect height="54" width="238.5" y="-27" x="-119.25" style="fill:#e1f5fe !important" class="basic label-container"></rect><g transform="translate(-89.25, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="178.5"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>File Management Service</p></span></div></foreignObject></g></g><g transform="translate(5931.71875, 289)" id="flowchart-EMAILPROC-140" class="node default application"><rect height="54" width="171.75" y="-27" x="-85.875" style="fill:#f3e5f5 !important" class="basic label-container"></rect><g transform="translate(-55.875, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="111.75"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Email Processor</p></span></div></foreignObject></g></g><g transform="translate(3192.4296875, 289)" id="flowchart-EMAILREADER-141" class="node default application"><rect height="54" width="224.296875" y="-27" x="-112.1484375" style="fill:#f3e5f5 !important" class="basic label-container"></rect><g transform="translate(-82.1484375, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="164.296875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Email Reader Interface</p></span></div></foreignObject></g></g><g transform="translate(3460.125, 289)" id="flowchart-TASKQUEUE-142" class="node default application"><rect height="54" width="211.09375" y="-27" x="-105.546875" style="fill:#f3e5f5 !important" class="basic label-container"></rect><g transform="translate(-75.546875, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="151.09375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Task Queue Interface</p></span></div></foreignObject></g></g><g transform="translate(7767.24609375, 518)" id="flowchart-ENTITIES-143" class="node default domain"><rect height="54" width="114.328125" y="-27" x="-57.1640625" style="fill:#e8f5e8 !important" class="basic label-container"></rect><g transform="translate(-27.1640625, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="54.328125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Entities</p></span></div></foreignObject></g></g><g transform="translate(7580.78515625, 518)" id="flowchart-VALUEOBJ-144" class="node default domain"><rect height="54" width="158.59375" y="-27" x="-79.296875" style="fill:#e8f5e8 !important" class="basic label-container"></rect><g transform="translate(-49.296875, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="98.59375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Value Objects</p></span></div></foreignObject></g></g><g transform="translate(7363.30078125, 518)" id="flowchart-DOMAINSERV-145" class="node default domain"><rect height="54" width="176.375" y="-27" x="-88.1875" style="fill:#e8f5e8 !important" class="basic label-container"></rect><g transform="translate(-58.1875, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="116.375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Domain Services</p></span></div></foreignObject></g></g><g transform="translate(7971.94921875, 518)" id="flowchart-EXCEPTIONS-146" class="node default domain"><rect height="54" width="195.078125" y="-27" x="-97.5390625" style="fill:#e8f5e8 !important" class="basic label-container"></rect><g transform="translate(-67.5390625, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="135.078125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Domain Exceptions</p></span></div></foreignObject></g></g><g transform="translate(304.390625, 518)" id="flowchart-EMAILDATA-147" class="node default datamodel"><rect height="54" width="137.328125" y="-27" x="-68.6640625" style="fill:#fff3e0 !important" class="basic label-container"></rect><g transform="translate(-38.6640625, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="77.328125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Email Data</p></span></div></foreignObject></g></g><g transform="translate(527.95703125, 704.1897773742676)" id="flowchart-ATTACHMENT-148" class="node default datamodel"><rect height="54" width="187.765625" y="-27" x="-93.8828125" style="fill:#fff3e0 !important" class="basic label-container"></rect><g transform="translate(-63.8828125, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="127.765625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Email Attachment</p></span></div></foreignObject></g></g><g transform="translate(777.41796875, 704.1897773742676)" id="flowchart-VENDORID-149" class="node default datamodel"><rect height="54" width="211.15625" y="-27" x="-105.578125" style="fill:#fff3e0 !important" class="basic label-container"></rect><g transform="translate(-75.578125, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="151.15625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Vendor Identification</p></span></div></foreignObject></g></g><g transform="translate(777.41796875, 518)" id="flowchart-PARSERESULT-150" class="node default datamodel"><rect height="54" width="159.109375" y="-27" x="-79.5546875" style="fill:#fff3e0 !important" class="basic label-container"></rect><g transform="translate(-49.5546875, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="99.109375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Parsing Result</p></span></div></foreignObject></g></g><g transform="translate(970.99609375, 518)" id="flowchart-TASKDATA-151" class="node default datamodel"><rect height="54" width="128.046875" y="-27" x="-64.0234375" style="fill:#fff3e0 !important" class="basic label-container"></rect><g transform="translate(-34.0234375, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="68.046875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Task Data</p></span></div></foreignObject></g></g><g transform="translate(298.44140625, 704.1897773742676)" id="flowchart-METADATA-152" class="node default datamodel"><rect height="54" width="171.265625" y="-27" x="-85.6328125" style="fill:#fff3e0 !important" class="basic label-container"></rect><g transform="translate(-55.6328125, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="111.265625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Email Metadata</p></span></div></foreignObject></g></g><g transform="translate(304.390625, 289)" id="flowchart-CONTEXT-153" class="node default datamodel"><rect height="54" width="195.0625" y="-27" x="-97.53125" style="fill:#fff3e0 !important" class="basic label-container"></rect><g transform="translate(-67.53125, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="135.0625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Processing Context</p></span></div></foreignObject></g></g><g transform="translate(4867.39453125, 518)" id="flowchart-EMAILDB-154" class="node default infrastructure"><rect height="54" width="169.859375" y="-27" x="-84.9296875" style="fill:#fce4ec !important" class="basic label-container"></rect><g transform="translate(-54.9296875, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="109.859375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Email Database</p></span></div></foreignObject></g></g><g transform="translate(4877.39453125, 704.1897773742676)" id="flowchart-DBMODELS-155" class="node default infrastructure"><rect height="54" width="179.09375" y="-27" x="-89.546875" style="fill:#fce4ec !important" class="basic label-container"></rect><g transform="translate(-59.546875, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="119.09375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Database Models</p></span></div></foreignObject></g></g><g transform="translate(5145.30859375, 518)" id="flowchart-OUTLOOK-156" class="node default infrastructure"><rect height="54" width="176.734375" y="-27" x="-88.3671875" style="fill:#fce4ec !important" class="basic label-container"></rect><g transform="translate(-58.3671875, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="116.734375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Outlook Adapter</p></span></div></foreignObject></g></g><g transform="translate(5362.63671875, 518)" id="flowchart-POP3-157" class="node default infrastructure"><rect height="54" width="157.921875" y="-27" x="-78.9609375" style="fill:#fce4ec !important" class="basic label-container"></rect><g transform="translate(-48.9609375, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="97.921875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>POP3 Adapter</p></span></div></foreignObject></g></g><g transform="translate(5567.00390625, 518)" id="flowchart-SMTP-158" class="node default infrastructure"><rect height="54" width="150.8125" y="-27" x="-75.40625" style="fill:#fce4ec !important" class="basic label-container"></rect><g transform="translate(-45.40625, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="90.8125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>SMTP Sender</p></span></div></foreignObject></g></g><g transform="translate(5788.97265625, 518)" id="flowchart-EMAILSYNC-159" class="node default infrastructure"><rect height="54" width="193.125" y="-27" x="-96.5625" style="fill:#fce4ec !important" class="basic label-container"></rect><g transform="translate(-66.5625, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="133.125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Email Sync Service</p></span></div></foreignObject></g></g><g transform="translate(6031.23828125, 518)" id="flowchart-WEBAPI-160" class="node default infrastructure"><rect height="54" width="191.40625" y="-27" x="-95.703125" style="fill:#fce4ec !important" class="basic label-container"></rect><g transform="translate(-65.703125, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="131.40625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Email Web Service</p></span></div></foreignObject></g></g><g transform="translate(2560.8515625, 518)" id="flowchart-CSVCONV-161" class="node default infrastructure"><rect height="54" width="224.984375" y="-27" x="-112.4921875" style="fill:#fce4ec !important" class="basic label-container"></rect><g transform="translate(-82.4921875, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="164.984375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>CSV to Excel Converter</p></span></div></foreignObject></g></g><g transform="translate(2802.375, 518)" id="flowchart-CTAPROC-162" class="node default infrastructure"><rect height="54" width="158.0625" y="-27" x="-79.03125" style="fill:#fce4ec !important" class="basic label-container"></rect><g transform="translate(-49.03125, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="98.0625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>CTA Processor</p></span></div></foreignObject></g></g><g transform="translate(3011.984375, 518)" id="flowchart-EQCPROC2-163" class="node default infrastructure"><rect height="54" width="161.15625" y="-27" x="-80.578125" style="fill:#fce4ec !important" class="basic label-container"></rect><g transform="translate(-50.578125, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="101.15625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>EQC Processor</p></span></div></foreignObject></g></g><g transform="translate(3255.234375, 518)" id="flowchart-FTSUM-164" class="node default infrastructure"><rect height="54" width="225.34375" y="-27" x="-112.671875" style="fill:#fce4ec !important" class="basic label-container"></rect><g transform="translate(-82.671875, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="165.34375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>FT Summary Generator</p></span></div></foreignObject></g></g><g transform="translate(3525.71875, 518)" id="flowchart-PERFMAN-165" class="node default infrastructure"><rect height="54" width="215.625" y="-27" x="-107.8125" style="fill:#fce4ec !important" class="basic label-container"></rect><g transform="translate(-77.8125, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="155.625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Performance Manager</p></span></div></foreignObject></g></g><g transform="translate(3830.203125, 518)" id="flowchart-FILEUPLOAD-166" class="node default infrastructure"><rect height="54" width="213.34375" y="-27" x="-106.671875" style="fill:#fce4ec !important" class="basic label-container"></rect><g transform="translate(-76.671875, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="153.34375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>File Upload Processor</p></span></div></foreignObject></g></g><g transform="translate(4079.3203125, 518)" id="flowchart-ARCHEXT-167" class="node default infrastructure"><rect height="54" width="184.890625" y="-27" x="-92.4453125" style="fill:#fce4ec !important" class="basic label-container"></rect><g transform="translate(-62.4453125, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="124.890625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Archive Extractor</p></span></div></foreignObject></g></g><g transform="translate(4318.84375, 518)" id="flowchart-TEMPFILE-168" class="node default infrastructure"><rect height="54" width="194.15625" y="-27" x="-97.078125" style="fill:#fce4ec !important" class="basic label-container"></rect><g transform="translate(-67.078125, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="134.15625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Temp File Manager</p></span></div></foreignObject></g></g><g transform="translate(4578.0390625, 518)" id="flowchart-CHINESEPATH-169" class="node default infrastructure"><rect height="54" width="224.234375" y="-27" x="-112.1171875" style="fill:#fce4ec !important" class="basic label-container"></rect><g transform="translate(-82.1171875, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="164.234375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Chinese Path Processor</p></span></div></foreignObject></g></g><g transform="translate(6906.1953125, 518)" id="flowchart-BASEPARSER-170" class="node default infrastructure"><rect height="54" width="141.703125" y="-27" x="-70.8515625" style="fill:#fce4ec !important" class="basic label-container"></rect><g transform="translate(-40.8515625, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="81.703125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Base Parser</p></span></div></foreignObject></g></g><g transform="translate(7096.75390625, 704.1897773742676)" id="flowchart-ETDPARSER-171" class="node default infrastructure"><rect height="54" width="136.71875" y="-27" x="-68.359375" style="fill:#fce4ec !important" class="basic label-container"></rect><g transform="translate(-38.359375, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="76.71875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>ETD Parser</p></span></div></foreignObject></g></g><g transform="translate(6909.21484375, 704.1897773742676)" id="flowchart-GTKPARSER-172" class="node default infrastructure"><rect height="54" width="138.359375" y="-27" x="-69.1796875" style="fill:#fce4ec !important" class="basic label-container"></rect><g transform="translate(-39.1796875, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="78.359375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>GTK Parser</p></span></div></foreignObject></g></g><g transform="translate(6718.12890625, 704.1897773742676)" id="flowchart-JCETPARSER-173" class="node default infrastructure"><rect height="54" width="143.8125" y="-27" x="-71.90625" style="fill:#fce4ec !important" class="basic label-container"></rect><g transform="translate(-41.90625, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="83.8125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>JCET Parser</p></span></div></foreignObject></g></g><g transform="translate(6515.01171875, 704.1897773742676)" id="flowchart-LINGSENPARSER-174" class="node default infrastructure"><rect height="54" width="162.421875" y="-27" x="-81.2109375" style="fill:#fce4ec !important" class="basic label-container"></rect><g transform="translate(-51.2109375, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="102.421875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Lingsen Parser</p></span></div></foreignObject></g></g><g transform="translate(6310.37109375, 704.1897773742676)" id="flowchart-XAHTPARSER-175" class="node default infrastructure"><rect height="54" width="146.859375" y="-27" x="-73.4296875" style="fill:#fce4ec !important" class="basic label-container"></rect><g transform="translate(-43.4296875, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="86.859375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>XAHT Parser</p></span></div></foreignObject></g></g><g transform="translate(2252.15625, 704.1897773742676)" id="flowchart-CONFIG-176" class="node default infrastructure"><rect height="54" width="170.109375" y="-27" x="-85.0546875" style="fill:#fce4ec !important" class="basic label-container"></rect><g transform="translate(-55.0546875, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="110.109375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Config Manager</p></span></div></foreignObject></g></g><g transform="translate(2252.15625, 890.3795547485352)" id="flowchart-SETTINGS-177" class="node default infrastructure"><rect height="54" width="116.921875" y="-27" x="-58.4609375" style="fill:#fce4ec !important" class="basic label-container"></rect><g transform="translate(-28.4609375, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="56.921875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Settings</p></span></div></foreignObject></g></g><g transform="translate(2252.15625, 518)" id="flowchart-LOGGER-178" class="node default infrastructure"><rect height="54" width="172.40625" y="-27" x="-86.203125" style="fill:#fce4ec !important" class="basic label-container"></rect><g transform="translate(-56.203125, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="112.40625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Logger Manager</p></span></div></foreignObject></g></g><g transform="translate(1415.19921875, 518)" id="flowchart-FILECLEAN-179" class="node default services"><rect height="54" width="146.34375" y="-27" x="-73.171875" style="fill:#f1f8e9 !important" class="basic label-container"></rect><g transform="translate(-43.171875, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="86.34375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>File Cleaner</p></span></div></foreignObject></g></g><g transform="translate(1415.19921875, 289)" id="flowchart-SCHEDULER-180" class="node default services"><rect height="54" width="130.40625" y="-27" x="-65.203125" style="fill:#f1f8e9 !important" class="basic label-container"></rect><g transform="translate(-35.203125, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="70.40625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Scheduler</p></span></div></foreignObject></g></g><g transform="translate(1761.62109375, 704.1897773742676)" id="flowchart-OUTLOOKAPP-181" class="node default external"><rect height="54" width="186.609375" y="-27" x="-93.3046875" style="fill:#f5f5f5 !important" class="basic label-container"></rect><g transform="translate(-63.3046875, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="126.609375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Microsoft Outlook</p></span></div></foreignObject></g></g><g transform="translate(1578.06640625, 704.1897773742676)" id="flowchart-DATABASE-182" class="node default external"><path transform="translate(-40.25, -34.189781021897815)" style="fill:#f5f5f5 !important" class="basic label-container" d="M0,9.793187347931873 a40.25,9.793187347931873 0,0,0 80.5,0 a40.25,9.793187347931873 0,0,0 -80.5,0 l0,48.79318734793188 a40.25,9.793187347931873 0,0,0 80.5,0 l0,-48.79318734793188"></path><g transform="translate(-32.75, -2)" style="" class="label"><rect></rect><foreignObject height="24" width="65.5"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Database</p></span></div></foreignObject></g></g><g transform="translate(1416.99609375, 704.1897773742676)" id="flowchart-FILESYSTEM-183" class="node default external"><rect height="54" width="141.640625" y="-27" x="-70.8203125" style="fill:#f5f5f5 !important" class="basic label-container"></rect><g transform="translate(-40.8203125, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="81.640625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>File System</p></span></div></foreignObject></g></g><g transform="translate(1210.59765625, 704.1897773742676)" id="flowchart-NETWORK-184" class="node default external"><rect height="54" width="171.15625" y="-27" x="-85.578125" style="fill:#f5f5f5 !important" class="basic label-container"></rect><g transform="translate(-55.578125, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="111.15625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Network Shares</p></span></div></foreignObject></g></g><g transform="translate(1979.81640625, 704.1897773742676)" id="flowchart-EMAILSERVER-185" class="node default external"><rect height="54" width="149.78125" y="-27" x="-74.890625" style="fill:#f5f5f5 !important" class="basic label-container"></rect><g transform="translate(-44.890625, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="89.78125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Email Server</p></span></div></foreignObject></g></g></g></g></g></svg>