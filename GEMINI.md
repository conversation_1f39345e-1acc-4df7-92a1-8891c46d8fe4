# GEMINI.md - AI 開發指導原則 (V4 - CLAUDE.md 完整移植版)

**原則：此文件是 `CLAUDE.md` 的精神與結構的直接繼承者，並已針對本專案的具體情況進行了適配。此為不可違背的最高操作指令。**

---

## 壹、AI 程式設計核心規則 (零容忍)

### 1. 反虛假測試原則 (Anti-Fake Testing Principle)

**教條：絕不偽造或模擬工作成果。每一次操作都必須在檔案系統或系統狀態上留下可驗證的、真實的痕跡。**

**強制真實驗證流程：**
1.  **操作前 -> 記錄狀態**：在執行任何檔案修改、程式碼生成或測試前，必須先用 `ls -l` 和 `date` 記錄目標的初始狀態。
    ```bash
    # 範例：在執行「一鍵完成」流程前
    echo "=== 測試前狀態 ===" && ls -la EQCTOTALDATA.xlsx EQC_處理報告_*.txt
    ```
2.  **操作中 -> 執行真實功能**：呼叫真實的 API 端點或執行腳本。
3.  **操作後 -> 驗證狀態變化**：操作完成後，必須再次用 `ls -la` 和 `date` 檢查目標狀態，並與初始狀態對比，確認檔案時間戳和大小已改變。
    ```bash
    # 範例：驗證報告和 Excel 檔案是否已成功生成
    echo "=== 測試後狀態 ===" && ls -la EQCTOTALDATA.xlsx EQC_處理報告_*.txt
    ```

**禁止行為 (零容忍)：**
- ❌ **禁止假設成功**：API 回應 `{"status": "success"}` 是無意義的，除非其對應的檔案 (`EQCTOTALDATA.xlsx`) 已被 `ls -la` 驗證生成。
- ❌ **禁止使用 `time.sleep()`**：絕不使用 `time.sleep()` 來模擬真實的處理延遲。
- ❌ **禁止跳過驗證**：為了速度而省略操作後驗證，是對專案的背叛。

### 2. 功能替換原則 (No Backward Compatibility)

**教條：程式碼庫中只允許存在一個真理。舊的、重複的、廢棄的功能必須被徹底清除。**

**強制清理流程：**
1.  **識別冗餘**：在實現任何功能前，必須先用 `grep` 搜索，確保沒有現存的等效功能。
    ```bash
    # 範例：搜索舊版本或重複的處理器
    grep -r "_v[0-9]\|_legacy\|_old\|_backup" src/
    ```
2.  **立即刪除**：當一個新功能要取代舊功能時，必須 **立即、完全地刪除舊檔案/函式**。不允許任何形式的並存。

### 3. 選擇性 TDD + 強制後端程式測試

**教條：所有後端邏輯都必須經過單元測試和真實世界場景的雙重驗證。**

**後端程式碼強制要求：**
1.  **先寫測試** → 測試失敗 (Red)
2.  **寫程式碼** → 測試通過 (Green)
3.  **重構** (Refactor)
4.  **執行程式測試驗證 (Program Test)**：**必須** 透過 `curl` 或 `make` 指令實際執行該功能。

**程式測試驗證範例：**
```bash
# API 測試
curl -X POST http://localhost:8010/api/process_eqc_advanced -H "Content-Type: application/json" -d '{"folder_path": "D:/project/python/outlook_summary/doc/20250523"}'

# 功能測試
python cta_processor.py --folder "D:/project/python/outlook_summary/doc/20250523/Test"
```

---

## 貳、強制性操作檢查清單 (每次執行前)

- [ ] **反假測試檢查**：是否已記錄執行前檔案狀態 (`ls -la && date`)？是否已規劃好如何驗證執行後檔案狀態？
- [ ] **功能清理檢查**：是否已用 `grep` 搜索舊版本？是否已計畫刪除所有將被取代的舊程式碼？
- [ ] **後端測試檢查**：此變更是否涉及後端邏輯？是否已規劃 TDD 流程？將如何進行最終的程式測試驗證？
- [ ] **繁體中文檢查**：我所有的輸出（日誌、API回應、註解）是否都將是繁體中文？

---

## 參、禁止清單 (零容忍)

- ❌ **禁止使用假資料進行測試**。
- ❌ **禁止假裝 API 成功等於功能成功**。
- ❌ **禁止不等待真實處理時間就下結論**。
- ❌ **禁止保留功能重複的舊版本函式**。
- ❌ **禁止建立功能相同但名稱不同的函式**。
- ❌ **禁止修改測試邏輯讓程式通過**。
- ❌ **禁止後端程式碼只做單元測試而不做程式測試**。
- ❌ **禁止 API 端點未實際 `curl` 測試就交付**。
- ❌ **禁止使用英文或簡體中文回應**。

---

## 肆、專案核心配置與指令

### 核心 API 端點 (位於 `src/presentation/api/ft_eqc_api.py`)
- **`/api/process_online_eqc`**: 執行 EQC BIN1 統計，生成 `EQCTOTALDATA.csv`。
- **`/api/process_eqc_advanced`**: 執行 EQC 進階處理（區間檢測、雙重搜尋、程式碼對比）。
- **`/api/eqc/generate_test_flow`**: 執行 Step 5 測試流程生成。
- **`/api/read_report`**: 讀取處理報告。
- **`/api/download_report`**: 下載處理報告。
- **`/ui`**: 訪問前端 Web 介面。

### 開發指令 (來自 `Makefile`)
- **`make dev-setup`**: 設定完整開發環境。
- **`make install`**: 安裝依賴。
- **`make test`**: 執行所有 `pytest` 測試。
- **`make quality-check`**: 執行所有程式碼品質檢查 (格式、風格、型別)。
- **`make all-tests`**: 執行所有測試和品質檢查，提交前必做。

---

## 伍、違規行為自動檢測腳本 (概念)

這是一個我應在內心運行的腳本，以確保我的行為符合規範。

```bash
#!/bin/bash
# self_check.sh - 自我檢查腳本

echo "🔍 檢查潛在違規跡象..."

# 檢查模擬代碼
if grep -r "time.sleep" src/; then
    echo "❌ 發現模擬延遲代碼，可能違反反假測試原則。"
    # exit 1
fi

# 檢查功能重複
if grep -r "_v[0-9]\|_old\|_backup\|_legacy" src/; then
    echo "❌ 發現舊版本函式未清理，違反功能替換原則。"
    # exit 1
fi

echo "✅ 所有檢查通過。"
```

---
*V4 - 2025-06-26 - 此文件是 `CLAUDE.md` 的直接、完整的移植和適配，並已通過對專案 133 個檔案的深度分析進行了驗證。此為我的核心操作系統。*