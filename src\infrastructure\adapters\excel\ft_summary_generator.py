#!/usr/bin/env python3
"""FT_Summary_Generator - 動態欄位橫向拼接生成器"""
import os, time, pandas as pd
from typing import Dict, List, Any
from dataclasses import dataclass
import sys
sys.path.append(os.path.join(os.path.dirname(os.path.abspath(__file__)), '../../../../'))
from src.infrastructure.adapters.excel.csv_to_excel_converter import CsvToExcelConverter

@dataclass
class FTSummaryResult:
    success: bool
    output_file: str
    processed_files: List[str]
    processing_time: float
    error_message: str = ""

class FTSummaryGenerator:
    def __init__(self, enable_logging: bool = True):
        self.enable_logging = enable_logging
        self.csv_converter = CsvToExcelConverter()
        self._log("🚀 初始化 FT_Summary_Generator (動態欄位版)")
    
    def _log(self, message: str):
        if self.enable_logging:
            print(message)

    def process_csv_to_step7_with_summary(self, csv_file_path: str) -> Dict[str, Any]:
        """直接使用CsvToExcelConverter前7步驟"""
        self._log(f"🔄 執行前7步驟：{os.path.basename(csv_file_path)}")
        df = self.csv_converter._safe_read_csv(csv_file_path)
        df = self.csv_converter.complete_csv_structure(df)
        df = self.csv_converter.assign_bin_numbers_df(df)
        df, protection_positions = self.csv_converter.apply_bin1_protection_df(df)
        df, fail_positions = self.csv_converter.assign_bins_to_devices(df)
        site_column, _ = self.csv_converter.site_finder.find_site_column_with_log(df, csv_file_path, header_row=7, start_col=2)
        summary_data = self.csv_converter.summary_generator.generate_summary(df, site_column, csv_file_path)
        self._log("✅ 前7步驟完成")
        return {
            'processed_df': df,
            'summary_data': summary_data,
            'site_column': site_column,
            'fail_positions': fail_positions
        }

    def _calculate_statistics_from_bin_rows(self, bin_rows: List) -> tuple:
        total = pass_count = 0
        for row in bin_rows:
            if len(row) > 1 and isinstance(row[1], (int, float)):
                count = int(row[1])
                total += count
                if len(row) > 0 and row[0] == 1:
                    pass_count = count
        return total, pass_count, total - pass_count

    def _calculate_statistics_from_summary_data(self, summary_data: Dict[str, Any]) -> tuple:
        try:
            if isinstance(summary_data, dict) and 'basic_stats' in summary_data:
                stats = summary_data['basic_stats']
                if isinstance(stats, dict):
                    return stats.get('total_devices', 0), stats.get('pass_devices', 0), stats.get('fail_devices', 0)
            if isinstance(summary_data, dict) and 'bin_rows' in summary_data:
                return self._calculate_statistics_from_bin_rows(summary_data['bin_rows'])
            return 0, 0, 0
        except Exception as e:
            self._log(f"⚠️ 統計計算錯誤: {str(e)}")
            return 0, 0, 0

    def read_csv_metadata(self, csv_file_path: str) -> Dict[str, str]:
        try:
            df = self.csv_converter._safe_read_csv(csv_file_path)
            metadata = {
                'computer': str(df.iloc[4, 1]).strip() if len(df) > 4 and len(df.columns) > 1 else 'Unknown',
                'date': str(df.iloc[5, 1]).strip() if len(df) > 5 and len(df.columns) > 1 else 'Unknown',
                'lot_id': os.path.basename(csv_file_path).replace('.csv', '.xlsx')
            }
            return metadata
        except Exception as e:
            self._log(f"⚠️ 讀取metadata失敗: {str(e)}")
            return {'computer': 'Unknown', 'date': 'Unknown', 'lot_id': os.path.basename(csv_file_path).replace('.csv', '.xlsx')}

    def save_summary_file(self, summary_data: Dict[str, Any], original_csv_path: str) -> str:
        """
        保存Summary檔案（標準格式：統計區+BIN表格）
        """
        base_name = os.path.basename(original_csv_path).replace('.csv', '')
        output_dir = os.path.dirname(original_csv_path)
        summary_path = os.path.join(output_dir, f"{base_name}_summary.csv")
        
        # 讀取CSV metadata
        metadata = self.read_csv_metadata(original_csv_path)
        
        # 計算統計數據（使用提取的函數）
        total, pass_count, fail_count = self._calculate_statistics_from_summary_data(summary_data)
        yield_rate = f"{(pass_count/total*100):.3f}%" if total > 0 else "0.000%"
        
        # 組建新的CSV結構
        all_rows = []
        
        # 前4行統計區
        all_rows.append(['Total', total, 'Date:', metadata['date']])
        all_rows.append(['Pass', pass_count, 'Computer:', metadata['computer']])
        all_rows.append(['Fail', fail_count, 'Lot ID:', metadata['lot_id']])
        all_rows.append(['Yield', yield_rate, '', ''])
        
        # 第5行：Site總計行（從步驟7抓取）
        if isinstance(summary_data, dict) and 'site_total_row' in summary_data:
            all_rows.append(summary_data['site_total_row'])
        else:
            all_rows.append(['', '', '', ''])  # 備用空行
        
        # BIN統計表格標頭
        if isinstance(summary_data, dict) and 'headers' in summary_data:
            all_rows.append(summary_data['headers'])
        
        # BIN統計數據
        if isinstance(summary_data, dict) and 'bin_rows' in summary_data:
            all_rows.extend(summary_data['bin_rows'])
        
        # 保存為CSV
        df_summary = pd.DataFrame(all_rows)
        df_summary.to_csv(summary_path, index=False, header=False, encoding='utf-8')
        
        self._log(f"📁 生成標準Summary：{os.path.basename(summary_path)}")
        self._log(f"📊 統計: Total={total}, Pass={pass_count}, Fail={fail_count}, Yield={yield_rate}")
        self._log(f"📊 Metadata: Computer={metadata['computer']}, Date={metadata['date']}")
        self._log(f"📄 結構: {len(all_rows)}行 CSV格式")
        
        return summary_path

    def extract_summary_data(self, df: pd.DataFrame, csv_file_path: str) -> Dict[str, Any]:
        """
        從處理後的DataFrame提取Summary數據
        """
        self._log("📊 提取Summary數據")
        
        # 使用內建的summary_generator
        site_column, _ = self.csv_converter.site_finder.find_site_column_with_log(df, csv_file_path, header_row=7, start_col=2)
        summary_data = self.csv_converter.summary_generator.generate_summary(df, site_column, csv_file_path)
        
        return summary_data

    def create_horizontal_summary(self, summary_blocks: List[Dict]) -> Dict[str, Any]:
        """
        橫向拼接多個Summary區塊
        """
        self._log("🔗 執行橫向拼接")
        
        if not summary_blocks:
            return {}
        
        # 簡化的橫向拼接邏輯
        combined_summary = {
            'files': [block['file_name'] for block in summary_blocks],
            'total_files': len(summary_blocks),
            'summary_data': [block['summary'] for block in summary_blocks]
        }
        
        return combined_summary

    def generate_ft_summary(self, csv_file_path: str) -> FTSummaryResult:
        """
        主要執行函數：單檔案FT Summary生成
        """
        start_time = time.perf_counter()
        
        try:
            self._log("=" * 60)
            self._log("🎯 開始FT Summary生成（簡化版）")
            self._log(f"📁 處理檔案：{os.path.basename(csv_file_path)}")
            self._log("=" * 60)
            
            # 執行完整7步驟（包含Summary生成）
            result = self.process_csv_to_step7_with_summary(csv_file_path)
            
            # 保存Summary檔案
            summary_path = self.save_summary_file(result['summary_data'], csv_file_path)
            
            # 橫向拼接簡化（單檔案情況）
            horizontal_summary = {
                'file_name': os.path.basename(csv_file_path),
                'summary_data': result['summary_data'],
                'processing_complete': True
            }
            
            processing_time = time.perf_counter() - start_time
            
            self._log("=" * 60)
            self._log("🎉 FT Summary生成完成")
            self._log(f"📊 處理時間：{processing_time:.2f}秒")
            self._log(f"📁 Summary檔案：{summary_path}")
            self._log("=" * 60)
            
            return FTSummaryResult(
                True, summary_path, [csv_file_path], processing_time
            )
            
        except Exception as e:
            processing_time = time.perf_counter() - start_time
            return FTSummaryResult(False, "", [], processing_time, f"生成失敗：{str(e)}")
    
    def _analyze_summary_structure(self, summary_file: str) -> Dict[str, Any]:
        """代理1：分析Summary檔案結構特徵"""
        try:
            df = pd.read_csv(summary_file, header=None)
            headers_row = df.iloc[5]  # 第6行是標題行
            headers = [str(h) for h in headers_row if pd.notna(h) and str(h).strip()]
            
            # 計算欄位數量
            total_columns = len(headers)
            
            # 基本欄位：Bin,Count,%,Definition,Note = 5欄
            basic_columns = 5
            
            # Site欄位：每個Site佔2欄(Site X, %)
            site_columns = total_columns - basic_columns
            site_count = site_columns // 2
            
            self._log(f"📊 分析 {os.path.basename(summary_file)}: {total_columns}欄 ({site_count}個Site)")
            
            return {
                'filename': os.path.basename(summary_file).replace('_summary.csv', ''),
                'file_path': summary_file,
                'total_columns': total_columns,
                'basic_columns': basic_columns,
                'site_count': site_count,
                'site_columns': site_columns,
                'headers': headers,
                'data': df
            }
        except Exception as e:
            self._log(f"⚠️ 分析Summary結構失敗: {str(e)}")
            return None
    
    def _calculate_horizontal_layout(self, summaries_metadata: List[Dict]) -> List[Dict]:
        """代理2：計算橫向布局方案"""
        layout = []
        current_start = 0
        
        for meta in summaries_metadata:
            layout.append({
                'filename': meta['filename'],
                'start_col': current_start,
                'end_col': current_start + meta['total_columns'] - 1,
                'column_count': meta['total_columns'],
                'site_count': meta['site_count']
            })
            current_start += meta['total_columns']
        
        # 添加TOTAL欄位布局
        max_site_count = max(meta['site_count'] for meta in summaries_metadata)
        total_columns = 5 + (max_site_count * 2)  # 使用最大Site數
        
        layout.append({
            'filename': 'TOTAL',
            'start_col': current_start,
            'end_col': current_start + total_columns - 1,
            'column_count': total_columns,
            'site_count': max_site_count
        })
        
        self._log(f"🗂️ 橫向布局: 總寬度={current_start + total_columns}欄")
        return layout
    
    def _calculate_total_statistics(self, summaries_metadata: List[Dict]) -> tuple:
        """代理4：計算TOTAL統計和BIN定義映射 - 修復版本"""
        # 基本統計計算
        total_count = sum(int(meta['data'].iloc[0, 1]) for meta in summaries_metadata)
        pass_count = sum(int(meta['data'].iloc[1, 1]) for meta in summaries_metadata)
        fail_count = sum(int(meta['data'].iloc[2, 1]) for meta in summaries_metadata)
        yield_rate = f"{(pass_count/total_count*100):.3f}%" if total_count > 0 else "0.000%"
        
        # 第一步：收集所有檔案的所有BIN和Definition
        all_bins_data = {}  # {bin_name: {'count': total, 'definition': str, 'sites': {site_no: count}}}
        bin_definitions = {}
        max_site_count = max(meta['site_count'] for meta in summaries_metadata)
        
        # 掃描所有檔案收集BIN數據
        for meta in summaries_metadata:
            for i in range(6, len(meta['data'])):  # 從第7行開始是BIN數據
                if pd.notna(meta['data'].iloc[i, 0]):
                    bin_name = str(meta['data'].iloc[i, 0])
                    if bin_name.lower() != 'bin':  # 跳過標題行
                        # 初始化BIN數據結構
                        if bin_name not in all_bins_data:
                            all_bins_data[bin_name] = {
                                'count': 0,
                                'sites': {site_no: 0 for site_no in range(1, max_site_count + 1)}
                            }
                        
                        # 累計總數
                        if pd.notna(meta['data'].iloc[i, 1]):
                            count = int(meta['data'].iloc[i, 1])
                            all_bins_data[bin_name]['count'] += count
                        
                        # 提取Definition (優先使用最完整的定義)
                        if len(meta['data'].columns) > 3 and pd.notna(meta['data'].iloc[i, 3]):
                            definition = str(meta['data'].iloc[i, 3]).strip()
                            if definition and definition != f"Bin {bin_name}":
                                bin_definitions[bin_name] = definition
                        
                        # 如果還沒有定義，使用預設
                        if bin_name not in bin_definitions:
                            bin_definitions[bin_name] = f"Bin {bin_name}"
                        
                        # 累計Site統計
                        site_col_start = 5  # Site數據從第6欄開始
                        for site_no in range(1, meta['site_count'] + 1):
                            site_col_idx = site_col_start + (site_no - 1) * 2
                            if site_col_idx < len(meta['data'].columns) and pd.notna(meta['data'].iloc[i, site_col_idx]):
                                site_count = int(meta['data'].iloc[i, site_col_idx])
                                all_bins_data[bin_name]['sites'][site_no] += site_count
        
        # 第二步：計算各Site的總數（用於百分比計算）
        site_totals = {}
        for site_no in range(1, max_site_count + 1):
            site_totals[site_no] = sum(bin_data['sites'][site_no] for bin_data in all_bins_data.values())
        
        # 第三步：排序BIN（BIN=1優先，其他按總數降序）
        def bin_sort_key(bin_name):
            if str(bin_name) == '1':
                return (0, -999999)  # BIN=1永遠第一，用負數確保在reverse=True時排最前面
            else:
                return (1, -all_bins_data[bin_name]['count'])  # 其他按總數降序，用負數實現降序
        
        sorted_bins = sorted(all_bins_data.keys(), key=bin_sort_key, reverse=False)
        
        # 構建統計結果
        total_bins = {bin_name: all_bins_data[bin_name]['count'] for bin_name in sorted_bins}
        site_bins = {bin_name: all_bins_data[bin_name]['sites'] for bin_name in sorted_bins}
        
        total_stats = {
            'total_count': total_count,
            'pass_count': pass_count,
            'fail_count': fail_count,
            'yield_rate': yield_rate,
            'reference_bins': sorted_bins,  # 改名但保持相容性
            'total_bins': total_bins,
            'site_bins': site_bins,
            'site_totals': site_totals,  # 新增：各Site總數
            'max_site_count': max_site_count
        }
        
        self._log(f"📊 TOTAL統計: Total={total_count}, Pass={pass_count}, Fail={fail_count}, Yield={yield_rate}")
        self._log(f"📋 收集到 {len(all_bins_data)} 個不同的BIN")
        self._log(f"📋 BIN排序: {sorted_bins[:5]}{'...' if len(sorted_bins) > 5 else ''} (BIN=1優先)")
        self._log(f"📊 Site總數: {site_totals}")
        
        return total_stats, bin_definitions
    
    def _generate_horizontal_csv(self, summaries_metadata: List[Dict], layout: List[Dict], 
                                total_stats: Dict, bin_definitions: Dict, output_path: str):
        """代理3：生成橫向CSV內容"""
        with open(output_path, 'w', encoding='utf-8') as f:
            # 找出最長的檔案行數，並確保有足夠行數顯示所有BIN
            max_rows = max(len(meta['data']) for meta in summaries_metadata)
            total_bins_count = len(total_stats['reference_bins'])
            required_rows = 6 + total_bins_count  # 6行標題 + 所有BIN數據
            max_rows = max(max_rows, required_rows)  # 確保有足夠行數顯示所有BIN
            
            # 逐行生成橫向CSV
            for row_idx in range(max_rows):
                full_row = []
                
                # 為每個Summary檔案生成該行的內容
                for i, meta in enumerate(summaries_metadata):
                    layout_info = layout[i]
                    
                    if row_idx < len(meta['data']):
                        # 取得該行的所有欄位數據
                        row_data = meta['data'].iloc[row_idx].fillna('')
                        # 確保填滿該Summary的欄位數量
                        summary_cols = []
                        for col_idx in range(layout_info['column_count']):
                            if col_idx < len(row_data):
                                summary_cols.append(str(row_data.iloc[col_idx]))
                            else:
                                summary_cols.append('')
                        full_row.extend(summary_cols)
                    else:
                        # 該Summary行數不足，填入空值
                        full_row.extend([''] * layout_info['column_count'])
                
                # 生成TOTAL Summary該行內容
                total_layout = layout[-1]  # 最後一個是TOTAL
                total_cols = self._generate_total_row(row_idx, total_stats, bin_definitions, 
                                                    total_layout['column_count'])
                full_row.extend(total_cols)
                
                # 輸出該行
                f.write(','.join(full_row) + '\n')
        
        self._log(f"📄 生成橫向CSV: {max_rows}行 × {layout[-1]['end_col']+1}欄")
    
    def _generate_total_row(self, row_idx: int, total_stats: Dict, bin_definitions: Dict, 
                           column_count: int) -> List[str]:
        """生成TOTAL Summary的指定行"""
        if row_idx == 0:  # Total行
            return ['Total', str(total_stats['total_count']), 'Date:', 'Multiple Files'] + [''] * (column_count - 4)
        elif row_idx == 1:  # Pass行
            return ['Pass', str(total_stats['pass_count']), 'Computer:', 'Batch Process'] + [''] * (column_count - 4)
        elif row_idx == 2:  # Fail行
            return ['Fail', str(total_stats['fail_count']), 'Lot ID:', 'FT_SUMMARY.csv'] + [''] * (column_count - 4)
        elif row_idx == 3:  # Yield行
            return ['Yield', total_stats['yield_rate']] + [''] * (column_count - 2)
        elif row_idx == 4:  # 第5行：Site總計行
            row = ['', '', '', '', '']  # 前5欄空白
            site_count = (column_count - 5) // 2
            for site_no in range(1, site_count + 1):
                site_total = total_stats['site_totals'].get(site_no, 0)
                row.extend(['Total', str(site_total)])
            while len(row) < column_count:
                row.append('')
            return row
        elif row_idx == 5:  # BIN標題行
            # 動態生成標題：基本5欄 + Site欄位
            headers = ['Bin', 'Count', '%', 'Definition', 'Note']
            site_count = (column_count - 5) // 2
            for site_no in range(1, site_count + 1):
                headers.extend([f'Site {site_no}', '%'])
            # 補齊剩餘欄位
            while len(headers) < column_count:
                headers.append('')
            return headers
        else:  # BIN數據行
            bin_idx = row_idx - 6
            if bin_idx < len(total_stats['reference_bins']):
                bin_name = total_stats['reference_bins'][bin_idx]
                total_bin_count = total_stats['total_bins'][bin_name]
                total_bin_percentage = f"{(total_bin_count / total_stats['total_count'] * 100):.3f}" if total_stats['total_count'] > 0 else "0.000"
                
                # 使用實際Definition，不是硬編碼
                definition = bin_definitions.get(bin_name, f"Bin {bin_name}")
                
                row = [
                    bin_name,
                    str(total_bin_count),
                    total_bin_percentage,
                    definition,
                    ''  # Note欄位空白
                ]
                
                # 添加實際Site統計（修復版本）
                site_count = (column_count - 5) // 2
                for site_no in range(1, site_count + 1):
                    site_bin_count = total_stats['site_bins'][bin_name].get(site_no, 0)
                    # 使用各Site自己的總數計算百分比，而非全局總數
                    site_total = total_stats['site_totals'].get(site_no, 0)
                    site_bin_percentage = f"{(site_bin_count / site_total * 100):.3f}" if site_total > 0 else "0.000"
                    row.extend([str(site_bin_count), site_bin_percentage])
                
                # 補齊剩餘欄位
                while len(row) < column_count:
                    row.append('')
                
                return row
            else:
                # 該行沒有BIN數據，返回空行
                return [''] * column_count
    
    def generate_horizontal_summary(self, summary_files: List[str], output_path: str) -> FTSummaryResult:
        """橫向整合多個Summary檔案 - 動態欄位版本"""
        start_time = time.perf_counter()
        self._log("🔄 開始橫向整合Summary檔案 (動態欄位)")
        
        try:
            import pandas as pd
            
            # 代理1: 分析所有Summary檔案結構
            summaries_metadata = []
            for file_path in summary_files:
                if not os.path.exists(file_path):
                    continue
                meta = self._analyze_summary_structure(file_path)
                if meta:
                    summaries_metadata.append(meta)
            
            if not summaries_metadata:
                return FTSummaryResult(False, "", [], 0, "無有效Summary檔案可處理")
                
            # 代理2: 計算橫向布局方案
            layout = self._calculate_horizontal_layout(summaries_metadata)
            
            # 代理4: 計算TOTAL統計和BIN數據
            total_stats, bin_definitions = self._calculate_total_statistics(summaries_metadata)
            
            # 代理3: 生成橫向CSV內容
            self._generate_horizontal_csv(summaries_metadata, layout, total_stats, bin_definitions, output_path)
            
            processing_time = time.perf_counter() - start_time
            self._log(f"✅ 動態欄位橫向整合完成: {os.path.basename(output_path)}")
            self._log(f"📊 處理檔案: {len(summaries_metadata)}個Summary, 總寬度: {layout[-1]['end_col']+1}欄")
            
            # 新增：將 FT_SUMMARY.csv 轉換為 Excel
            try:
                self._log(f"📝 轉換 FT_SUMMARY.csv 為 Excel 格式...")
                success = self.csv_converter.convert_ft_summary_to_excel(output_path)
                if success:
                    excel_file = output_path.replace('.csv', '.xlsx')
                    self._log(f"✅ FT_SUMMARY Excel 生成完成：{os.path.basename(excel_file)}")
                    return FTSummaryResult(True, excel_file, summary_files, processing_time)
                else:
                    self._log(f"⚠️ Excel 轉換失敗，回傳 CSV 檔案")
                    return FTSummaryResult(True, output_path, summary_files, processing_time)
            except Exception as e:
                self._log(f"⚠️ Excel 轉換異常：{str(e)}，回傳 CSV 檔案")
                return FTSummaryResult(True, output_path, summary_files, processing_time)
            
        except Exception as e:
            processing_time = time.perf_counter() - start_time
            return FTSummaryResult(False, "", [], processing_time, f"橫向整合失敗：{str(e)}")


def test_horizontal_integration():
    generator = FTSummaryGenerator(enable_logging=True)
    summary_files = [
        "/mnt/d/project/python/outlook_summary/doc/20250523/Production Data/KDD0530D3.D_F2550176A_FT1R0_20250522230014_summary.csv",
        "/mnt/d/project/python/outlook_summary/doc/20250523/Production Data/KDD0530D3.D_F2550176A_FT1R1_20250522232553_summary.csv"
    ]
    output_path = "/mnt/d/project/python/outlook_summary/doc/20250523/FT_SUMMARY.csv"
    result = generator.generate_horizontal_summary(summary_files, output_path)
    print(f"✅ 橫向整合測試: {result.success}, {result.processing_time:.2f}秒" if result.success else f"❌ 失敗: {result.error_message}")

def main():
    csv_file = "/mnt/d/project/python/outlook_summary/doc/20250523/Production Data/KDD0530D3.D_F2550176A_FT1R0_20250522230014.csv"
    generator = FTSummaryGenerator(enable_logging=True)
    result = generator.generate_ft_summary(csv_file)
    print(f"✅ 單檔Summary: {result.processing_time:.2f}秒" if result.success else f"❌ 失敗: {result.error_message}")
    test_horizontal_integration()

if __name__ == "__main__":
    main()