#!/usr/bin/env python3
"""
檢查 convert_csv_to_excel 執行後的 spec 限值變化
"""

import os

def read_csv_limits(file_path):
    """讀取 CSV 檔案的限值"""
    try:
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
            lines = []
            for i, line in enumerate(f):
                if i >= 15:  # 只讀前15行
                    break
                lines.append(line.strip().split(','))
        
        if len(lines) >= 11:
            return {
                'test_ids': lines[6],      # 第7行
                'test_names': lines[7],    # 第8行  
                'max_limits': lines[9],    # 第10行
                'min_limits': lines[10]    # 第11行
            }
    except Exception as e:
        print(f'讀取失敗 {file_path}: {e}')
    return None

def main():
    print("🔍 檢查 convert_csv_to_excel 執行後的 spec 限值變化")
    print("=" * 70)
    
    # 檢查處理過程的中間檔案
    step3_file = None
    for filename in os.listdir('doc/'):
        if 'step3_protection' in filename and filename.endswith('.csv'):
            step3_file = f'doc/{filename}'
            break
    
    if not step3_file:
        print("❌ 找不到處理過程的中間檔案 (step3_protection)")
        return
    
    print(f"📂 找到處理後檔案: {step3_file}")
    
    # 讀取原始和處理後的限值
    original_file = 'doc/G2735KS1U-K(BA)_GHKR03.13_F2490018A_FT1_R0_ALL_20240910203816.csv'
    
    original_data = read_csv_limits(original_file)
    processed_data = read_csv_limits(step3_file)
    
    if not original_data or not processed_data:
        print("❌ 無法讀取限值資料")
        return
    
    print("\n🔍 比較 spec 限值變化:")
    
    changes = []
    max_cols = min(len(original_data['max_limits']), len(processed_data['max_limits']))
    
    for i in range(max_cols):
        orig_max = original_data['max_limits'][i] if i < len(original_data['max_limits']) else ''
        proc_max = processed_data['max_limits'][i] if i < len(processed_data['max_limits']) else ''
        orig_min = original_data['min_limits'][i] if i < len(original_data['min_limits']) else ''
        proc_min = processed_data['min_limits'][i] if i < len(processed_data['min_limits']) else ''
        
        # 檢查是否有變化 (改為 none)
        max_changed = orig_max != proc_max and proc_max.lower() == 'none'
        min_changed = orig_min != proc_min and proc_min.lower() == 'none'
        
        if max_changed or min_changed:
            test_id = original_data['test_ids'][i] if i < len(original_data['test_ids']) else f'Col{i+1}'
            test_name = original_data['test_names'][i] if i < len(original_data['test_names']) else f'Unknown{i+1}'
            changes.append({
                'test_id': test_id,
                'test_name': test_name,
                'orig_max': orig_max,
                'proc_max': proc_max,
                'orig_min': orig_min,
                'proc_min': proc_min,
                'max_changed': max_changed,
                'min_changed': min_changed
            })
    
    print(f"\n✅ 共 {len(changes)} 個測試項目的 spec 被 BIN1 保護機制放寬:")
    
    if changes:
        print("-" * 90)
        print(f"{'測項編號':15s} | {'測項名稱':25s} | {'Max變化':20s} | {'Min變化':20s}")
        print("-" * 90)
        
        for change in changes:
            test_id = change['test_id'][:14]
            test_name = change['test_name'][:24]
            
            max_change = f"{change['orig_max']} → {change['proc_max']}" if change['max_changed'] else "無變化"
            min_change = f"{change['orig_min']} → {change['proc_min']}" if change['min_changed'] else "無變化"
            
            print(f"{test_id:15s} | {test_name:25s} | {max_change:20s} | {min_change:20s}")
        
        print("-" * 90)
        
        # 統計
        max_changes = sum(1 for c in changes if c['max_changed'])
        min_changes = sum(1 for c in changes if c['min_changed'])
        
        print(f"\n📊 統計:")
        print(f"   Max 限值放寬: {max_changes} 個")
        print(f"   Min 限值放寬: {min_changes} 個")
        print(f"   總放寬限值數: {max_changes + min_changes} 個")
        print(f"   涉及測試項目: {len(changes)} 個")
        
    else:
        print("⚠️ 沒有發現任何 spec 限值變化")
    
    print(f"\n🎯 BIN1 保護邏輯: BIN1 設備會失敗且數值為 0 時才放寬限值")

if __name__ == "__main__":
    main()