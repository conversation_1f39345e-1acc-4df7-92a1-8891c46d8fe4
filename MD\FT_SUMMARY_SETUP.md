# FT Summary 設定指南

## 🚀 快速啟動

### 方法 1：直接使用 uvicorn（推薦）
```bash
# 確保在專案根目錄
nohup python3 -m uvicorn src.presentation.api.ft_eqc_api:app --host 0.0.0.0 --port 8010 --workers 1 > nohup.out &
```

### 方法 2：使用啟動腳本
```bash
python3 start_ft_summary_service.py
```

## 🔧 環境需求

### 必要依賴
```bash
pip install fastapi uvicorn pandas openpyxl python-dotenv pydantic loguru
```

### 虛擬環境（推薦）
```bash
# 啟動虛擬環境
source venv/bin/activate  # Linux/Mac
# 或
venv\Scripts\activate     # Windows

# 安裝依賴
pip install -r requirements.txt
```

## 🌐 訪問端點

- **FT Summary UI**: http://localhost:8010/ft-summary-ui
- **API 文檔**: http://localhost:8010/docs
- **健康檢查**: http://localhost:8010/health
- **FT Summary 狀態**: http://localhost:8010/api/ft-summary-status

## 🔍 故障排除

### 500 錯誤診斷
1. 檢查 FT Summary 狀態：
   ```bash
   curl http://localhost:8010/api/ft-summary-status
   ```

2. 檢查日誌：
   ```bash
   tail -f nohup.out
   ```

### 常見問題

#### 問題：`No module named 'pandas'`
**解決**：安裝缺少的依賴
```bash
pip install pandas openpyxl
```

#### 問題：`No module named 'batch_csv_to_excel_processor'`
**解決**：確保在專案根目錄啟動服務

#### 問題：`FT Summary 處理器無法使用`
**解決**：檢查所有依賴是否正確安裝
```bash
python3 simple_test.py
```

## 🎯 使用方式

### Web 介面
1. 開啟 http://localhost:8010/ft-summary-ui
2. 輸入資料夾路徑（例如：`doc/20250523/Production Data`）
3. 點擊「開始批量處理」
4. 等待處理完成並下載結果

### API 使用
```bash
curl -X POST http://localhost:8010/api/process_ft_summary \
  -H "Content-Type: application/json" \
  -d '{
    "folder_path": "doc/20250523/Production Data",
    "force_overwrite": false
  }'
```

## 📋 功能說明

1. **自動掃描**：掃描指定資料夾中的 CSV 檔案
2. **類型識別**：基於 B2 欄位識別 FT 類型檔案
3. **批量處理**：同時執行 Excel 轉換和 Summary 生成
4. **結果整併**：生成最終的 `FT_SUMMARY.csv`

## ✅ 驗證安裝

```bash
# 1. 測試核心功能
python3 simple_test.py

# 2. 檢查 API 狀態
curl http://localhost:8010/api/ft-summary-status

# 3. 測試完整功能
python3 test_ft_summary_api.py
```

---
**修復記錄**：已移除複雜的動態匯入，使用最簡化的整合方式