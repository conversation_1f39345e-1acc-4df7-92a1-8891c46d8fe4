================================================================================
                    EQC系統 API、UI功能和函式對應表格
================================================================================
生成時間: 2025-06-22 (更新版)
系統版本: EQC一鍵完成處理系統 v2.0 (完全模組化版本 + 向下相容)
API端點總數: 31個 (比舊版本多7個)
================================================================================

第一部分：API端點總覽 (31個端點)
================================================================================

🔧 核心系統端點 (3個)
序號 | HTTP方法 | API端點路徑                    | 函式名稱                    | 功能描述
-----|----------|--------------------------------|----------------------------|------------------------------------------
 1   | GET      | /health                        | health_check()             | 健康檢查端點 (新版本)
 2   | GET      | /api/health                    | health_check_legacy()      | 健康檢查端點 (向下相容) ✅
 3   | GET      | /                              | root()                     | 根路由
 4   | GET      | /ui                            | get_ui()                   | UI界面端點

⚙️ EQC處理端點 (6個)
 5   | POST     | /api/scan_eqc_bin1            | scan_eqc_bin1()            | 掃描EQC BIN1資料
 6   | POST     | /api/process_eqc_standard     | process_eqc_standard()     | EQC標準處理
 7   | POST     | /api/process_eqc_advanced     | process_eqc_advanced()     | EQC進階處理 (雙階段)
 8   | POST     | /api/process_online_eqc       | process_online_eqc()       | 完整Online EQC處理端點（主要功能）✅
 9   | POST     | /api/generate_eqc_step5_flow  | generate_eqc_step5_flow()  | 生成EQC Step5測試流程 (新版本)
10   | POST     | /api/eqc/generate_test_flow   | generate_test_flow_legacy()| 生成測試流程 (向下相容) ✅
11   | POST     | /api/analyze_eqc_real_data    | analyze_eqc_real_data()    | 分析EQC實際資料

📁 檔案管理端點 (9個)
12   | GET      | /api/upload_config            | get_upload_config()        | 取得上傳配置
13   | POST     | /api/upload_file              | upload_file()              | 檔案上傳 (新增)
14   | POST     | /api/upload_and_process       | upload_and_process()       | 上傳並處理檔案
15   | POST     | /api/upload_archive           | upload_archive()           | 壓縮檔上傳
16   | GET      | /api/download_file            | download_file()            | 檔案下載 ✅
17   | GET      | /api/download_report          | download_report()          | 報告下載
18   | POST     | /api/read_report              | read_report()              | 讀取報告內容
19   | POST     | /api/check_file_exists        | check_file_exists()        | 檢查檔案是否存在 ✅
20   | GET      | /api/archive_info             | get_archive_info()         | 壓縮檔資訊

📊 記錄管理端點 (3個)
21   | GET      | /api/today_processed_files    | get_today_processed_files()| 今日處理檔案
22   | POST     | /api/update_today_records     | update_today_records()     | 更新今日記錄
23   | POST     | /api/clear_duplicate_cache    | clear_duplicate_cache()    | 清除重複快取

🧹 清理服務端點 (6個)
24   | GET      | /api/temp_files_info          | get_temp_files_info()      | 暫存檔案資訊
25   | POST     | /api/cleanup_temp_files       | cleanup_temp_files()       | 清理臨時檔案
26   | POST     | /api/cleanup_files_manual     | manual_cleanup_files()     | 手動清理檔案
27   | GET      | /api/cleanup_status           | get_cleanup_status()       | 清理狀態
28   | POST     | /api/start_cleanup_scheduler  | start_cleanup_scheduler()  | 啟動清理調度器 (新增)
29   | POST     | /api/stop_cleanup_scheduler   | stop_cleanup_scheduler()   | 停止清理調度器 (新增)

🔄 向下相容端點 (2個)
30   | POST     | /api/ft_eqc_grouping          | ft_eqc_grouping()          | FT-EQC分組處理 (新增)
31   | POST     | /api/online_eqc_fail_analysis | online_eqc_fail_analysis() | Online EQC失敗分析 (新增)

================================================================================

第二部分：UI按鈕與JavaScript函式對應 (模組化架構)
================================================================================

按鈕名稱/功能                     | JavaScript函式              | 調用API                    | 功能描述
----------------------------------|------------------------------|---------------------------|---------------------------
🪄 一鍵完成程式碼對比              | processCompleteEQCWorkflow() | Step1: /api/process_online_eqc | 完整4步驟流程：
                                  |                              | Step2: /api/process_eqc_advanced | 中文路徑處理→EQCTOTALDATA生成→
                                  |                              | Step3: /api/analyze_eqc_real_data | 程式碼區間檢測→雙重搜尋→真實資料分析→最終整合
🔄 執行 Online EQC 處理            | processOnlineEQC(mode)       | /api/process_online_eqc   | 根據模式執行EQC處理（1=BIN1整合統計，2=完整TOTAL，3=兩者）
📊 掃描 EQC BIN1 資料              | scanEQCBin1()                | /api/scan_eqc_bin1        | 掃描並分析EQC BIN1資料
📈 EQC 標準處理                    | processEQCStandard()         | /api/process_eqc_standard | EQC標準處理流程
📈 EQC 進階處理                    | processEQCAdvanced()         | /api/process_eqc_advanced | EQC進階處理（雙階段）
📤 檔案上傳                        | handleFileUpload()           | /api/upload_archive       | 上傳ZIP/7Z/RAR檔案
📥 檔案下載                        | downloadFile()               | /api/download_file        | 下載處理結果檔案
🧹 清理暫存檔案                    | cleanupTempFiles()           | /api/cleanup_temp_files   | 清理系統暫存檔案
📋 檢查檔案                        | checkFileExists()            | /api/check_file_exists    | 檢查指定檔案是否存在
📖 讀取報告                        | readReport()                 | /api/read_report          | 讀取處理報告檔案
📊 今日處理記錄                    | getTodayProcessedFiles()     | /api/today_processed_files| 查看今日處理的檔案
🔄 更新記錄                        | updateTodayRecords()         | /api/update_today_records | 手動更新今日記錄
🗑️ 清除快取                        | clearDuplicateCache()        | /api/clear_duplicate_cache| 清除重複上傳快取

================================================================================

第三部分：v2.0 模組化架構與服務類別
================================================================================

🏗️ 新版本服務架構 (FastAPI依賴注入)
服務類別名稱                      | 主要方法                                | 功能描述
----------------------------------|----------------------------------------|---------------------------
EQCProcessingService              | process_online_eqc()                   | ⭐ 核心：完整11步驟Online EQC處理
                                  | process_eqc_standard()                 | EQC標準處理流程
                                  | process_eqc_advanced()                 | EQC進階處理（雙階段）
                                  | scan_eqc_bin1()                        | EQC BIN1掃描
                                  | generate_test_flow()                   | Step5測試流程生成
                                  | analyze_real_data()                    | 真實資料分析

FileManagementService             | get_upload_config()                    | 取得上傳配置
                                  | upload_file()                          | 檔案上傳處理
                                  | upload_and_process()                   | 上傳並處理
                                  | download_file()                        | 檔案下載
                                  | check_file_exists()                    | 檔案存在檢查
                                  | read_report_content()                  | 讀取報告內容
                                  | get_today_processed_files()            | 今日處理檔案
                                  | process_download_path()                | 路徑處理（Windows/Linux）
                                  | upload_and_extract_archive()           | 壓縮檔上傳解壓
                                  | get_archive_info()                     | 壓縮檔資訊

CleanupService                    | cleanup_temp_files()                   | 清理臨時檔案
                                  | get_temp_files_info()                  | 暫存檔案資訊
                                  | manual_cleanup_files()                 | 手動清理檔案
                                  | get_cleanup_status()                   | 清理狀態
                                  | start_scheduler()                      | 啟動清理調度器
                                  | stop_scheduler()                       | 停止清理調度器

🔧 核心處理器類別 (業務邏輯層)
EQCBin1FinalProcessorV2           | process_complete_eqc_integration()     | ⭐ 核心：完整12步驟EQC BIN1整合統計處理
                                  | one_click_complete_comparison()        | ⭐ 一鍵完成程式碼對比（4階段流程）
                                  | generate_eqc_total_data()              | 生成完整EQCTOTALDATA.csv

StandardEQCProcessor              | process_standard_eqc_pipeline()        | ⭐ 完整8步驟EQC處理流程
                                  | process_from_stage2_only()             | 第二階段處理（跳過EQCTOTALDATA生成）
                                  | process_code_comparison_pipeline()     | 簡化4步驟程式碼對比流程

ChinesePathProcessor              | process_chinese_paths_in_directory()   | ⭐ 繁中資料夾處理（步驟0）
                                  | auto_rename_folders()                  | 自動重命名中文資料夾
                                  | has_chinese_or_special_chars()         | 檢測中文字或特殊符號

🛠️ 工具類別
APIUtils                          | process_folder_path()                  | 路徑處理和轉換
                                  | validate_folder_path()                 | 路徑驗證
                                  | validate_file_path()                   | 檔案路徑驗證

ResponseFormatter                 | format_health_response()               | 健康檢查回應格式化
                                  | format_eqc_standard_response()         | EQC標準處理回應格式化
                                  | format_upload_result()                 | 上傳結果格式化
                                  | format_temp_files_info()               | 暫存檔案資訊格式化
                                  | format_archive_info_response()         | 壓縮檔資訊格式化

LoggingUtils                      | log_api_start()                        | API開始日誌
                                  | log_api_success()                      | API成功日誌
                                  | log_api_error()                        | API錯誤日誌

================================================================================

第四部分：DEBUG LOG生成機制
================================================================================

LOG檔案類型                      | 生成條件                     | 檔案命名規則                 | 觸發API/函式
---------------------------------|------------------------------|------------------------------|----------------------------
EQCTOTALDATA_DEBUG_*.log         | EQC_DETAILED_LOGS=true       | EQCTOTALDATA_DEBUG_YYYYMMDD_HHMMSS.log | process_complete_eqc_integration()
EQCTOTALDATA_Step3_DEBUG.log     | InsEqcRtData2 Step3執行       | 固定檔名                     | InsEqcRtData2Processor
EQCTOTALDATA_Step4_DEBUG.log     | InsEqcRtData2 Step4執行       | 固定檔名                     | InsEqcRtData2Processor
處理報告*.txt                     | 完整流程執行完成             | 包含處理統計和結果           | StandardEQCProcessor

================================================================================

第五部分：關鍵環境變數配置
================================================================================

環境變數                         | 預設值    | 功能說明                     | 影響範圍
---------------------------------|-----------|------------------------------|----------------------------
EQC_DETAILED_LOGS                | true      | 控制詳細DEBUG日誌生成         | 所有EQC處理流程
KEEP_INTERMEDIATE_FILES          | true      | 是否保留中間處理檔案         | 檔案清理機制
AI_PROVIDER                      | 未設定    | AI提供者選擇                 | RAG查詢功能
EMBEDDING_MODEL                  | 未設定    | 嵌入模型選擇                 | 向量檢索功能

================================================================================

第六部分：v2.0 完整4步驟處理流程對應表 (修復版)
================================================================================

UI操作                           | 實際執行流程                              | 生成的LOG/檔案
---------------------------------|------------------------------------------|---------------------------
點擊「一鍵完成程式碼對比」        | Step 1: processCompleteEQCWorkflow()     | EQCTOTALDATA_DEBUG_*.log
                                 | ↓ /api/process_online_eqc (mode=1)       | EQCTOTALDATA.csv
                                 | ↓ EQCProcessingService.process_online_eqc()| EQCTOTALDATA_RAW.csv
                                 | ↓ EQCBin1FinalProcessorV2.process_complete_eqc_integration() | EQCTOTALDATA.xlsx
                                 | ├── 步驟0: 中文路徑處理 (繁中資料夾處理)  | 路徑標準化日誌
                                 | ├── 步驟0A: 特定副檔名檔案自動刪除        | 檔案清理日誌
                                 | ├── 步驟0B: SPD檔案自動轉換為CSV          | SPD轉換日誌
                                 | ├── 步驟1: 執行 FT-EQC 配對處理           | 配對結果日誌
                                 | ├── 步驟2: 取得所有 EQC 檔案並按時間分類  | 檔案分類日誌
                                 | ├── 步驟3: 基於配對結果計算統計數據       | 統計計算日誌
                                 | ├── 步驟4: 找到 EQC BIN=1 golden IC      | BIN1檢測日誌
                                 | ├── 步驟6: 填入統計資料 (A9/B9, A10/B10)  | 統計填入日誌
                                 | ├── 步驟8: 生成帶超連結的 FT-EQC 失敗配對資料 | 超連結生成日誌
                                 | ├── 步驟9: 添加 FT-EQC 失敗配對資料       | 失敗資料日誌
                                 | ├── 步驟10: 添加帶超連結的 EQC RT 資料    | RT資料日誌
                                 | └── 步驟11: 生成 EQCTOTALDATA.csv 和 EQCTOTALDATA_RAW.csv | 最終生成日誌
                                 | Step 2: /api/process_eqc_advanced         | Stage2處理日誌
                                 | ↓ EQCProcessingService.process_eqc_advanced() |
                                 | ↓ StandardEQCProcessor.process_from_stage2_only() |
                                 | ├── 步驟0: 中文路徑處理 (第二階段確保一致性) | 路徑再次標準化
                                 | ├── 步驟2: 程式碼區間檢測 (find_code_region) | 區間檢測結果
                                 | ├── 步驟3: 雙重搜尋機制 (perform_corrected_dual_search) | 搜尋結果日誌
                                 | ├── 步驟4: InsEqcRtData2處理 (可選)       | InsEqcRtData2日誌
                                 | ├── 步驟5: Step5測試流程生成 (可選)       | 測試流程日誌
                                 | ├── 步驟6: Step6Excel生成標記 (可選)      | Excel標記日誌
                                 | ├── 步驟7: 最終Excel轉換Summary (可選)    | Summary轉換日誌
                                 | └── 步驟8: 生成最終處理報告               | 最終報告
                                 | Step 3: /api/analyze_eqc_real_data        | 真實資料分析結果
                                 | ↓ EQCProcessingService.analyze_real_data() | 分析統計日誌
                                 | Step 4: 前端整合顯示結果                  | UI顯示更新

執行「完整標準EQC流程」           | 1. StandardEQCProcessor                   | Step3_DEBUG.log
                                 | 2. 8步驟完整處理                          | Step4_DEBUG.log
                                 | 3. 生成Excel和Summary                     | 處理報告.txt
                                 | 4. 程式碼區間檢測+雙重搜尋                |

上傳檔案處理                     | 1. upload_archive()                       | 解壓縮LOG
                                 | 2. 自動檢測資料夾                         | 處理結果檔案
                                 | 3. 觸發EQC處理流程                        |

================================================================================

第七部分：v2.0 系統更新說明 (最新版)
================================================================================

✅ v2.0 主要架構更新：
   - 🏗️ 完全模組化架構：FastAPI依賴注入機制
   - 📦 服務層分離：EQCProcessingService, FileManagementService, CleanupService
   - 🔧 工具類統一：APIUtils, ResponseFormatter, LoggingUtils
   - 📁 中文路徑處理：步驟0 (ChinesePathProcessor) - 12步驟完整流程
   - 🎯 一鍵完成程式碼對比：4階段完整流程
   - 🔄 向下相容支援：31個API端點 (比舊版本多7個)

✅ v2.0 API端點更新：
   - 🆕 新增端點：5個額外功能端點
   - 🔄 向下相容：/api/health 和 /api/eqc/generate_test_flow 雙路徑支援
   - 🔧 路徑修復：檔案下載和檢查端點格式統一
   - 📊 功能增強：壓縮檔資訊、清理調度器、記錄管理

✅ v2.0 工作流程修復：
   - 🔥 核心修復：process_online_eqc 完整實現12步驟流程
   - 📋 步驟完整：步驟0(中文路徑) → 步驟0A(檔案刪除) → 步驟0B(SPD轉換) → 步驟1-11
   - 🎯 模式支援：完整支援mode=1,2,3三種處理模式
   - 🔗 流程整合：Step1→Step2→Step3→Step4 完整API調用鏈

✅ v2.0 代碼質量提升：
   - 📉 代碼精簡：主檔案從2221行減少到678行 (70%減少)
   - 🧩 模組分離：4個獨立服務模組，職責單一
   - 🛡️ 錯誤處理：統一異常處理和日誌記錄
   - 🔍 依賴注入：FastAPI專業級架構設計

================================================================================

第八部分：v2.0 系統架構摘要
================================================================================

層級結構：
UI層 (HTML/JavaScript)
  ↓
API層 (FastAPI端點)
  ↓
業務邏輯層 (EQCBin1FinalProcessorV2, StandardEQCProcessor)
  ↓
基礎設施層 (ChinesePathProcessor, 檔案處理、日誌記錄)

v2.0 完整功能流程：
步驟0: 繁中資料夾處理 → 步驟0A: 檔案清理 → 步驟0B: SPD轉換 →
步驟1-11: EQC BIN1整合統計處理 → 程式碼區間檢測 → 雙重搜尋機制 →
真實資料分析 → 最終整合顯示

主要輸出：
- EQCTOTALDATA.csv（主要資料檔案）
- EQCTOTALDATA_RAW.csv（原始資料備份）
- EQCTOTALDATA_DEBUG_*.log（詳細處理日誌）⭐
- EQC_一鍵完成程式碼對比報告.json（完整報告）⭐
- Excel檔案（含格式和超連結）
- 各步驟DEBUG日誌

================================================================================

第九部分：v2.0 完整4步驟流程詳解 (修復版 - 12步驟)
================================================================================

🎯 前端按鈕 processCompleteEQCWorkflow() [JavaScript]
    ↓
📡 Step 1: /api/process_online_eqc (mode='1') [API調用]
    ↓
🔧 EQCProcessingService.process_online_eqc() [服務層]
    ↓
⚙️ EQCBin1FinalProcessorV2.process_complete_eqc_integration() [處理器層]
    ├── 🔧 步驟0: 中文路徑處理（繁中資料夾處理）✅
    │   └── ChinesePathProcessor.process_chinese_paths_in_directory()
    ├── 🗑️ 步驟0A: 特定副檔名檔案自動刪除 ✅
    ├── 🔄 步驟0B: SPD檔案自動轉換為CSV ✅
    ├── 🔗 步驟1: 執行 FT-EQC 配對處理 ✅
    ├── 📁 步驟2: 取得所有 EQC 檔案並按時間分類 ✅
    ├── 📊 步驟3: 基於配對結果計算統計數據 ✅
    ├── 🎯 步驟4: 找到 EQC BIN=1 golden IC ✅
    ├── 📋 步驟6: 填入統計資料 (A9/B9, A10/B10) ✅
    ├── 🔗 步驟8: 生成帶超連結的 FT-EQC 失敗配對資料 ✅
    ├── ➕ 步驟9: 添加 FT-EQC 失敗配對資料 ✅
    ├── 📈 步驟10: 添加帶超連結的 EQC RT 資料 ✅
    └── 💾 步驟11: 生成 EQCTOTALDATA.csv 和 EQCTOTALDATA_RAW.csv ✅
    ↓
📡 Step 2: /api/process_eqc_advanced [API調用]
    ↓
🔧 EQCProcessingService.process_eqc_advanced() [服務層 - 雙階段處理]
    ├── 🏗️ 階段1：EQCTOTALDATA 生成 (EQCBin1FinalProcessorV2)
    └── 🔍 階段2：StandardEQC 處理 (StandardEQCProcessor.process_from_stage2_only())
        ├── 🔧 步驟0: 中文路徑處理 (第二階段確保一致性) ✅
        ├── 🎯 步驟2: 程式碼區間檢測 (find_code_region) ✅
        ├── 🔍 步驟3: 雙重搜尋機制 (perform_corrected_dual_search) ✅
        ├── 📊 步驟4: InsEqcRtData2處理 (可選) ✅
        ├── 🧪 步驟5: Step5測試流程生成 (可選) ✅
        ├── 📝 步驟6: Step6Excel生成標記 (可選) ✅
        ├── 📋 步驟7: 最終Excel轉換Summary (可選) ✅
        └── 📄 步驟8: 生成最終處理報告 ✅
    ↓
📡 Step 3: /api/analyze_eqc_real_data [API調用]
    ↓
🔧 EQCProcessingService.analyze_real_data() [服務層]
    ├── 📊 讀取 EQCTOTALDATA.xlsx 檔案
    ├── 📈 分析 Online EQC FAIL 和 EQC RT PASS 數據
    ├── 📋 解析 Summary sheet 資料
    └── 🔍 分析 Debug 日誌匹配結果
    ↓
🖥️ Step 4: 前端整合顯示結果 [UI層]
    ├── 📊 顯示處理統計結果
    ├── 📁 提供檔案下載連結
    ├── 📋 顯示今日處理記錄
    └── 🎯 整合所有分析結果

================================================================================
                                   表格結束
================================================================================

🎯 v2.0 使用說明 (更新版)：
1. 🪄 點擊「一鍵完成程式碼對比」按鈕執行完整4步驟流程
2. 🔄 系統自動執行12步驟：
   步驟0(中文路徑處理) → 步驟0A(檔案刪除) → 步驟0B(SPD轉換) →
   步驟1-11(EQC BIN1整合統計) → 程式碼區間檢測 → 雙重搜尋 → 真實資料分析 → 最終整合
3. ⚙️ 確保環境變數EQC_DETAILED_LOGS=true以生成詳細日誌
4. 💾 所有處理結果和DEBUG日誌都會保存在處理的資料夾中
5. 📥 支援自動下載EQCTOTALDATA.xlsx和EQCTOTALDATA_RAW.csv檔案
6. 🔄 完全向下相容：支援舊版本所有API路徑
7. 🏗️ 模組化架構：31個API端點，比舊版本多7個功能

🔧 技術特色：
- FastAPI依賴注入架構
- 服務層完全分離 (EQC處理/檔案管理/清理服務)
- 統一錯誤處理和日誌記錄
- Windows/Linux路徑自動轉換
- 壓縮檔自動解壓和處理

📊 性能提升：
- 代碼行數減少70% (2221行 → 678行)
- 模組化設計提升維護性
- 統一工具類減少重複代碼
- 專業級API架構設計

更新日期：2025-06-22 (最新修復版)
系統狀態：v2.0 完全模組化版本 + 向下相容，所有31個API端點正常運行 ✅