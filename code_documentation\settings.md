# settings.py

配置管理系統實作模組，使用 Pydantic 進行配置驗證和管理。

## DatabaseConfig

資料庫配置模型，繼承自 BaseModel，用於設定資料庫連接相關參數。

### 屬性
- `driver` (Literal["sqlite", "postgresql"]): 資料庫驅動，預設為 "sqlite"
- `host` (str): 主機位址，預設為 "localhost"
- `port` (int): 埠號，預設為5432，必須大於0
- `database_name` (str): 資料庫名稱，預設為 "outlook_summary"
- `username` (str): 使用者名稱，預設為空字串
- `password` (str): 密碼，預設為空字串

### validate_driver

驗證資料庫驅動的類別方法。

**參數:**
- `v` (str): 驅動名稱

**返回值:**
- str: 驗證通過的驅動名稱

**異常:**
- ValueError: 當驅動不支援時拋出

### connection_string

生成資料庫連線字串的屬性。

**返回值:**
- str: 資料庫連線字串

**異常:**
- ValueError: 當驅動不支援時拋出

## EmailConfig

郵件配置模型，繼承自 BaseModel，用於設定郵件相關參數。

### 屬性
- `monitor_enabled` (bool): 是否啟用監控，預設為True
- `check_interval` (int): 檢查間隔（秒），預設為30，必須大於0
- `max_retries` (int): 最大重試次數，預設為3，必須大於等於0
- `sender_address` (str): 寄件者地址，預設為空字串
- `sender_password` (str): 寄件者密碼，預設為空字串

### validate_check_interval

驗證檢查間隔的類別方法。

**參數:**
- `v` (int): 檢查間隔

**返回值:**
- int: 驗證通過的檢查間隔

**異常:**
- ValueError: 當檢查間隔小於等於0時拋出

## BIN1LogConfig

BIN1 保護機制 LOG 配置模型，繼承自 BaseModel。

### 屬性
- `enabled` (bool): 是否啟用，預設為True
- `save_protection_log` (bool): 是否儲存保護日誌，預設為True
- `save_analysis_log` (bool): 是否儲存分析日誌，預設為True
- `log_directory` (str): 日誌目錄，預設為 "."
- `include_timestamp` (bool): 是否包含時間戳，預設為True

## Settings

主要配置設定模型，繼承自 BaseModel，包含所有基礎配置。

### 屬性
- `app_name` (str): 應用程式名稱，預設為 "Outlook Summary System"
- `debug` (bool): 是否啟用除錯模式，預設為False
- `log_level` (Literal["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]): 日誌級別，預設為 "INFO"
- `temp_dir` (Path): 暫存目錄，預設為 Path("/tmp/outlook_summary")
- `database` (DatabaseConfig): 資料庫配置，預設為新實例
- `email` (EmailConfig): 郵件配置，預設為新實例
- `bin1_log` (BIN1LogConfig): BIN1 日誌配置，預設為新實例

### validate_log_level

驗證日誌級別的類別方法。

**參數:**
- `v` (str): 日誌級別

**返回值:**
- str: 驗證通過的日誌級別

**異常:**
- ValueError: 當日誌級別無效時拋出

### from_dict

從字典建立配置的類別方法。

**參數:**
- `config_data` (Dict[str, Any]): 配置字典

**返回值:**
- Settings: 配置實例

### from_environment

從環境變數載入配置的類別方法。

**返回值:**
- Settings: 從環境變數載入的配置實例

**環境變數:**
- `OUTLOOK_DEBUG`: 除錯模式開關
- `OUTLOOK_LOG_LEVEL`: 日誌級別
- `OUTLOOK_TEMP_DIR`: 暫存目錄
- `OUTLOOK_DB_DRIVER`: 資料庫驅動
- `OUTLOOK_DB_HOST`: 資料庫主機
- `OUTLOOK_DB_PORT`: 資料庫埠號
- `OUTLOOK_DB_NAME`: 資料庫名稱
- `OUTLOOK_DB_USER`: 資料庫使用者
- `OUTLOOK_DB_PASSWORD`: 資料庫密碼
- `OUTLOOK_EMAIL_ENABLED`: 郵件監控開關
- `OUTLOOK_EMAIL_INTERVAL`: 郵件檢查間隔
- `OUTLOOK_EMAIL_RETRIES`: 郵件重試次數
- `OUTLOOK_EMAIL_SENDER`: 寄件者地址
- `OUTLOOK_EMAIL_PASSWORD`: 寄件者密碼
- `BIN1_LOG_ENABLED`: BIN1 日誌開關
- `BIN1_LOG_SAVE_PROTECTION`: 保護日誌儲存開關
- `BIN1_LOG_SAVE_ANALYSIS`: 分析日誌儲存開關
- `BIN1_LOG_DIRECTORY`: 日誌目錄
- `BIN1_LOG_INCLUDE_TIMESTAMP`: 時間戳包含開關

### to_dict

轉換為字典的方法。

**返回值:**
- Dict[str, Any]: 配置字典，確保 Path 物件被序列化為字串
