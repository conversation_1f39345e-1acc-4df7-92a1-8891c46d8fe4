"""
檔案上傳處理模組

提供檔案上傳、大小驗證、格式檢查、重複防護等功能。
"""

import asyncio
import hashlib
import logging
import time
from pathlib import Path
from typing import Optional, Dict, Any, Set
from fastapi import UploadFile, HTTPException
from .upload_config import UploadConfig, upload_config
from .temp_file_manager import TempFileManager


logger = logging.getLogger(__name__)


class UploadError(Exception):
    """檔案上傳錯誤"""
    pass


class FileSizeError(UploadError):
    """檔案大小錯誤"""
    pass


class FileFormatError(UploadError):
    """檔案格式錯誤"""
    pass


class DuplicateUploadError(UploadError):
    """重複上傳錯誤"""
    pass


class UploadProcessor:
    """檔案上傳處理器（包含重複防護和狀態鎖定）"""
    
    def __init__(self, config: Optional[UploadConfig] = None):
        """
        初始化檔案上傳處理器
        
        Args:
            config: 上傳配置，若為 None 則使用預設配置
        """
        self.config = config or upload_config
        self.temp_manager = TempFileManager(self.config)
        
        # 重複防護和狀態管理
        self.has_duplicate_prevention = True
        self.is_uploading = False
        self._upload_lock = asyncio.Lock()
        self._recent_uploads: Dict[str, float] = {}  # file_hash -> timestamp
        self._upload_hashes: Set[str] = set()  # 已上傳檔案的hash集合
        self._duplicate_check_window = 300  # 5分鐘內的重複檢查視窗
    
    def validate_file_size(self, file_size: int) -> None:
        """
        驗證檔案大小
        
        Args:
            file_size: 檔案大小（位元組）
            
        Raises:
            FileSizeError: 檔案大小超過限制
        """
        if file_size > self.config.max_upload_size_bytes:
            max_size_mb = self.config.max_upload_size_mb
            actual_size_mb = file_size / (1024 * 1024)
            
            error_msg = f"檔案大小超過限制: {actual_size_mb:.1f}MB > {max_size_mb}MB"
            logger.warning(error_msg)
            raise FileSizeError(error_msg)
    
    def validate_file_format(self, filename: str) -> None:
        """
        驗證檔案格式
        
        Args:
            filename: 檔案名稱
            
        Raises:
            FileFormatError: 不支援的檔案格式
        """
        file_extension = Path(filename).suffix.lower().lstrip('.')
        
        if not self.config.is_supported_format(file_extension):
            supported_formats = ', '.join(self.config.supported_archive_formats)
            error_msg = f"不支援的檔案格式: {file_extension}。支援格式: {supported_formats}"
            logger.warning(error_msg)
            raise FileFormatError(error_msg)
    
    def _calculate_file_hash(self, file_content: bytes) -> str:
        """
        計算檔案內容的 SHA256 雜湊值
        
        Args:
            file_content: 檔案內容
            
        Returns:
            str: SHA256 雜湊值
        """
        return hashlib.sha256(file_content).hexdigest()
    
    def _cleanup_old_upload_records(self) -> None:
        """清理過期的上傳記錄"""
        current_time = time.time()
        expired_hashes = [
            file_hash for file_hash, timestamp in self._recent_uploads.items()
            if current_time - timestamp > self._duplicate_check_window
        ]
        
        for file_hash in expired_hashes:
            self._recent_uploads.pop(file_hash, None)
            self._upload_hashes.discard(file_hash)
    
    def _check_duplicate_upload(self, file_content: bytes, filename: str) -> None:
        """
        檢查是否為重複上傳
        
        Args:
            file_content: 檔案內容
            filename: 檔案名稱
            
        Raises:
            DuplicateUploadError: 檔案重複上傳
        """
        # 清理過期記錄
        self._cleanup_old_upload_records()
        
        # 計算檔案雜湊
        file_hash = self._calculate_file_hash(file_content)
        current_time = time.time()
        
        # 檢查是否為重複檔案
        if file_hash in self._upload_hashes:
            last_upload_time = self._recent_uploads.get(file_hash, 0)
            time_diff = current_time - last_upload_time
            
            if time_diff < self._duplicate_check_window:
                error_msg = f"檔案重複上傳: {filename} (在 {time_diff:.1f} 秒前已上傳相同檔案)"
                logger.warning(error_msg)
                raise DuplicateUploadError(error_msg)
        
        # 記錄新上傳
        self._recent_uploads[file_hash] = current_time
        self._upload_hashes.add(file_hash)
    
    async def process_upload(self, upload_file: UploadFile) -> Dict[str, Any]:
        """
        處理檔案上傳（包含重複防護和狀態鎖定）
        
        Args:
            upload_file: FastAPI 上傳檔案物件
            
        Returns:
            Dict[str, Any]: 上傳結果資訊
            
        Raises:
            UploadError: 上傳過程中的各種錯誤
        """
        # 使用異步鎖定防止並發上傳
        async with self._upload_lock:
            try:
                # 設置上傳狀態
                self.is_uploading = True
                logger.info(f"開始處理檔案上傳: {upload_file.filename}")
                
                # 檢查檔案名稱
                if not upload_file.filename:
                    raise UploadError("檔案名稱不能為空")
                
                # 讀取檔案內容
                file_content = await upload_file.read()
                file_size = len(file_content)
                
                # 檢查重複上傳
                try:
                    self._check_duplicate_upload(file_content, upload_file.filename)
                except DuplicateUploadError as e:
                    return {
                        'success': False,
                        'message': str(e),
                        'error_type': 'duplicate_upload',
                        'original_filename': upload_file.filename
                    }
                
                # 驗證檔案大小
                self.validate_file_size(file_size)
                
                # 驗證檔案格式
                self.validate_file_format(upload_file.filename)
                
                # 檢查磁碟空間
                required_space_mb = (file_size / (1024 * 1024)) * 3  # 預留3倍空間用於解壓縮
                if not self.temp_manager.check_disk_space(int(required_space_mb)):
                    raise UploadError("磁碟空間不足")
                
                # 創建唯一的檔案路徑
                upload_path = self.temp_manager.create_unique_upload_path(upload_file.filename)
                
                # 寫入檔案
                with open(upload_path, 'wb') as f:
                    f.write(file_content)
                
                logger.info(f"檔案上傳成功: {upload_file.filename} -> {upload_path}")
                
                # 返回上傳結果
                return {
                    'success': True,
                    'message': '檔案上傳成功',
                    'original_filename': upload_file.filename,
                    'upload_path': str(upload_path),
                    'file_size': file_size,
                    'file_size_mb': round(file_size / (1024 * 1024), 2)
                }
                
            except (FileSizeError, FileFormatError) as e:
                # 已知的驗證錯誤
                logger.warning(f"檔案上傳驗證失敗: {e}")
                return {
                    'success': False,
                    'message': str(e),
                    'error_type': 'validation_error',
                    'original_filename': upload_file.filename if upload_file.filename else '未知檔案'
                }
                
            except Exception as e:
                # 未預期的錯誤
                error_msg = f"檔案上傳處理失敗: {e}"
                logger.error(error_msg)
                return {
                    'success': False,
                    'message': error_msg,
                    'error_type': 'internal_error',
                    'original_filename': upload_file.filename if upload_file.filename else '未知檔案'
                }
            
            finally:
                # 重置上傳狀態
                self.is_uploading = False
                logger.debug("檔案上傳處理完成，重置狀態")
    
    def get_upload_state(self) -> Dict[str, Any]:
        """
        取得上傳狀態資訊
        
        Returns:
            Dict[str, Any]: 上傳狀態資訊
        """
        return {
            'is_uploading': self.is_uploading,
            'has_duplicate_prevention': self.has_duplicate_prevention,
            'recent_uploads_count': len(self._recent_uploads),
            'duplicate_check_window_seconds': self._duplicate_check_window
        }
    
    def get_upload_info(self) -> Dict[str, Any]:
        """
        取得檔案上傳相關資訊
        
        Returns:
            Dict[str, Any]: 上傳配置資訊
        """
        info = {
            'max_upload_size_mb': self.config.max_upload_size_mb,
            'max_upload_size_bytes': self.config.max_upload_size_bytes,
            'supported_formats': self.config.supported_archive_formats,
            'upload_temp_dir': str(self.config.upload_temp_dir),
            'extract_temp_dir': str(self.config.extract_temp_dir),
            'auto_cleanup_hours': self.config.auto_cleanup_hours
        }
        
        # 添加狀態資訊
        info.update(self.get_upload_state())
        return info
    
    def cleanup_upload_file(self, upload_path: str) -> bool:
        """
        清理上傳的檔案
        
        Args:
            upload_path: 上傳檔案路徑
            
        Returns:
            bool: 清理是否成功
        """
        try:
            path = Path(upload_path)
            return self.temp_manager.remove_file_or_dir(path)
        except Exception as e:
            logger.error(f"清理上傳檔案失敗 {upload_path}: {e}")
            return False
    
    def get_duplicate_info(self, file_content: bytes, filename: str) -> Dict[str, Any]:
        """
        取得檔案重複上傳資訊
        
        Args:
            file_content: 檔案內容
            filename: 檔案名稱
            
        Returns:
            Dict[str, Any]: 重複上傳資訊
        """
        import time
        
        # 清理過期記錄
        self._cleanup_old_upload_records()
        
        # 計算檔案雜湊
        file_hash = self._calculate_file_hash(file_content)
        current_time = time.time()
        
        # 檢查是否為重複檔案
        if file_hash in self._upload_hashes:
            last_upload_time = self._recent_uploads.get(file_hash, 0)
            time_diff = current_time - last_upload_time
            remaining_time = max(0, self._duplicate_check_window - time_diff)
            
            return {
                'is_duplicate': True,
                'last_upload_time': last_upload_time,
                'time_since_upload': time_diff,
                'remaining_wait_time': remaining_time,
                'can_upload': time_diff >= self._duplicate_check_window,
                'filename': filename,
                'file_hash': file_hash[:16]  # 只顯示前16字元用於識別
            }
        
        return {
            'is_duplicate': False,
            'can_upload': True,
            'filename': filename,
            'file_hash': file_hash[:16]
        }
    
    def clear_duplicate_record(self, file_content: bytes) -> bool:
        """
        清除特定檔案的重複上傳記錄
        
        Args:
            file_content: 檔案內容
            
        Returns:
            bool: 清除是否成功
        """
        try:
            file_hash = self._calculate_file_hash(file_content)
            
            # 移除記錄
            if file_hash in self._upload_hashes:
                self._upload_hashes.remove(file_hash)
                
            if file_hash in self._recent_uploads:
                del self._recent_uploads[file_hash]
                
            logger.info(f"已清除檔案重複上傳記錄: {file_hash[:16]}")
            return True
            
        except Exception as e:
            logger.error(f"清除重複上傳記錄失敗: {e}")
            return False
    
    def clear_all_duplicate_records(self) -> int:
        """
        清除所有重複上傳記錄
        
        Returns:
            int: 清除的記錄數量
        """
        try:
            count = len(self._upload_hashes)
            self._upload_hashes.clear()
            self._recent_uploads.clear()
            
            logger.info(f"已清除所有重複上傳記錄，共 {count} 個")
            return count
            
        except Exception as e:
            logger.error(f"清除所有重複上傳記錄失敗: {e}")
            return 0