"""
XAHT 廠商解析器實作
基於 VBA 雙重解析邏輯，支援中文字元

VBA 邏輯參考：
- 識別條件：InStr(LCase(body), "tianshui") Or InStr(LCase(body), "西安") Or InStr(LCase(subject), "西安")
- 雙重解析：XAHTInfoFromStrings (wa/GYC 模式) 和 XAHTInfoFromStrings2 (下劃線分隔)
- 中文字元支援：正確處理 UTF-8 編碼的中文關鍵字
"""

import re
from typing import List, Optional, Dict, Any
from dataclasses import dataclass

from src.infrastructure.parsers.base_parser import VendorParser, ParsingError, ParsingContext
from src.data_models.email_models import EmailData, EmailParsingResult


class XAHTParser(VendorParser):
    """
    XAHT 廠商郵件解析器
    
    識別條件：
    - 郵件正文包含 "tianshui"（天水）
    - 郵件正文包含 "西安"
    - 郵件主旨包含 "西安"
    
    雙重解析機制：
    1. 方法 1：搜尋包含 "wa" 或 "GYC" 的詞
    2. 方法 2：下劃線分隔格式解析
    """
    
    def __init__(self):
        """初始化 XAHT 解析器"""
        super().__init__()
        self._vendor_code = "XAHT"
        self._vendor_name = "XAHT"
        self._identification_patterns = [
            "tianshui",  # 天水拼音
            "西安"       # 中文城市名
        ]
        self.set_confidence_threshold(0.8)
        
        # XAHT 特有的 MO 編號模式
        self.mo_patterns = [
            r'\b\w*wa\w*\b',      # 包含 'wa' 的詞
            r'\b\w*GYC\w*\b'      # 包含 'GYC' 的詞
        ]

    @property
    def vendor_code(self) -> str:
        """廠商代碼"""
        return self._vendor_code
    
    @property  
    def vendor_name(self) -> str:
        """廠商名稱"""
        return self._vendor_name
    
    @property
    def supported_patterns(self) -> List[str]:
        """支援的模式列表"""
        return self._identification_patterns
    
    def identify_vendor(self, email_data: EmailData) -> 'VendorIdentificationResult':
        """識別廠商 - 實作抽象方法"""
        from src.data_models.email_models import VendorIdentificationResult
        
        subject = email_data.subject or ""
        body = email_data.body or ""
        sender_lower = email_data.sender.lower()
        matched_patterns = []
        confidence_score = 0.0
        
        # 檢查主要識別模式
        # VBA: InStr(1, LCase(body), "tianshui", vbTextCompare) > 0
        if "tianshui" in body.lower():
            matched_patterns.append("tianshui")
            confidence_score += 0.5  # 提高基礎分數
            
        # 也檢查主旨中的 tianshui（增強識別能力）
        if "tianshui" in subject.lower():
            if "tianshui" not in matched_patterns:
                matched_patterns.append("tianshui")
            confidence_score += 0.5  # 提高分數以達到閾值
            
        # VBA: InStr(1, LCase(body), "西安", vbTextCompare) > 0
        if "西安" in body:
            matched_patterns.append("西安")
            confidence_score += 0.5  # 提高基礎分數
            
        # VBA: InStr(1, LCase(subject), "西安", vbTextCompare) > 0
        if "西安" in subject:
            matched_patterns.append("西安")
            confidence_score += 0.5  # 提高基礎分數
        
        # 額外的信心分數計算
        if "xaht" in sender_lower:
            confidence_score += 0.3  # XAHT 官方寄件者
            
        # 檢查主旨是否有下劃線分隔格式（方法 2 的格式）
        if "_" in subject and len(subject.split("_")) >= 5:
            confidence_score += 0.2  # 符合下劃線格式
            
        # 檢查是否有 wa 或 GYC 模式（方法 1 的格式）
        for pattern in self.mo_patterns:
            if re.search(pattern, subject, re.IGNORECASE):
                confidence_score += 0.2
                break
                
        # 確保信心分數不超過 1.0
        confidence_score = min(confidence_score, 1.0)
        
        is_identified = len(matched_patterns) > 0 and confidence_score >= self.get_confidence_threshold()
        
        return VendorIdentificationResult(
            vendor_code=self.vendor_code,
            vendor_name=self.vendor_name,
            confidence_score=confidence_score,
            matching_patterns=matched_patterns,
            is_identified=is_identified,
            identification_method="chinese_keyword_matching"
        )
    
    def parse_email(self, context: ParsingContext) -> EmailParsingResult:
        """解析郵件 - 實作抽象方法"""
        email_data = context.email_data
        
        if not email_data.subject:
            # 空主旨時嘗試從識別結果判斷
            identification = self.identify_vendor(email_data)
            if not identification.is_identified:
                raise ParsingError("Empty subject and cannot identify vendor", vendor_code=self.vendor_code)
        
        try:
            # 使用雙重解析機制
            dual_result = self.parse_dual_method(email_data.subject or "")
            
            # 清理和驗證 MO 編號格式
            mo_number = dual_result["mo_number"]
            if mo_number and mo_number != "":
                # XAHT MO 編號可能有各種格式，嘗試提取標準格式
                mo_match = re.search(r'([A-Z]\d{6})', mo_number)
                if mo_match:
                    mo_number = mo_match.group(1)
                else:
                    mo_number = None  # 格式不匹配時設為 None
            else:
                mo_number = None
            
            # 建立解析結果
            result = EmailParsingResult(
                vendor_code=self.vendor_code,
                mo_number=mo_number,
                lot_number=dual_result["lot_number"] if dual_result["lot_number"] != "?" else None,
                is_success=True,
                error_message=None,
                extracted_data={
                    'vendor_name': self.vendor_name,
                    'product': dual_result["product"],
                    'lot_number': dual_result["lot_number"],
                    'mo_number': dual_result["mo_number"],  # 原始 MO 編號
                    'parsing_method': dual_result["final_method"],
                    'subject': email_data.subject,
                    'sender': email_data.sender,
                    'received_time': email_data.received_time.isoformat(),
                    'parser_version': '1.0',
                    'patterns_matched': self.identify_vendor(email_data).matching_patterns,
                    'extraction_method': 'dual_parsing_xaht'
                }
            )
            
            return result
            
        except Exception as e:
            # 發生錯誤時返回失敗結果
            return EmailParsingResult(
                vendor_code=self.vendor_code,
                mo_number=None,
                lot_number=None,
                is_success=False,
                error_message=f"XAHT parsing failed: {str(e)}",
                extracted_data={
                    'vendor_name': self.vendor_name,
                    'subject': email_data.subject,
                    'sender': email_data.sender,
                    'received_time': email_data.received_time.isoformat(),
                    'parser_version': '1.0',
                    'error_type': type(e).__name__
                }
            )

    def parse_method_1(self, subject: str) -> Dict[str, Any]:
        """
        解析方法 1：XAHTInfoFromStrings 邏輯
        基於 VBA 搜尋包含 "wa" 或 "GYC" 的詞
        
        VBA 邏輯：
        - 按空格分割主旨
        - 找到長度 > 4 且包含 "wa" 或 "GYC" 的詞作為 MO
        - 取該詞的前 11 個字符作為 MO 編號
        - 取 MO 前一個詞作為產品型號
        """
        if not subject:
            return {
                "mo_number": "",
                "product": "",
                "method": "no_pattern"
            }
            
        words = subject.split()
        mo_number = ""
        product = ""
        method = "no_pattern"
        
        for i, word in enumerate(words):
            word_lower = word.lower()
            
            # 檢查是否包含 "wa" 或 "gyc" 且長度 > 4
            if len(word) > 4 and ("wa" in word_lower or "gyc" in word_lower):
                # 取前 11 個字符作為 MO 編號
                mo_number = word[:11] if len(word) >= 11 else word
                
                # 取前一個詞作為產品型號
                if i > 0:
                    product = words[i - 1]
                
                # 確定使用的模式
                if "wa" in word_lower:
                    method = "wa_pattern"
                elif "gyc" in word_lower:
                    method = "gyc_pattern"
                
                break
        
        return {
            "mo_number": mo_number,
            "product": product,
            "method": method
        }

    def parse_method_2(self, subject: str) -> Dict[str, Any]:
        """
        解析方法 2：XAHTInfoFromStrings2 邏輯
        基於 VBA 下劃線分隔格式
        
        VBA 邏輯：
        - 移除 "FW: " 前綴
        - 用下劃線 "_" 分割字串
        - 固定提取：parts[2]=product, parts[3]=lot_number, parts[4]=mo_number
        """
        if not subject:
            return {
                "product": "?",
                "lot_number": "?",
                "mo_number": "?",
                "method": "underscore_split"
            }
        
        # 移除 "FW: " 前綴
        cleaned_subject = subject
        if subject.startswith("FW: "):
            cleaned_subject = subject[4:]
        
        # 用下劃線分割
        parts = cleaned_subject.split("_")
        
        # 安全提取各部分資料（VBA 索引從 0 開始）
        product = parts[2] if len(parts) > 2 else "?"
        lot_number = parts[3] if len(parts) > 3 else "?"
        mo_number = parts[4] if len(parts) > 4 else "?"
        
        return {
            "product": product,
            "lot_number": lot_number,
            "mo_number": mo_number,
            "method": "underscore_split"
        }

    def parse_dual_method(self, subject: str) -> Dict[str, Any]:
        """
        雙重解析機制
        基於 VBA 邏輯：先用方法 1，如果 MO 為空則用方法 2
        
        VBA 邏輯：
        XAHTInfoFromStrings subject, product, moString, lotString, yieldString
        If Len(moString) = 0 Then
            XAHTInfoFromStrings2 subject, product, moString, lotString, yieldString
        End If
        """
        # 先嘗試方法 1
        result1 = self.parse_method_1(subject)
        
        if result1["mo_number"] and result1["mo_number"] != "":
            # 方法 1 成功，返回結果
            return {
                "mo_number": result1["mo_number"],
                "product": result1["product"],
                "lot_number": "",  # 方法 1 不提取 LOT
                "final_method": "method_1",
                "method_1_result": result1
            }
        else:
            # 方法 1 失敗，使用方法 2
            result2 = self.parse_method_2(subject)
            return {
                "mo_number": result2["mo_number"],
                "product": result2["product"],
                "lot_number": result2["lot_number"],
                "final_method": "method_2",
                "method_1_result": result1,
                "method_2_result": result2
            }

    def validate_chinese_encoding(self, text: str) -> bool:
        """
        驗證中文字符編碼是否正確
        處理可能的編碼問題
        """
        if not text:
            return True
            
        try:
            # 嘗試重新編碼以檢測問題
            text.encode('utf-8').decode('utf-8')
            return True
        except UnicodeError:
            return False

    def normalize_chinese_text(self, text: str) -> str:
        """
        標準化中文文本，處理可能的編碼問題
        """
        if not text:
            return text
            
        try:
            # 處理可能的編碼問題
            # 替換常見的編碼錯誤字符
            replacements = {
                "?": "",  # 移除問號（可能是編碼錯誤）
                "??": "",
                "???": ""
            }
            
            normalized = text
            for old, new in replacements.items():
                normalized = normalized.replace(old, new)
                
            return normalized.strip()
        except Exception:
            return text

    def get_parser_info(self) -> dict:
        """獲取解析器資訊"""
        return {
            'vendor': self.vendor_name,
            'version': '1.0',
            'identification_patterns': self._identification_patterns,
            'supported_formats': [
                'Method 1: wa/GYC pattern matching',
                'Method 2: underscore-separated format'
            ],
            'confidence_threshold': self.get_confidence_threshold(),
            'chinese_support': True,
            'encoding_support': ['UTF-8', 'GBK'],
            'based_on': 'VBA XAHTInfoFromStrings and XAHTInfoFromStrings2',
            'extraction_capabilities': [
                'Product name via wa/GYC pattern (Method 1)',
                'Product name from position [2] (Method 2)',
                'LOT number from position [3] (Method 2)',
                'MO number from wa/GYC match or position [4]',
                'Dual parsing mechanism with fallback',
                'Chinese keyword identification'
            ],
            'special_features': [
                'Dual parsing mechanism',
                'Chinese character support (tianshui, 西安)',
                'Encoding error tolerance',
                'FW: prefix handling'
            ]
        }