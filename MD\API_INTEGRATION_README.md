# API整合服務使用說明

## 概述

遵循CLAUDE.md功能替換原則，建立獨立的API整合模組，同時啟動主API系統和網路共享瀏覽器API，兩個服務並存且不衝突。

## 服務配置

| 服務名稱 | 端口 | 主要功能 | 文檔/UI入口 |
|---------|------|----------|-------------|
| 主API系統 | 8010 | EQC處理、檔案上傳、分析功能 | http://localhost:8010/docs |
| 網路瀏覽器API | 8009 | 網路共享資料夾瀏覽、下載 | http://localhost:8009/network/ui |

## 快速啟動

### 方法1: 使用啟動腳本（推薦）
```bash
./start_integrated_apis.sh
```

### 方法2: 手動啟動
```bash
# 啟動虛擬環境
source venv/bin/activate

# 確認環境正確
which python

# 啟動整合服務
python api_integration.py
```

### 方法3: 測試服務狀態
```bash
source venv/bin/activate
python test_integration.py
```

## 服務端點一覽

### 主API系統 (端口 8010)
- 📊 API文檔: http://localhost:8010/docs
- 🖥️ 主UI介面: http://localhost:8010/ui
- 🔍 健康檢查: http://localhost:8010/api/health
- 核心功能：
  - EQC檔案處理和分析
  - 檔案上傳和解壓縮
  - 資料統計和報告生成

### 網路瀏覽器API (端口 8009)
- 🌍 瀏覽器UI: http://localhost:8009/network/ui
- 🔍 健康檢查: http://localhost:8009/network/health
- 核心功能：
  - 網路共享資料夾瀏覽
  - 檔案下載和資訊查看
  - 路徑驗證和存取控制

## 功能特點

### 🔧 獨立模組設計
- ✅ 不修改現有主API檔案（ft_eqc_api.py已1688行，超過500行限制）
- ✅ 建立獨立整合器（api_integration.py，202行）
- ✅ 遵循功能替換原則，新舊功能並存

### 🚀 並存運行
- ✅ 兩個API服務同時運行
- ✅ 端口隔離避免衝突
- ✅ 獨立的功能領域

### 📋 管理功能
- ✅ 自動端口檢查和清理
- ✅ 優雅的服務啟動和停止
- ✅ 進程監控和狀態檢查
- ✅ 統一的日誌管理

## 停止服務

按 `Ctrl+C` 停止所有服務，整合器會自動清理並優雅關閉所有子進程。

## 故障排除

### 端口佔用問題
如果遇到端口佔用，整合器會自動嘗試清理：
```bash
# 手動檢查端口
lsof -ti:8010
lsof -ti:8009

# 手動清理（如需要）
kill -9 $(lsof -ti:8010)
kill -9 $(lsof -ti:8009)
```

### 虛擬環境問題
```bash
# 確認虛擬環境啟動
source venv/bin/activate
which python

# 重新安裝依賴（如需要）
pip install -r requirements.txt
```

### 模組導入問題
```bash
# 檢查模組是否可用
python -c "from src.presentation.api.ft_eqc_api import app; print('主API模組正常')"
python -c "from src.presentation.api.network_browser_api import app; print('網路API模組正常')"
```

## 架構原理

```
API整合架構 (功能替換原則)
├── api_integration.py          # 獨立整合器 (202行)
│   ├── APIIntegrator Class     # 服務管理器
│   ├── 端口檢查和清理          # 避免衝突
│   ├── 進程管理               # uvicorn子進程
│   └── 信號處理               # 優雅關閉
├── 主API服務 (8010)           # 現有功能不變
│   └── ft_eqc_api.py          # 1688行，不修改
└── 網路瀏覽器API (8009)       # 獨立功能
    └── network_browser_api.py  # 獨立運行
```

## 遵循CLAUDE.md規則

- ✅ **功能替換原則**: 建立獨立模組，不重複現有功能
- ✅ **檔案行數限制**: 所有新檔案≤500行
- ✅ **反假測試原則**: 提供實際測試腳本驗證功能
- ✅ **繁體中文**: 所有介面和日誌使用繁體中文
- ✅ **零容忍禁令**: 不修改現有程式碼，不建立功能重複

## 整合結果

🎉 **整合成功**：
- 主API系統正常運行 (8010端口)
- 網路瀏覽器API正常運行 (8009端口)  
- 兩個服務並存且不衝突
- 獨立管理，不影響現有功能