# 📊 Excel 處理系統完整說明書

本說明書整合了所有 Excel 相關功能，包含 CSV 轉換、CTA 處理、和統一的處理流程。

## 🎯 系統概述

### 核心功能
- **統一 CSV 處理** - 支援 CTA 格式和一般 CSV 格式
- **自動格式檢測** - 無需手動判斷檔案類型
- **完整 Excel 輸出** - 包含 Summary 工作表和所有功能
- **高性能處理** - 向量化優化，大幅提升處理速度

### 系統架構
```
📁 Excel 處理系統架構
├── 🎯 統一入口點
│   └── cta_processor.py (根目錄CLI)
├── 🔧 核心處理模組  
│   ├── csv_to_excel_converter.py (一般CSV處理)
│   └── cta_integrated_processor.py (整合處理器)
├── 🛠️ 支援模組
│   ├── summary_generator.py (Summary生成)
│   ├── site_column_finder.py (Site欄位檢測)
│   └── advanced_performance_manager.py (性能管理)
└── 📋 CTA 專用模組
    ├── cta_format_detector.py (格式檢測)
    ├── cta_converter.py (CTA轉換)
    └── worksheet_manager.py (工作表管理)
```

## 🚀 使用方式

### 統一入口（推薦）
**檔案位置**: `cta_integrated_processor.py:integrated_process_file()`
```bash
# 1. 啟動虛擬環境 (強制要求)
source venv/bin/activate

# 2. 自動檢測處理（推薦）
python cta_processor.py "doc/your_file.csv"
# 輸出: logs/processed_your_file.xlsx

# 3. 傳統CTA模式
python cta_processor.py "doc/cta_file.csv" --traditional
# 輸出: logs/cta_file.xlsx (無 Summary 工作表)
```

**輸出檔案命名規則**:
- **自動檢測模式**: `processed_{原檔名}.xlsx`
- **傳統模式**: `{原檔名}.xlsx`  
- **輸出位置**: 統一放在 `logs/` 目錄

### 直接執行主程式
```bash
# 一般CSV處理
cd src/infrastructure/adapters/excel/
python fixed_test.py "your_file.csv"

# CTA整合處理
cd src/infrastructure/adapters/excel/cta/
python cta_integrated_processor.py "your_file.csv"
```

## 🔀 自動檢測流程

### 流程圖
```
CSV檔案輸入 → 格式檢測 → 分流處理 → 統一輸出
     ↓            ↓          ↓         ↓
   任何格式    [Data]標記   雙重路徑   Summary
              自動檢測     處理邏輯   Excel檔案
```

### 檢測邏輯
**檔案位置**: `cta_integrated_processor.py:500-512`
```python
def is_cta_csv(file_path: str) -> bool:
    """檢測是否為 CTA CSV 檔案（檢測 [Data] 標記）"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                if '[Data]' in line:
                    return True  # CTA 格式
                if line_num > 5000:  # 限制搜尋範圍
                    break
            return False  # 一般 CSV 格式
    except Exception as e:
        print(f"⚠️ 檔案檢測失敗: {e}")
        return False
```

### 雙重處理流程

**核心方法**: `cta_integrated_processor.py:integrated_process_file()`  
**功能**: 統一入口，自動檢測分流，統一輸出

#### 🔵 CTA 格式處理流程
```
CTA CSV → CTA專門處理 → Data11 DataFrame → 
暫存CSV → csv_to_excel_converter → Summary Excel
```

**詳細步驟**：
1. **CTA 解析** - 提取 [Data] 區段內容
2. **動態欄位檢測** - 計算有效欄位數量
3. **Data11 生成** - 建立標準 DataFrame 結構
4. **暫存轉換** - 儲存為臨時 CSV 檔案  
5. **標準流程** - 接入 csv_to_excel_converter
6. **清理** - 自動刪除暫存檔案

#### 🟢 一般 CSV 處理流程
```
一般 CSV → 直接 csv_to_excel_converter → Summary Excel
```

**詳細步驟**：
1. **直接處理** - 跳過 CTA 專門處理
2. **標準流程** - 完整的 8 步驟轉換流程
3. **Summary 生成** - 完整的統計分析

## 📋 核心 8 步驟處理流程

**程式位置**: `csv_to_excel_converter.py:convert_csv_to_excel()`  
**性能管理**: 使用 `performance_manager.performance_context` 進行全程監控  
**日誌記錄**: 所有步驟耗時和性能分析自動記錄到 `logs/datalog.txt`

### 步驟 1: CSV 讀取
**檔案位置**: `csv_to_excel_converter.py:646` (方法: `_safe_read_csv`)
```python
df = self._safe_read_csv(csv_file_path)
```

**處理內容**:
- **安全讀取** CSV 檔案，處理編碼問題
- **統一欄數**，補齊缺少的欄位
- **動態更新** 最大設備數和測試項目數
- **錯誤處理** 和空檔案檢查

**輸出**: pandas DataFrame

---

### 步驟 2: 結構補全
**檔案位置**: `csv_to_excel_converter.py:658` (方法: `complete_csv_structure`)
```python
df = self.complete_csv_structure(df)
```

**處理內容**:
- **C7 (第7行)**: 測試項目分組 = `"0.00.01"`
- **C8 (第8行)**: 測試項目名稱 = `"Test_Time"`
- **C10 (第10行)**: Max 限值 = `"none"`
- **C11 (第11行)**: Min 限值 = `"none"`

**目的**: 確保 CSV 具備必要的結構欄位

---

### 步驟 3: BIN 分配
**檔案位置**: `csv_to_excel_converter.py:663` (方法: `assign_bin_numbers_df`)
```python
df = self.assign_bin_numbers_df(df)
```

**處理內容**:
- **位置**: 第6行 (index 5)
- **分配規則**: 從 C 欄開始，分配 BIN 5, 6, 7, 8...
- **範例**: C6=BIN5, D6=BIN6, E6=BIN7, F6=BIN8

**目的**: 為每個測試項目分配唯一的 BIN 號碼

---

### 步驟 4: 🛡️ BIN1 保護機制
**檔案位置**: `csv_to_excel_converter.py:668` (方法: `apply_bin1_protection_df`)
```python
df, protection_positions = self.apply_bin1_protection_df(df)
```

#### 🔍 保護流程詳解:

**步驟 4.1: 識別 BIN1 設備**
```python
# 掃描第2欄 (Bin#) 找出 BIN=1 的設備
bin_column_data = df.iloc[device_data_start_row:, 1].astype(str).str.strip()
bin1_mask = bin_column_data == "1"
bin1_device_indices = bin1_mask[bin1_mask].index.tolist()
```

**步驟 4.2: 提取關鍵資料**
```python
# 批量提取限值和設備測試資料
max_limits = df.iloc[max_limit_row, test_item_start_col:].values      # 第10行 Max 限值
min_limits = df.iloc[min_limit_row, test_item_start_col:].values      # 第11行 Min 限值
bin1_device_data = df.iloc[bin1_device_indices, test_item_start_col:].values  # BIN1 設備測試資料
```

**步驟 4.3: 核心保護邏輯**
```python
for test_col_offset in valid_test_indices:
    device_values = bin1_device_data[:, test_col_offset]
    
    # 檢查失敗條件: 超出上下限
    would_fail_mask = self._vectorized_failure_check(device_values, max_limit, min_limit, ...)
    
    # 檢查零值條件: 測試數值為 0
    is_zero_mask = self._vectorized_zero_check(device_values)
    
    # ⭐ 關鍵邏輯: 任一 BIN1 設備會失敗且數值為 0 才放寬限值
    should_relax = np.any(would_fail_mask & is_zero_mask)
    
    if should_relax:
        # 放寬限值: 設為 "none"
        df.iloc[max_limit_row, actual_col_idx] = "none"
        df.iloc[min_limit_row, actual_col_idx] = "none"
        
        # 記錄保護位置供紅色標記
        protection_positions.extend([
            (max_limit_row, actual_col_idx),
            (min_limit_row, actual_col_idx)
        ])
```

**保護邏輯說明**:
- **目的**: 防止良品 (BIN1) 因測試數值為 0 被誤判為不良品
- **條件**: BIN1 設備會失敗 **且** 測試數值為 0
- **動作**: 將該測試項目的 Max 和 Min 限值設為 "none"
- **記錄**: 保護資訊記錄到 `logs/datalog.txt`

**性能統計**: 處理速度 ~2,066,002 次檢查/秒，向量化優化提升數倍性能

---

### 步驟 5: 🎯 全設備 BIN 分類
**檔案位置**: `csv_to_excel_converter.py:673` (方法: `assign_bins_to_devices`)
```python
df, fail_positions = self.assign_bins_to_devices(df)
```

#### 🔍 分類流程詳解:

**步驟 5.1: 向量化預處理**
```python
# 提取所有需要的資料 (一次性存取)
device_data = df.iloc[device_start_row:, test_start_col:].values     # 設備測試資料 (第13行起)
max_limits = df.iloc[max_limit_row, test_start_col:].values          # 上限值 (第10行)
min_limits = df.iloc[min_limit_row, test_start_col:].values          # 下限值 (第11行)
bin_assignments = df.iloc[bin_assignment_row, test_start_col:].values # BIN 分配 (第6行)
```

**步驟 5.2: 向量化失敗檢測**
```python
# 建立有效限值遮罩
max_valid_mask = ~np.isnan(max_numeric) & (max_numeric != np.inf)
min_valid_mask = ~np.isnan(min_numeric) & (min_numeric != -np.inf)

# 向量化失敗檢測 (廣播操作)
max_failures = device_numeric[:, max_valid_mask] > max_numeric[max_valid_mask]
min_failures = device_numeric[:, min_valid_mask] < min_numeric[min_valid_mask]
all_failures = max_failures | min_failures
```

**步驟 5.3: 核心分類邏輯**
```python
# 預設所有設備為 BIN 1 (All Pass)
device_bins = np.ones(num_devices, dtype=int)
device_has_failures = np.any(all_failures, axis=1)

# ⭐ 關鍵邏輯: 找出每個設備的第一個失敗項目對應的 BIN
for device_idx in np.where(device_has_failures)[0]:
    first_fail_col = np.argmax(all_failures[device_idx])  # 第一個失敗測試項目
    device_bins[device_idx] = bin_numeric[first_fail_col]  # 分配對應的 BIN

# 批量更新 DataFrame
df.iloc[device_start_row:device_start_row + num_devices, 1] = device_bins
```

**分類邏輯說明**:
- **預設**: 所有設備初始為 BIN 1 (All Pass)
- **失敗判定**: 如果設備有測試項目超出上下限
- **BIN 分配**: 根據第一個失敗的測試項目對應的 BIN 號碼
- **紅色標記**: 記錄所有失敗測試項目和失敗設備的 BIN 欄位位置

**性能統計**: 處理速度 ~2,057,489 次比較/秒，資料規模 404 設備 × 1786 測試項目 = 721,544 次比較

---

### 步驟 6: Site 統計分析
**檔案位置**: `csv_to_excel_converter.py:677-686` (方法: `site_finder` + `strategy_b_processor`)
```python
self.site_column, site_log = self.site_finder.find_site_column_with_log(...)
strategy_b_result = self.strategy_b_processor.execute_site_statistics_only(...)
```

**處理內容**:
- **動態查找**: 自動識別 Site 欄位位置 (通常第5欄)
- **Site 統計**: 計算各 Site 的總設備數、Pass 數、Fail 數
- **BIN 分佈**: 統計各 Site 內各 BIN 的設備數量

---

### 步驟 7: 📊 Summary 工作表生成
**檔案位置**: `csv_to_excel_converter.py:689-696` (方法: `summary_generator.generate_summary`)
```python
summary_data = self.summary_generator.generate_summary(
    df=df,
    site_column=self.site_column,
    csv_file_path=csv_file_path
)
```

#### 🔍 Summary 生成詳解:

**步驟 7.1: BIN 統計計算**
```python
# 檔案: summary_generator.py:51-120
def calculate_bin_statistics(self, df: pd.DataFrame):
    # 掃描第13行起的設備資料
    device_rows = df.iloc[12:]  # 設備資料從第13行開始
    bin_column = 1  # BIN 欄位在第2欄
    
    # 統計各 BIN 數量
    for idx in device_rows.index:
        bin_value = df.iloc[idx, bin_column]
        bin_num = int(bin_value)
        bin_counts[bin_num] = bin_counts.get(bin_num, 0) + 1
```

**步驟 7.2: Site 統計計算**
```python
# 檔案: summary_generator.py:122-201
def calculate_site_statistics(self, df: pd.DataFrame, site_column: int):
    # 按 Site 分組統計
    for idx in device_rows.index:
        site_no = int(df.iloc[idx, site_column])
        bin_no = int(df.iloc[idx, bin_column])
        
        # 累計統計
        site_stats[site_no]['total'] += 1
        site_stats[site_no]['bins'][bin_no] += 1
        
        # 判斷 Pass/Fail (BIN 1-4 為 Pass)
        if bin_no <= self.max_pass_bin:
            site_stats[site_no]['pass'] += 1
        else:
            site_stats[site_no]['fail'] += 1
```

**步驟 7.3: BIN 定義對應**
```python
# 檔案: summary_generator.py:203-249
def get_bin_definitions(self, df: pd.DataFrame):
    definitions = {1: "All Pass"}  # BIN 1 固定定義
    
    # 從第8行獲取測試項目名稱
    test_item_row = df.iloc[7]
    # 從第6行獲取 BIN 分配
    bin_assignment_row = df.iloc[5]
    
    # 建立 BIN → 測試項目名稱對應
    for col_idx, bin_num in enumerate(bin_assignment_row):
        test_name = test_item_row.iloc[col_idx]
        definitions[bin_num] = str(test_name).strip()
```

**步驟 7.4: Summary 結構生成**
```python
# 檔案: summary_generator.py:290-389
def generate_summary(self, df, site_column, csv_file_path):
    # 1. 基本統計行 (第1-4行)
    basic_stats = [
        ['Total', total_devices],     # 總設備數
        ['Pass', pass_devices],       # Pass 設備數  
        ['Fail', fail_devices],       # Fail 設備數
        ['Yield', yield_rate]         # 良率百分比
    ]
    
    # 2. Site 總計行 (第5行)
    site_total_row = ['', '', '', '', ''] + ['Total', count] * sites
    
    # 3. 動態標頭 (第6行)
    headers = ['Bin', 'Count', '%', 'Definition', 'Note'] + site_headers
    
    # 4. BIN 資料行 (第7行起)
    # ⭐ 重要: BIN 1 永遠排第一行
    if 1 not in bin_distribution:
        # 即使沒有 BIN 1 設備，也要顯示 Count=0 的 BIN 1 行
        bin_rows.append([1, 0, 0.0, "All Pass", ''] + site_data)
    
    for bin_no in sorted_bins:
        row = [bin_no, count, percentage, definition, note] + site_data
        bin_rows.append(row)
```

**Summary 特色**:
- **BIN 1 優先**: 永遠排在第一行，符合業務邏輯
- **完整 BIN 列表**: 包含 Count=0 的 BIN
- **動態 Site 欄位**: 支援任意數量的 Site
- **正確百分比**: 小數格式 (0.6 = 60%)

---

### 步驟 8: Excel 檔案輸出
**檔案位置**: `csv_to_excel_converter.py:699-702` (方法: `_create_excel_with_summary`)
```python
all_red_positions = protection_positions + fail_positions
self._create_excel_with_summary(df, all_red_positions, output_file_path, summary_data)
```

#### 🔍 Excel 輸出詳解:

**步驟 8.1: 主資料工作表**
```python
# 檔案: csv_to_excel_converter.py:841-917
def _write_main_data(self, worksheet, df, all_red_positions, red_font_format):
    # 向量化智慧轉換
    df_final = df.copy()
    df_final.iloc[12:] = df_final.iloc[12:].apply(vectorized_smart_convert, axis=0)
    
    # 批量寫入所有數據
    for row_idx in range(len(df_final)):
        row_data = df_final.iloc[row_idx].tolist()
        worksheet.write_row(row_idx, 0, row_data)
    
    # 🔴 分離處理紅色格式化
    for row_idx, col_idx in red_positions_set:
        value = df_final.iloc[row_idx, col_idx]
        worksheet.write(row_idx, col_idx, value, red_font_format)
```

**步驟 8.2: Summary 工作表**
```python
# 檔案: csv_to_excel_converter.py:918-1041
def _write_summary_data(self, worksheet, summary_data, ...):
    # 批量填入基本統計行 (第1-4行) - 正常格式
    for row_idx, row_data in enumerate(summary_data['basic_stats']):
        for col_idx, value in enumerate(row_data):
            if row_idx == 3 and col_idx == 1:  # Yield 百分比
                worksheet.write(row_idx, col_idx, value, percentage_format)
            else:
                worksheet.write(row_idx, col_idx, value, normal_format)
    
    # 批量填入 BIN 資料行 - 全部正常格式
    for row_idx, bin_row in enumerate(summary_data['bin_rows'], 6):
        for col_idx, value in enumerate(bin_row):
            if col_idx == 2 or (col_idx > 4 and (col_idx - 4) % 2 == 0):  # % 欄位
                worksheet.write(row_idx, col_idx, value, percentage_format)
            else:
                worksheet.write(row_idx, col_idx, value, normal_format)
```

**輸出特色**:
- **主資料工作表**: 失敗項目和 BIN1 保護位置標紅色
- **Summary 工作表**: ⭐ 整頁正常格式 (不變色) - 符合用戶要求
- **百分比格式**: 0% (不是 0.0%)
- **高性能**: 使用 xlsxwriter + 向量化批量處理

#### 📈 實時性能分析報告
程式執行完成後會自動生成詳細的性能分析報告：

```
⚡ 詳細性能分析報告
================================================================================
📊 各步驟處理時間 (按耗時排序):
  1. 步驟8_Excel輸出    :    4.746 秒 ( 86.2%)
  2. 步驟5_設備BIN分類  :    0.351 秒 (  6.4%)
  3. 步驟4_BIN1保護     :    0.213 秒 (  3.9%)
  4. 步驟1_CSV讀取      :    0.099 秒 (  1.8%)
  5. 步驟7_Summary生成  :    0.056 秒 (  1.0%)

🎯 總處理時間: 5.465 秒

🔍 性能瓶頸分析:
  前 3 大耗時步驟佔總時間: 96.5%
    🎯 瓶頸 1: 步驟8_Excel輸出 (86.2%)
    🎯 瓶頸 2: 步驟5_設備BIN分類 (6.4%)
    🎯 瓶頸 3: 步驟4_BIN1保護 (3.9%)

💡 進一步優化建議:
  🔧 Excel 輸出是主要瓶頸，建議優化 xlsxwriter 操作
```

## 🛡️ BIN1 保護機制詳解

### 保護原理
當 BIN1（PASS）設備在某測試項目：
1. **會失敗** (超出 Max/Min 限值)
2. **測試值為 0** (通常表示測試未執行)

則**放寬該測試項目的限值**為 "none"，確保 BIN1 設備保持 PASS 狀態。

### 保護記錄
```
🔴 BIN1 保護記錄
================================================================================
發現 BIN1 設備數量: 315
需要保護的測試項目數量: 7

| 欄位    | 測試項目編號 | 測試項目名稱 | 原始Max限值 | 原始Min限值 | 結果         |
|---------|-------------|-------------|------------|------------|--------------|
| Col576  | 1.08.19     | SDA_ACK     | 0.64725    | 0.10275    | Max&Min→none |
| Col577  | 1.08.20     | FAULT_VOL   | 0.79699999 | 0.20299999 | Max&Min→none |
```

## 📊 CTA 格式處理詳解

### CTA 格式特徵
- 包含 **[GENERAL]** 和 **[Data]** 區段
- 標準的測試項目結構
- Unit/Min/Max 限值定義

### CTA 處理流程
```python
def process_cta_to_dataframe(csv_file_path: str) -> pd.DataFrame:
    # 1. 解析 [Data] 區段
    # 2. 提取測試項目名稱、Unit/Min/Max
    # 3. 動態欄位檢測 (2 + CSV有效欄位數)
    # 4. 生成 Data11 工作表結構
    # 5. 智慧數字轉換
    # 6. 返回標準 DataFrame
```

### Data11 工作表結構
- **總欄位數**: 2（A、B欄）+ CSV有效欄位數
- **標頭區段**: 前11行包含檔案資訊和測試設定
- **資料區段**: 從第12行開始的設備測試資料
- **動態適應**: 根據原始 CSV 欄位數量自動調整

## 🎯 Summary 工作表結構

### 基本統計（前4行）
- **Total**: 總設備數
- **Pass**: PASS 設備數（BIN 1-4）  
- **Fail**: FAIL 設備數（BIN 5+）
- **Yield**: 良率（Pass/Total）

### Site 統計（第5行）
- 各 Site 的總設備數
- 動態生成 Site 欄位

### 標頭行（第6行）
```
Bin | Count | % | Definition | Note | Site 1 | % | Site 2 | % | ...
```

### BIN 資料行（第7行起）
- **BIN 1** 永遠在第一行（All Pass）
- **其他 BIN** 按 Count 降序，BIN 號碼升序排列
- **完整列表** 包含 Count=0 的 BIN
- **Site 統計** 每個 Site 在各 BIN 的分佈

## 📊 Site 顯示優化規格

### 🎯 雙模式支援

#### 📋 VBA 相容模式
- **顯示方式**: 從 Site 1 到最大 Site 號的完整範圍
- **適用情境**: 從 Site 1 開始的連續 Site (如 Site 1-4)
- **優勢**: 與原始 VBA 行為完全一致
- **範例**: `| Bin | Count | % | Definition | Note | Site 1 | % | Site 2 | % | Site 3 | % |`

#### ✅ 優化模式
- **顯示方式**: 只顯示有實際資料的 Site
- **適用情境**: 跳躍 Site (如 Site 2, 5, 8) 或單一 Site
- **優勢**: 節省 20-67% 欄位空間，避免空欄位困惑
- **範例**: `| Bin | Count | % | Definition | Note | Site 2 | % | Site 8 | % |`

### 🧠 智慧型模式推薦

#### 推薦邏輯
```python
def _recommend_display_mode(site_stats: Dict) -> str:
    """根據 Site 分佈自動推薦最適模式"""
    active_sites = sorted([site for site in site_stats.keys() if site_stats[site]])
    
    if len(active_sites) <= 1:
        return "optimized"  # 單一 Site 優先優化模式
    
    # 檢查是否為連續 Site
    is_continuous = all(active_sites[i] == active_sites[i-1] + 1 
                       for i in range(1, len(active_sites)))
    
    if is_continuous and active_sites[0] == 1:
        return "vba_compatible"  # 從 Site 1 開始的連續 Site
    else:
        return "optimized"  # 跳躍或非從 1 開始的 Site
```

#### 推薦規則
1. **單一 Site**: 推薦優化模式
2. **連續 Site (從 1 開始)**: 推薦 VBA 相容模式
3. **跳躍 Site**: 推薦優化模式
4. **非從 1 開始的連續 Site**: 推薦優化模式

### 📈 效益分析

| 情境 | VBA 模式欄位數 | 優化模式欄位數 | 節省欄位 | 節省率 |
|------|----------------|----------------|----------|--------|
| 只有 Site 2 | 9 欄 | 7 欄 | 2 欄 | 22% |
| 只有 Site 8 | 21 欄 | 7 欄 | 14 欄 | 67% |
| Site 1,3,5 | 15 欄 | 11 欄 | 4 欄 | 27% |
| Site 1,5,8 | 21 欄 | 11 欄 | 10 欄 | 48% |

### 🛠️ 技術實作

#### VBA 相容標頭生成
```python
def _generate_vba_compatible_headers(site_stats: Dict, max_site: int) -> List[str]:
    """生成 VBA 相容的標頭 (從 Site 1 到最大 Site 號)"""
    headers = ['Bin', 'Count', '%', 'Definition', 'Note']
    for i in range(1, max_site + 1):
        headers.extend([f'Site {i}', '%'])
    return headers
```

#### 優化版標頭生成
```python
def _generate_optimized_headers(site_stats: Dict) -> List[str]:
    """生成優化版標頭 (只顯示有資料的 Site)"""
    headers = ['Bin', 'Count', '%', 'Definition', 'Note']
    active_sites = sorted([site for site, stats in site_stats.items() 
                          if stats.get('total', 0) > 0])
    for site in active_sites:
        headers.extend([f'Site {site}', '%'])
    return headers
```

### 🎯 實際應用情境

#### 情境 1: 測試設備部分開啟
```
實際狀況: 只開啟 Site 2, 4, 6 進行測試
VBA 結果: 顯示 Site 1-6 (包含空的 Site 1, 3, 5)
優化結果: 只顯示 Site 2, 4, 6
效益: 節省 6 欄，更清楚的資料呈現
```

#### 情境 2: 單一設備測試
```
實際狀況: 只開啟 Site 8 進行除錯
VBA 結果: 顯示 Site 1-8 (7 個空 Site + 1 個有效 Site)
優化結果: 只顯示 Site 8
效益: 節省 14 欄，避免使用者困惑
```

#### 情境 3: 標準生產模式
```
實際狀況: Site 1-4 連續開啟
VBA 結果: 顯示 Site 1-4
優化結果: 同 VBA (推薦相容模式)
效益: 保持與 VBA 一致的使用體驗
```

## ⚡ 性能優化特性

### 向量化處理
- **BIN1 保護** 使用 numpy 向量化操作
- **設備分類** 批量比較和分配
- **數據轉換** 智慧向量化轉換

### 混合優化策略
```python
# 1. 批量寫入所有數據（無格式）
for row_idx in range(len(df)):
    worksheet.write_row(row_idx, 0, df.iloc[row_idx].tolist())

# 2. 只對紅色位置重寫格式
for row_idx, col_idx in red_positions:
    worksheet.write(row_idx, col_idx, value, red_font_format)
```

### 性能表現
- **處理時間**: 從 17.9秒 → 6.2秒（提升 65%）
- **記憶體使用**: 智慧垃圾收集和暫存檔清理
- **檔案大小**: ~4.5MB（包含完整功能）

## 🔗 超連結功能

### 超連結邏輯
- **FAIL BIN** 號碼可點擊
- **跳轉目標**: 該設備第一個失敗的測試項目
- **格式**: Excel 標準內部連結（無綠色驚嘆號）

### 實現方式
```python
# Excel 標準格式
hyperlink_url = f"#{main_sheet_name}!{target_cell}"
worksheet.write_url(device_row, 1, hyperlink_url, string=str(bin_num))
```

## 🎨 視覺化功能

### 紅色標記
- **BIN 欄位**: 設備失敗自動標紅
- **測試項目**: 超出限值自動標紅  
- **保護項目**: BIN1 保護放寬的限值標紅

### 視窗凍結
- **水平滾動**: Site 欄位之前的欄位保持可見
- **垂直滾動**: 前12行保持可見
- **動態適應**: 根據 Site 欄位位置自動調整

## 📂 檔案輸入輸出

### 輸入要求
- **檔案格式**: CSV 檔案
- **編碼**: UTF-8（自動處理錯誤）
- **位置**: 任何可存取的路徑
- **大小**: 支援大型檔案（記憶體優化）

### 輸出結果
- **檔案格式**: .xlsx（Excel 2007+）
- **命名規則**: 
  - 自動檢測模式: `processed_{原檔名}.xlsx`
  - 一般模式: `{原檔名}.xlsx`
- **輸出位置**: `logs/` 目錄
- **包含內容**: 
  - 主資料工作表（含紅色標記和超連結）
  - Summary 工作表（完整統計分析）

## 🛠️ 技術實作細節

### 智慧數字轉換
```python
def smart_convert_to_number(value):
    """只轉換純數字，保留有意義的字串"""
    # 保留: 'none', 'Test_Time', 版本號等
    # 轉換: 純數字字串
    # 避免: Excel 數字警告三角形
```

### 動態欄位檢測
```python
def find_site_column_with_log(df, header_row=7, start_col=2):
    """動態查找 Site 欄位位置"""
    # 搜尋包含 'site' 的欄位名稱
    # 排除 'Site_Check' 等非資料欄位
    # 返回精確的欄位索引
```

### 錯誤處理
- **檔案讀取**: 編碼錯誤自動處理
- **數據轉換**: 無效值安全處理
- **記憶體管理**: 自動垃圾收集
- **暫存清理**: 處理完成後自動清理

## 📝 使用範例

### 範例 1: 自動檢測處理
```bash
source venv/bin/activate
python cta_processor.py "doc/G2735KS1U-K(BA)_GHKR03.13_F2490018A_FT1_R0_ALL_20240910203816.csv"

# 輸出
🚀 整合模式：自動檢測 → 完整處理流程
🔍 檢測檔案類型: G2735KS1U-K(BA)_GHKR03.13_F2490018A_FT1_R0_ALL_20240910203816.csv
📋 檔案類型: 一般 CSV
📊 執行一般 CSV 流程...
✅ 一般CSV流程 處理成功!
⏱️ 處理時間: 5.909 秒
📄 輸出檔案: processed_G2735KS1U-K(BA)_GHKR03.13_F2490018A_FT1_R0_ALL_20240910203816.xlsx
```

### 範例 2: CTA 格式處理
```bash
python cta_processor.py "doc/cta_format_file.csv"

# 輸出
📋 檔案類型: CTA CSV
🚀 執行 CTA 整合流程...
📊 CTA → csv_to_excel_converter 整合...
✅ CTA整合流程 處理成功!
```

## 🔧 故障排除

### 常見問題

#### 1. 虛擬環境未啟動
```bash
# 錯誤: ModuleNotFoundError: No module named 'pandas'
# 解決: 先啟動虛擬環境
source venv/bin/activate
```

#### 2. 檔案路徑問題  
```bash
# 錯誤: 檔案不存在
# 解決: 使用引號包圍檔案名稱
python cta_processor.py "doc/file with spaces.csv"
```

#### 3. 記憶體不足
```bash
# 錯誤: 處理大檔案時記憶體不足
# 解決: 關閉其他程式，或分批處理
```

### 除錯功能
- **詳細記錄**: 所有處理過程記錄到 `logs/datalog.txt`
- **錯誤追蹤**: 完整的錯誤堆疊和處理時間
- **性能分析**: 各步驟耗時統計和瓶頸分析

## 📈 系統特色

### 🎯 統一處理
- **一個入口** 處理所有 CSV 格式
- **自動分流** 無需手動判斷
- **統一輸出** 都產生完整 Summary

### ⚡ 高性能
- **向量化優化** 大幅提升處理速度
- **記憶體管理** 智慧垃圾收集
- **批量操作** 減少 I/O 開銷

### 🛡️ 可靠性
- **容錯處理** 各種異常情況
- **數據完整性** 確保轉換準確
- **自動清理** 暫存檔案管理

### 🎨 用戶友好
- **視覺化標記** 紅色標記和超連結
- **動態適應** Site 欄位自動檢測
- **詳細記錄** 完整的處理日誌

## 📊 關鍵資料結構位置

### DataFrame 行位置:
- **第6行 (index 5)**: BIN 分配行
- **第7行 (index 6)**: 測試項目分組  
- **第8行 (index 7)**: 測試項目名稱
- **第10行 (index 9)**: Max 限值
- **第11行 (index 10)**: Min 限值
- **第13行起 (index 12+)**: 設備測試資料

### DataFrame 欄位位置:
- **第1欄 (index 0)**: Serial# (序號)
- **第2欄 (index 1)**: Bin# (設備 BIN 分配)
- **第3欄 (index 2)**: Time (時間)
- **第4欄 (index 3)**: Index_Time (索引時間)  
- **第5欄 (index 4)**: Site_No (Site 號碼)
- **第6欄起 (index 5+)**: 測試項目資料

## 🎯 處理結果

### 效能統計:
- **總處理時間**: ~5.5 秒 (相比原始 60 秒提升 90%+)
- **主要瓶頸**: Excel 輸出 (86.2%)
- **向量化優化**: BIN1 保護 + 設備分類

### 輸出檔案:
- **主資料工作表**: 包含所有原始資料 + 紅色標記 (失敗項目)
- **Summary 工作表**: 統計摘要 + 正常格式 (整頁不變色)
- **檔案大小**: ~4.5MB
- **格式**: .xlsx (xlsxwriter 高性能版本)

## 🛡️ 品質保證記錄

### BIN1 保護記錄範例:
```
🔴 BIN1 保護記錄
================================================================================
發現 BIN1 設備數量: 315
需要保護的測試項目數量: 7

| 欄位      | 測試項目編號   | 測試項目名稱            | 原始 Max 限值  | 原始 Min 限值  | step3 結果         |
|---------|----------|-------------------|------------|------------|------------------|
| C       | 0.00.01  | Test_Time         | none       | none       | Max & Min → none |
```

### 性能記錄範例:
```
⚡ CSV 到 Excel 轉換器性能記錄
================================================================================
📊 各步驟處理時間 (按耗時排序):
  1. 步驟8_Excel輸出    :    4.746 秒 ( 86.2%)
  2. 步驟5_設備BIN分類  :    0.351 秒 (  6.4%)
  3. 步驟4_BIN1保護     :    0.213 秒 (  3.9%)
  4. 步驟1_CSV讀取      :    0.099 秒 (  1.8%)
  5. 步驟7_Summary生成  :    0.056 秒 (  1.0%)
```

## 🔧 核心演算法

### BIN1 保護演算法:
```
for 每個測試項目:
    if (限值有效 AND 有BIN1設備):
        for 每個BIN1設備:
            if (設備測試值為0 AND 設備會失敗):
                放寬該測試項目限值
                記錄保護位置
                break
```

### 設備 BIN 分類演算法:
```
for 每個設備:
    設備BIN = 1  // 預設 All Pass
    
    for 每個測試項目:
        if (設備測試值超出限值):
            設備BIN = 該測試項目對應BIN
            記錄失敗位置
            break  // 只取第一個失敗項目
```

### Summary 生成演算法:
```
1. 統計各BIN設備數量
2. 統計各Site的Pass/Fail分佈
3. 生成BIN定義對應表
4. 排序BIN (BIN1永遠第一，其他按Count降序)
5. 生成完整BIN列表 (包含Count=0)
6. 格式化輸出結構
```

## 📝 重要注意事項

1. **BIN1 保護**: 只有測試數值為 0 且會失敗的情況才觸發保護
2. **Summary 格式**: 整頁使用正常格式，不變紅色
3. **BIN 排序**: BIN 1 永遠排第一行，符合業務邏輯
4. **百分比格式**: 使用 0% 格式，不是 0.0%
5. **向量化優化**: 關鍵步驟使用 numpy 向量化操作提升性能
6. **記錄完整**: 所有重要操作都記錄到 logs/datalog.txt

---

**版本**: 2025-06-07 程式碼同步版  
**符合規範**: CLAUDE.md 功能替換原則 ✅  
**測試狀態**: 全功能驗證完成 ✅  
**文件整合**: 統一 Excel 處理說明書 📋  
**程式碼同步**: 與 `csv_to_excel_converter.py` + `cta_integrated_processor.py` 完全一致 ✅  
**包含內容**: CSV轉換 + CTA處理 + Summary生成 + Site顯示優化 + 實時性能監控 + 詳細技術規範