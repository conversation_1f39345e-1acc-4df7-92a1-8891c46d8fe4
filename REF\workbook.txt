Private WithEvents olInboxItems As Outlook.Items
Private recordedSubjects As New Collection
Private Sub Workbook_Open()
    ' 監控 Outlook 中收件箱中的郵件
    Dim olApp As Outlook.Application
    Dim olNs As Outlook.Namespace
    Dim olInboxFolder As Outlook.MAPIFolder
    
    Set olApp = New Outlook.Application
    Set olNs = olApp.GetNamespace("MAPI")
    Set olInboxFolder = olNs.GetDefaultFolder(olFolderInbox)
    Set olInboxItems = olInboxFolder.Items
    
    SendSummaryEmail
    'Call ScheduleSub
    ' 初始化時記錄現有郵件
    'RecordEmails
    Dim cellValue As String
    cellValue = Worksheets("Sheet2").Range("D13").value ' 請根據實際需要更換工作表名
    If UCase(cellValue) <> "Y" Then
        StartTimerDual
    End If
End Sub
Private Sub olInboxItems_ItemAdd(ByVal Item As Object)
    Dim i  As Integer
    'i = 0
    'WriteNumberedTimeToFile 0
    Dim cellValue As String
    cellValue = Worksheets("Sheet2").Range("D13").value ' 請根據實際需要更換工作表名
    If UCase(cellValue) = "Y" Then
        If TypeOf Item Is Outlook.MailItem Then
            Dim recipients As Outlook.recipients
            Set recipients = Item.recipients
            Dim recipient As Outlook.recipient
    
            Dim folderPathnet1 As String
            Dim filecnt  As Integer
            filecnt = 0
            folderPathnet1 = ""
            For Each recipient In recipients
                RecordEmail Item, folderPathnet1
                Exit For
            Next recipient
        End If
        'i = i + 1
        'filecnt = Check_download_fail_file()
       ' Call StartTimer2
        'Call ScheduleSub
        'WriteNumberedTimeToFile i
        'Call ScheduleSubx
        On Error Resume Next
            Application.OnTime Now + TimeValue("00:00:05"), "Module1.ReadtemplotFile"
        On Error GoTo 0
    End If
End Sub
Private Sub RecordExistingEmails()
    ' 記錄收件箱中的所有現有郵件
    Dim olMail As Outlook.MailItem
    Dim folderPathnet1 As String
    folderPathnet1 = ""
    For Each olMail In olInboxItems
        If TypeOf olMail Is MailItem Then '只處理郵件類型
            RecordEmail olMail, folderPathnet1
        End If
    Next olMail
End Sub
Private Function GetKeywordValue(ByVal keyword As String, ByVal subject As String) As String
    ' 從主題中提取關鍵字的值，值的格式為 "keyword: value"
    Dim value As String
    If InStr(1, subject, keyword & ":", vbTextCompare) > 0 Then ' 檢查主題是否包含關鍵字，忽略大小寫
        value = Trim(Mid(subject, InStr(1, subject, keyword & ":", vbTextCompare) + Len(keyword) + 1))
        ' 獲取關鍵字後面的文本，忽略前導和尾隨空格
        If InStr(value, "~") > 0 Then
            value = Left(value, InStr(value, "~") - 1)
        End If
        If InStr(value, " ") > 0 Then
            value = Left(value, InStr(value, " ") - 1)
        End If
        If InStr(value, " ") > 0 Then
            value = Left(value, InStr(value, "_") - 1)
        End If
    Else
        value = "?"
    End If
    GetKeywordValue = value
End Function
Private Function FindBin1Line(ByVal mail As Outlook.MailItem) As String
    ' 查找郵件正文中包含 "BIN1:" 的行並返回該行內容
    Dim body As String
    Dim lines() As String
    Dim i As Long
    body = mail.body ' 獲取郵件正文
    lines = Split(body, vbCrLf) ' 按行切分郵件正文
    For i = 0 To UBound(lines)
        If InStr(1, LCase(lines(i)), "bin1:", vbTextCompare) > 0 Then ' 找到包含 "BIN1:" 關鍵字的那一行
            FindBin1Line = lines(i) ' 返回該行內容
            Exit For ' 找到後直接跳出循環
        End If
    Next i
End Function
Private Function GetInQty(ByVal mail As Outlook.MailItem) As String
    ' 從郵件正文中提取包含 "IN QTY" 關鍵字的那一行並返回
    Dim body As String
    Dim lines() As String
    Dim i As Long
    body = mail.body ' 獲取郵件正文
    lines = Split(body, vbCrLf) ' 按行切分郵件正文
    For i = 0 To UBound(lines)
        If InStr(1, LCase(lines(i)), "in qty", vbTextCompare) > 0 Then ' 找到包含 "IN QTY" 關鍵字的那一行
            Dim start As Long
            Dim end1  As Long
            start = InStr(LCase(lines(i)), "in qty:") + Len("IN QTY:") ' 定位 "IN QTY:" 後面的位置
            end1 = InStr(start, lines(i), " ") ' 定位下一個空格的位置
            GetInQty = Trim(Mid(lines(i), start, end1))  ' 獲取 "IN QTY:" 後面的值並去除首尾空格
            Exit For ' 找到後直接跳出循環
        End If
    Next i
End Function
Function GetNumberFromString(str As String) As Double
    Dim regex As Object
    Set regex = CreateObject("VBScript.RegExp")
    regex.Global = True
    regex.pattern = "\d+"
    Dim matches As Object
    Set matches = regex.Execute(str)
    If matches.count > 0 Then
        GetNumberFromString = CDbl(matches(0))
    Else
        GetNumberFromString = 0
    End If
End Function
Private Function GetQtyFromMailBody(ByVal mailBody As String) As String
    Dim qtyLine As String
    Dim qty As String
    
    qtyLine = ""
    qty = ""
    
    ' 搜尋郵件內容是否包含 "Qty:" 關鍵字
    If InStr(1, mailBody, "Qty:", vbTextCompare) > 0 Then
        ' 取得 "Qty:" 所在的行
        qtyLine = Split(mailBody, "Qty:")(1)
        ' 取得 "Qty:" 後面第一個空格到下一個空格或字元 (的文字內容)
       ' qty = Trim(Left(qtyLine, InStr(qtyLine, " ") - 1))

        Dim end1  As Long
        If Left(qtyLine, 1) = " " Then
            ' 字符串 qtyLine 第一?字符是空格
             end1 = InStr(Mid(qtyLine, 2), " ") ' 定位下一個空格的位置
             If end1 > 10 Then
                end1 = InStr(Mid(qtyLine, 2), "S") ' 定位下一個空格的位置
             End If
        Else
            end1 = InStr(qtyLine, " ") ' 定位下一個空格的位置
        End If
       
        qty = Left(qtyLine, end1) ' 獲取 "QTY:" 後面的值並去除首尾空格
    End If
    If Len(qty) > 0 Then
    ' 回傳取得的數量
        GetQtyFromMailBody = qty
    Else
        GetQtyFromMailBody = 0
    End If
End Function
Function GetYieldFromMail(mailBody As String) As String
    ' 找到 yield 那一行
    Dim lines As Variant
    lines = Split(mailBody, vbCrLf)
    Dim lineIndex As Long
    For lineIndex = LBound(lines) To UBound(lines)
        If InStr(1, lines(lineIndex), "yield", vbTextCompare) > 0 Then
            Exit For
        End If
    Next lineIndex
    If lineIndex > UBound(lines) Then
        ' 找不到 yield 那一行
        GetYieldFromMail = ""
        Exit Function
    End If
    ' 在 yield 那一行找到第一個出現的 % 前至空格或:或(
    Dim yieldLine As String
    yieldLine = Trim(lines(lineIndex))
    Dim percentIndex As Long
    percentIndex = InStr(1, yieldLine, "%")
    If percentIndex = 0 Then
        ' yield 那一行找不到 %
        GetYieldFromMail = ""
        Exit Function
    End If
    Dim endIndex As Long
    endIndex = Len(yieldLine)
    Dim searchIndex As Long
    For searchIndex = percentIndex - 1 To 1 Step -1
        Dim currentChar As String
        currentChar = Mid(yieldLine, searchIndex, 1)
        If currentChar = " " Or currentChar = ":" Or currentChar = "(" Or currentChar = "：" Then
            endIndex = searchIndex
            Exit For
        End If
    Next searchIndex
  
    GetYieldFromMail = Mid(yieldLine, endIndex + 1, percentIndex - endIndex)
End Function
Function FindLineContainingString(str As String, searchString As String) As String
    Dim lines As Variant
    lines = Split(str, vbCrLf)
    For i = LBound(lines) To UBound(lines)
        If InStr(1, lines(i), searchString) > 0 Then
            FindLineContainingString = lines(i)
            Exit Function
        End If
    Next i
End Function
Function GetLineBitCount(str As String) As Long
    ' 先把換行字元拿掉
    str = Replace(str, vbLf, "")
    str = Replace(str, vbCrLf, "")
    
    ' 找出第一個斷行的位置
    Dim lineBreak As Long
    lineBreak = InStr(str, vbCr)
    If lineBreak = 0 Then
        ' 找不到斷行字元，表示整個字串都是一行
        GetLineBitCount = Len(str)
    Else
        ' 找到第一個斷行字元，回傳該行的 bit 數
        GetLineBitCount = lineBreak - 1
    End If
End Function
Function ExtractCode(s As String) As String
    Dim i As Integer
    For i = Len(s) To 1 Step -1
        If IsNumeric(Mid(s, i, 1)) Or IsLetter(Mid(s, i, 1)) Then
            ExtractCode = Left(s, i)
            Exit Function
        End If
    Next i
    ExtractCode = ""
End Function
Function IsLetter(s As String) As Boolean
    IsLetter = LCase(s) >= "a" And LCase(s) <= "z"
End Function
Function ExtractString(str As String) As String
    Dim pos As Integer
    pos = InStr(1, str, "~", vbTextCompare)
    If pos > 0 Then
        ExtractString = Left(str, pos - 1)
    Else
        ExtractString = str
    End If
End Function
Function GetUnicodeSubjectMAPI(olMail As Object) As String
    On Error Resume Next
    Dim PR_SUBJECT_W As String
    PR_SUBJECT_W = "http://schemas.microsoft.com/mapi/proptag/0x0037001F" ' MAPI ?? - Unicode ??

    ' ?? PropertyAccessor ?? Unicode ?????
    GetUnicodeSubjectMAPI = olMail.PropertyAccessor.GetProperty(PR_SUBJECT_W)
    
    ' ????
    If Err.Number <> 0 Then
        GetUnicodeSubjectMAPI = "Error reading subject"
    End If
    On Error GoTo 0
End Function
Private Sub RecordEmail(olMail As Outlook.MailItem, folderPathnet As String) '0
    Dim subject As String
    Dim r As Long
    Dim existingSubject As Long
    Dim receivedDate As Date
    Dim senderAddress As String
    Dim moString As String
    Dim lotString As String
    Dim lotStringtemp As String
    Dim yieldString As String
    Dim ft_or_eqc_fail As String
    Dim inQty As String ' IN QTY 的值
    Dim summary_line As String
    Dim body As String
    Dim factory As String
    Dim product As String
    Dim filedownloadok As Boolean
    Dim linemessage As String
    Dim sendmail As Boolean
    Dim inQtydouble As Double
    Dim oAttachment As Outlook.attachment
    Dim sSaveFolder As String
    Dim sFileType As String
    Dim destinationPath As String
    Dim recipients As Outlook.recipients
    Dim recipient As Outlook.recipient
    Dim recipientAddress As String
    Dim toAddresses() As String
    Dim ccAddresses() As String
    Dim ccTelowyield01 As Boolean
    Dim i As Integer
    Dim j As Integer
    Dim k As Integer
    ccTelowyield01 = False
    i = 0
    j = 0
    destinationPath = Sheet2.Range("D2").value ' 設定預設路徑為Sheet2的D2儲存格的值
    sSaveFolder = destinationPath '設定下載附件的儲存路徑
    sFileType = "*.rar,*.zip" '設定附件類型
    
    fileok = False
    sendmail = False
    filedownloadok = False
    subject = olMail.subject
    'subject = StrConv(olMail.subject, vbFromUnicode)
    'subject = GetUnicodeSubjectMAPI(olMail)

    'subject = StrConv(StrConv(olMail.subject, vbUnicode), vbFromUnicode)

    receivedDate = olMail.ReceivedTime
    senderAddress = olMail.SenderEmailAddress
    body = olMail.body ' 獲取郵件正文


    subject = Replace(subject, "?", "")
    Dim receivemain As String
    
    If CheckMailAddress(senderAddress) = True Then
        receivemain = senderAddress '寄過來的人 寄件者
    End If
    
    For Each recipient In olMail.recipients
        ' 如果 recipient.Type 是 olTo，那主收件人
        If recipient.Type = olTo Then
            'ToRecipients = ToRecipients & recipient.Address & "; "
            If CheckMailAddress(recipient.Address) = True Then
                ReDim Preserve toAddresses(i)
                toAddresses(i) = recipient.Address
                i = i + 1
            End If
        End If
        ' 如果 recipient.Type 是 olCC，C.C.
        If recipient.Type = olCC Then
            'CCRecipients = CCRecipients & recipient.Address & "; "
            If CheckMailAddress(recipient.Address) = True Then
                ReDim Preserve ccAddresses(j)
                ccAddresses(j) = recipient.Address
                j = j + 1
                If InStr(1, recipient.Address, "<EMAIL>", vbTextCompare) > 0 Then
                    ccTelowyield01 = True
                End If
            End If
        End If
    Next recipient
    
    ' 去除最後一個分號
    'toAddresses = Left(toAddresses, Len(toAddresses) - 1)
    'ccAddresses = Left(ccAddresses, Len(ccAddresses) - 1)

    If ccTelowyield01 = True Then
        CC_FLAG = True
        receivemain = ""
        For k = 0 To UBound(toAddresses)
            receivemain = receivemain + toAddresses(k) & "; "
        Next k
    Else
        CC_FLAG = False
    End If
    If receivemain = "" Then '如果寄件者是外測廠 則這個為Null
        Exit Sub
    End If
    
'    Set objRecipients = olMail.recipients
'    objRecipients.Add "收件者*************"
'    objRecipients.Add "收件者*************"
'    objRecipients.ResolveAll

    'Set objRecipients = olMail.recipients
    'objRecipients.Add "收件者*************"
    'objRecipients.Add "收件者*************"
    'objRecipients.Add "抄送收件者*************", olCC
    'objRecipients.Add "抄送收件者*************", olCC
    'objRecipients.ResolveAll
    
    
    inQtydouble = 0
    yieldString = "0%"
    ft_or_eqc_fail = "FT LOW OR EQC"
    If InStr(1, LCase(subject), "ft hold", vbTextCompare) Or InStr(1, LCase(subject), "ft lot", vbTextCompare) > 0 Then 'GTK
        moString = GetKeywordValue("mo", LCase(subject))
        moString = ExtractCode(moString)
        lotString = GetKeywordValue("lot", LCase(subject))
        lotString = ExtractString(lotString)
        lotStringtemp = lotString
        On Error Resume Next
            lotStringtemp = Left(lotString, InStr(lotString, ".") - 1)
        On Error GoTo 0
        yieldString = GetKeywordValue("yield", LCase(subject))
        inQty = GetInQty(olMail) ' 獲取 IN QTY 的值
        inQtydouble = GetNumberFromString(inQty)
'        r = Sheet1.Cells(rows.count, 1).End(xlUp).row + 1 '找到最後一行記錄
'        Sheet1.Cells(r, 1).value = receivedDate
'        Sheet1.Cells(r, 2).value = "GTK"
'        Sheet1.Cells(r, 3).value = receivemain
        product = UCase(GetKeywordValue("type", LCase(subject)))
        If Right(product, 1) = "," Then
            product = Left(product, Len(product) - 1)
        End If
'        Sheet1.Cells(r, 4).value = product
        If Right(moString, 1) = "," Then
            moString = Left(moString, Len(moString) - 1)
        End If
'        Sheet1.Cells(r, 5).value = UCase(moString)
'        Sheet1.Cells(r, 6).value = UCase(lotString)
'        Sheet1.Cells(r, 7).value = "FALSE"
'        Sheet1.Cells(r, 8).value = inQtydouble
'        Sheet1.Cells(r, 9).value = yieldString
        factory = "GTK"
        lotStringtemp = UCase(lotString)
        
        If InStr(1, LCase(subject), "low", vbTextCompare) > 0 Then
            ft_or_eqc_fail = "Low Yield ISSUE"
        End If
        If InStr(1, LCase(subject), "eqc", vbTextCompare) > 0 Then
            If ft_or_eqc_fail <> "" Then
                ft_or_eqc_fail = ft_or_eqc_fail & "+ EQC ISSUE"
            Else
                ft_or_eqc_fail = "EQC ISSUE"
            End If
        End If
        summary_line = FindBin1Line(olMail)
        If summary_line <> "" Then
            ft_or_eqc_fail = ft_or_eqc_fail & Chr(10) & summary_line ' 添加 summary_line 並換行
        End If
'        Sheet1.Cells(r, 11).value = ft_or_eqc_fail ' 將 ft_or_eqc_fail 的內容寫入單元格
        If CreateFolders(product, UCase(moString)) = True Then
             For Each oAttachment In olMail.Attachments
                oAttachment.SaveAsFile tempPath & "\" & product & "\" & UCase(moString) & "\" & oAttachment.fileName '下載附件
            Next oAttachment
            'filedownloadok = CopyFilesGTK(UCase(moString), tempPath & "\" & product & "\" & UCase(moString) & "\", product, lotStringtemp)
        End If
    ElseIf InStr(1, LCase(subject), "anf", vbTextCompare) > 0 Then 'ETD
        Dim myArray() As String
        myArray = Split(subject, "/")
'        r = Sheet1.Cells(rows.count, 1).End(xlUp).row + 1 '找到最後一行記錄
'        Sheet1.Cells(r, 1).value = receivedDate
'        'Sheet1.Cells(r, 2).value = GetSenderCode(senderAddress)
'        Sheet1.Cells(r, 2).value = "ETD"
'        Sheet1.Cells(r, 3).value = receivemain
        product = myArray(1)
        If Right(product, 1) = "," Then
            product = Left(product, Len(product) - 1)
        End If
'        Sheet1.Cells(r, 4).value = product
        moString = Left(myArray(6), Len(myArray(6)) - 1)
        'Sheet1.Cells(r, 5).value = moString
        lotString = myArray(4)
       ' Sheet1.Cells(r, 6).value = lotString
       ' Sheet1.Cells(r, 7).value = "FALSE"
       
        
        'Sheet1.Cells(r, 8).value = CDbl(GetQtyFromMailBody(body))
       ' Sheet1.Cells(r, 9).value = GetYieldFromMail(body)
        Dim tempstr As String
        tempstr = FindLineContainingString(body, "異常")
        'Sheet1.Cells(r, 11).value = tempstr
        Dim cell As Range
        
        'Set cell = Sheet1.Cells(r, 11)
        Dim cellWidth As Double
       ' cellWidth = cell.ColumnWidth
        Dim tempstr_bitcount As Long
        tempstr_bitcount = GetLineBitCount(tempstr)
'        If cellWidth < tempstr_bitcount Then
'            Sheet1.Cells(r, 101).ColumnWidth = tempstr_bitcount + 15
'        End If
        factory = "ETD"
        lotStringtemp = UCase(lotString)
        inQtydouble = CDbl(GetQtyFromMailBody(body))
        yieldString = GetYieldFromMail(body)
        ft_or_eqc_fail = tempstr
'        product = "G2532USKE1U-K"
'        moString = "*********"
'        lotString = "DLF2A.1D"
'        CopyFilesETD product, moString, lotString
        If CreateFolders(product, UCase(moString)) = True Then
             For Each oAttachment In olMail.Attachments
                oAttachment.SaveAsFile tempPath & "\" & product & "\" & UCase(moString) & "\" & oAttachment.fileName '下載附件
            Next oAttachment
            'filedownloadok = CopyFilesETD(product, moString, lotString)
        End If
    ElseIf InStr(1, LCase(body), "chuzhou", vbTextCompare) > 0 Then 'Chuzhou
    'subject
'        r = Sheet1.Cells(rows.count, 1).End(xlUp).row + 1 '找到最後一行記錄
'        Sheet1.Cells(r, 1).value = receivedDate
'        Sheet1.Cells(r, 2).value = "Chuzhou"
'        Sheet1.Cells(r, 3).value = receivemain
        ChuzhouInfoFromStrings subject, product, moString, lotString
'        Sheet1.Cells(r, 4).value = product
'        Sheet1.Cells(r, 5).value = moString
'        Sheet1.Cells(r, 6).value = lotString
'        Sheet1.Cells(r, 7).value = "FALSE"
        factory = "Chuzhou"
        lotStringtemp = lotString
     ElseIf InStr(1, LCase(body), "宿", vbTextCompare) > 0 Then  'Suqian
    'subject
'        r = Sheet1.Cells(rows.count, 1).End(xlUp).row + 1 '找到最後一行記錄
'        Sheet1.Cells(r, 1).value = receivedDate
'        Sheet1.Cells(r, 2).value = "Suqian"
'        Sheet1.Cells(r, 3).value = receivemain
        SuqianInfoFromStrings subject, product, moString, lotString, yieldString
'        Sheet1.Cells(r, 4).value = product
'        Sheet1.Cells(r, 5).value = moString
'        Sheet1.Cells(r, 6).value = lotString
'        Sheet1.Cells(r, 7).value = "FALSE"
'        Sheet1.Cells(r, 9).value = yieldString
        factory = "Suqian"
        lotStringtemp = lotString
     ElseIf InStr(1, UCase(body), "LINGSEN", vbTextCompare) > 0 Or InStr(1, LCase(senderAddress), "lingsen", vbTextCompare) > 0 Then 'LINGSEN
    'subject
'        r = Sheet1.Cells(rows.count, 1).End(xlUp).row + 1 '找到最後一行記錄
'        Sheet1.Cells(r, 1).value = receivedDate
'        Sheet1.Cells(r, 2).value = "LINGSEN"
'        Sheet1.Cells(r, 3).value = receivemain
        LINGSENInfoFromStrings subject, product, moString, lotString, yieldString
'        Sheet1.Cells(r, 4).value = product
'        Sheet1.Cells(r, 5).value = moString
'        Sheet1.Cells(r, 6).value = lotString
'        Sheet1.Cells(r, 7).value = "FALSE"
'        Sheet1.Cells(r, 9).value = yieldString
        factory = "LINGSEN"
        lotStringtemp = lotString
    ElseIf InStr(1, UCase(body), "Nanotech ", vbTextCompare) > 0 Or InStr(1, LCase(senderAddress), "lingsen", vbTextCompare) > 0 Then 'Nanotech
'        Dim osstring As String
'        Dim body_temp As String
'        osstring = Nanotechbody(body)
'        If osstring <> "" Then
'            Dim words() As String
'            words = Split(body, " ")
'            For i = LBound(words) To UBound(words)
'                If InStr(1, LCase(words(i)), osstring, vbTextCompare) > 0 Then
'                    body_temp = words(i)
'                    Exit For
'                End If
'            Next i
'        End If
'        If body_temp <> "" Then
'            NanotechInfoFromStrings body_temp, product, moString, lotString, yieldString
'        End If
'        Sheet1.Cells(r, 1).value = receivedDate
'        Sheet1.Cells(r, 2).value = "Nanotech"
'        Sheet1.Cells(r, 3).value = receivemain
        LINGSENInfoFromStrings subject, product, moString, lotString, yieldString
'        Sheet1.Cells(r, 4).value = product
'        Sheet1.Cells(r, 5).value = moString
'        Sheet1.Cells(r, 6).value = lotString
'        Sheet1.Cells(r, 7).value = "FALSE"
'        Sheet1.Cells(r, 9).value = yieldString
        factory = "Nanotech"
        lotStringtemp = lotString
    ElseIf InStr(1, LCase(subject), "low yield hold", vbTextCompare) > 0 Or InStr(1, LCase(senderAddress), "msec.com.tw", vbTextCompare) > 0 Then 'MSECZD
        MSECZDInfoFromStrings subject, product, moString, lotString, yieldString
        factory = "MSECZD"
        lotStringtemp = lotString
    ElseIf InStr(1, LCase(body), "tfme", vbTextCompare) > 0 Or InStr(1, LCase(senderAddress), "tfme.com", vbTextCompare) > 0 Or InStr(1, LCase(body), "tfme.com", vbTextCompare) > 0 Then   'NFME
        NFMEInfoFromStrings subject, product, moString, lotString, yieldString
        factory = "NFME"
        lotStringtemp = lotString
        lotStringtemp = moString
    ElseIf InStr(1, LCase(body), "jcet", vbTextCompare) > 0 Or InStr(1, LCase(senderAddress), "jcetglobal.com", vbTextCompare) > 0 Or InStr(1, LCase(JCET), "jcetglobal.com", vbTextCompare) > 0 Then   'JCET
        JCETInfoFromStrings subject, product, moString, lotString, yieldString
        factory = "JCET"
        lotStringtemp = lotString
        lotStringtemp = moString
     ElseIf InStr(1, LCase(body), "tianshui", vbTextCompare) > 0 Or InStr(1, LCase(body), "西安", vbTextCompare) > 0 Or InStr(1, LCase(subject), "西安", vbTextCompare) > 0 Then     'XAHT tianshuitianshuitianshuitianshuitianshuitianshui???????????
       XAHTInfoFromStrings subject, product, moString, lotString, yieldString
        If Len(moString) = 0 Then
            XAHTInfoFromStrings2 subject, product, moString, lotString, yieldString
        End If
        factory = "XAHT"
        lotStringtemp = lotString
        lotStringtemp = moString
     ElseIf InStr(1, LCase(body), "tianshui", vbTextCompare) > 0 Or InStr(1, LCase(senderAddress), "ht-tech.com", vbTextCompare) > 0 Then 'TianShui tianshuitianshuitianshuitianshuitianshui?????
        FindTw083 subject, product, moString, lotString
        factory = "TSHT"
        lotStringtemp = lotString
    ElseIf InStr(1, LCase(subject), "ask", vbTextCompare) > 0 Then 'CUST ASK
        Dim myArray2() As String
        'subject = "ask gtk G2515UG1U-K(BG) Q3FS41.1X"
        myArray2 = Split(subject, " ")
        product = myArray2(2)
        moString = myArray2(3)
        lotString = myArray2(3)
        lotStringtemp = lotString
'        r = Sheet1.Cells(rows.count, 1).End(xlUp).row + 1 '找到最後一行記錄
'        Sheet1.Cells(r, 1).value = receivedDate
'        If InStr(1, LCase(myArray2(1)), "gtk", vbTextCompare) > 0 Then Sheet1.Cells(r, 2).value = "GTK"
'        If InStr(1, LCase(myArray2(1)), "etd", vbTextCompare) > 0 Then Sheet1.Cells(r, 2).value = "ETD"
'        Sheet1.Cells(r, 3).value = senderAddress
'        Sheet1.Cells(r, 4).value = product
'        Sheet1.Cells(r, 5).value = "ASK"
'        Sheet1.Cells(r, 6).value = UCase(lotString)
'        Sheet1.Cells(r, 7).value = "FALSE"
        'filedownloadok = CopyFilesGTK2(tempPath & "\" & product & "\" & UCase(moString) & "\", product, lotStringtemp)
    End If
    Dim linedata As String
'    linedata = receivedDate & " " & factory & " " & receivemain & " " & product & " " & UCase(moString) & " " & lotStringtemp & " " & _
'               "FALSE " & inQtydouble & " " & yieldString
    If Len(factory) > 0 And Len(moString) > 0 Then
        linedata = receivedDate & vbTab & factory & vbTab & receivemain & vbTab & product & vbTab & UCase(moString) & vbTab & lotStringtemp & vbTab & _
                   "FALSE" & vbTab & inQtydouble & vbTab & yieldString & vbTab & subject
        WritetemplotFile linedata
        CopyFolderCreateIfNotExist product, UCase(moString) '20231027
    End If
'    If filedownloadok = True Then sendmail = True
'    If sendmail = True Then
'        FolderPicker tempPath & "\" & product & "\" & UCase(moString), netpath & "\" & product & "\" & UCase(moString), receivemain, "AutoMail: " & subject
'        If Dir(tempPath & "\" & product & "\" & UCase(moString) & "\" & "EQCTOTALDATA.xlsx") <> "" Or Dir(tempPath & "\" & product & "\" & UCase(moString) & "\" & "EQC_SUMMARY.xlsx") <> "" Or Dir(tempPath & "\" & product & "\" & UCase(moString) & "\" & "FT_SUMMARY.xlsx") <> "" Then
'            moveilfe tempPath & "\" & product & "\" & UCase(moString)
'        End If
'        If Dir(netpath & "\" & product & "\" & UCase(moString) & "\" & "EQCTOTALDATA.xlsx") <> "" Or Dir(netpath & "\" & product & "\" & UCase(moString) & "\" & "EQC_SUMMARY.xlsx") <> "" Or Dir(netpath & "\" & product & "\" & UCase(moString) & "\" & "FT_SUMMARY.xlsx") <> "" Then
'            Sheet1.Cells(r, 7).value = "TRUE"
'            folderPathnet = netpath & "\" & product & "\" & UCase(moString)
'            linemessage = netpath & "\" & product & "\" & UCase(moString) & "\" & "is OK"
'            TestSend_LINE_Notify_Message linemessage
'        Else
'            Sheet1.Cells(r, 7).value = "FALSE"
'        End If
'    End If
    
End Sub
Private Function GetSenderCode(senderAddress As String) As String
    ' 根據發件人名稱獲取發件人代碼
    If InStr(1, senderAddress, "greatek", vbTextCompare) > 0 Then
        GetSenderCode = "GTK"
    ElseIf InStr(1, senderAddress, "etrendtech", vbTextCompare) > 0 Then
        GetSenderCode = "ETD"
    ElseIf InStr(1, senderAddress, "GMT", vbTextCompare) > 0 Then
        GetSenderCode = "GMT"
    Else
        GetSenderCode = ""
    End If
End Function


