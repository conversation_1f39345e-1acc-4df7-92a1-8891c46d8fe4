# 公司郵件系統 POP3/SMTP 整合指南

## 概述

本指南說明如何使用專為公司郵件伺服器（hcmail.gmt.com.tw）設計的 POP3/SMTP 整合方案。

## 系統架構

### 郵件伺服器設定
- **伺服器位址**: hcmail.gmt.com.tw
- **POP3 收信**: 端口 1100 (SSL)
- **SMTP 發信**: 端口 2500 (TLS, 需驗證)

### 核心檔案
```
.env                        # 郵件配置檔案
email_config.py            # 配置載入模組
test_pop3_company.py       # POP3/SMTP 測試腳本
README_COMPANY_EMAIL.md    # 本說明文件
```

## 快速開始

### 1. 設定帳號密碼

編輯 `.env` 檔案，修改以下兩行：

```bash
# 將以下設定改為您的實際帳號
EMAIL_ADDRESS=<EMAIL>
EMAIL_PASSWORD=your_actual_password
```

### 2. 執行測試

```bash
# 執行完整的 POP3/SMTP 測試
python3 test_pop3_company.py

# 測試配置載入
python3 email_config.py
```

### 3. 預期輸出

成功連接時會顯示：
```
=== 公司郵件伺服器 POP3/SMTP 測試 ===
測試目標: hcmail.gmt.com.tw

🔗 正在測試 POP3 連接...
   伺服器: hcmail.gmt.com.tw:1100
   SSL: 是
🔐 正在驗證帳號...
✅ POP3 連接成功！
📧 共有 25 封郵件

📧 正在讀取最新 10 封郵件...

1. 主旨: 工作報告
   寄件者: <EMAIL>
   時間: 2025-07-09 14:30:00
   附件: 無
   內容預覽: 本週工作進度報告...
```

## 詳細配置

### 環境變數配置

完整的 `.env` 配置選項：

```bash
# 必要設定
EMAIL_ADDRESS=<EMAIL>
EMAIL_PASSWORD=your_password

# POP3 設定（可選，有預設值）
POP3_SERVER=hcmail.gmt.com.tw
POP3_PORT=1100
POP3_USE_SSL=true
POP3_TIMEOUT=30

# SMTP 設定（可選，有預設值）
SMTP_SERVER=hcmail.gmt.com.tw
SMTP_PORT=2500
SMTP_USE_AUTH=true
SMTP_USE_TLS=true
SMTP_TIMEOUT=30

# 處理設定（可選）
EMAIL_CHECK_INTERVAL=60
EMAIL_MAX_FETCH_COUNT=50
EMAIL_DELETE_AFTER_READ=false
EMAIL_ENABLE_MONITORING=true
```

### 程式化使用

```python
from email_config import EmailConfigManager

# 載入配置
config = EmailConfigManager()

# 取得各項設定
pop3_config = config.get_pop3_config()
smtp_config = config.get_smtp_config()
account = config.get_email_account()

# 驗證配置
validation = config.validate_config()
if not validation['valid']:
    print("配置有錯誤:", validation['errors'])
```

## 功能特色

### POP3 收信功能
- ✅ 支援 SSL 連接
- ✅ 自動解碼中文郵件
- ✅ 附件資訊檢查
- ✅ 郵件內容預覽
- ✅ 可設定讀取數量限制

### SMTP 發信功能
- ✅ 支援 TLS 加密
- ✅ 驗證帳號密碼
- ✅ 中文郵件發送
- ✅ 測試郵件功能

### 配置管理
- ✅ 從 .env 檔案載入設定
- ✅ 環境變數覆蓋
- ✅ 配置驗證和錯誤檢查
- ✅ 預設值支援

## 使用場景

### 1. 基本郵件檢查

```bash
# 檢查是否有新郵件
python3 test_pop3_company.py
```

### 2. 發送測試郵件

執行測試腳本時選擇發送測試郵件：
```
是否發送測試郵件？(y/N): y
```

### 3. 整合到現有系統

```python
# 在您的 Python 程式中使用
from email_config import EmailConfigManager
import poplib

config_manager = EmailConfigManager()
pop3_config = config_manager.get_pop3_config()
account = config_manager.get_email_account()

# 建立 POP3 連接
mail = poplib.POP3_SSL(pop3_config.server, pop3_config.port)
mail.user(account.email_address)
mail.pass_(account.password)

# 處理郵件...
```

## 常見問題

### Q1: 連接失敗，顯示「POP3 協議錯誤」

**可能原因：**
1. 帳號或密碼錯誤
2. 伺服器端口設定錯誤
3. SSL/TLS 設定問題
4. 防火牆阻擋

**解決方法：**
1. 確認 `.env` 中的帳號密碼正確
2. 檢查公司網路防火牆設定
3. 確認伺服器端口（POP3: 1100, SMTP: 2500）

### Q2: 可以收信但無法發信

**可能原因：**
1. SMTP 驗證設定錯誤
2. TLS 設定問題
3. 發信權限限制

**解決方法：**
1. 確認 SMTP_USE_AUTH=true
2. 確認 SMTP_USE_TLS=true
3. 聯絡 IT 部門確認發信權限

### Q3: 中文郵件顯示亂碼

**解決方法：**
腳本已內建多種中文編碼支援（UTF-8, Big5, GB2312），應能正確顯示中文郵件。

### Q4: 想要自動監控新郵件

**實作建議：**
```python
import time
from test_pop3_company import CompanyEmailTester

tester = CompanyEmailTester()

while True:
    # 檢查新郵件
    mail, num_messages = tester.test_pop3_connection(email, password)
    if mail and num_messages > 0:
        tester.read_emails(mail, num_messages)
        mail.quit()
    
    # 等待下次檢查
    time.sleep(60)  # 每分鐘檢查一次
```

## 安全性建議

### 1. 密碼保護
- 不要將 `.env` 檔案提交到版本控制系統
- 使用強密碼
- 定期更換密碼

### 2. 網路安全
- 確保使用 SSL/TLS 連接
- 在安全的網路環境中使用
- 注意公司網路政策

### 3. 存取控制
- 限制腳本執行權限
- 定期檢查郵件存取日誌
- 遵守公司資訊安全政策

## 進階功能

### 1. 批次處理

可以修改腳本來處理多個帳號：

```python
accounts = [
    {'email': '<EMAIL>', 'password': 'pass1'},
    {'email': '<EMAIL>', 'password': 'pass2'},
]

for account in accounts:
    # 處理每個帳號的郵件
    pass
```

### 2. 郵件過濾

根據主旨或寄件者過濾郵件：

```python
def filter_emails(emails, keywords):
    filtered = []
    for email in emails:
        if any(keyword in email.subject.lower() for keyword in keywords):
            filtered.append(email)
    return filtered
```

### 3. 附件下載

擴展腳本以支援附件下載：

```python
def download_attachments(msg, download_dir):
    for part in msg.walk():
        if part.get_content_disposition() == 'attachment':
            filename = part.get_filename()
            if filename:
                filepath = os.path.join(download_dir, filename)
                with open(filepath, 'wb') as f:
                    f.write(part.get_payload(decode=True))
```

## 支援

如果遇到問題，請檢查：
1. 網路連接是否正常
2. 公司郵件伺服器是否正常運作
3. 帳號密碼是否正確
4. 防火牆設定是否正確

如需進一步協助，請聯絡 IT 支援部門。

---

*最後更新: 2025-07-09*